name: sort_pro_printer_flutter
description: A new Flutter project.

publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.0.1 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
    # Provide internationalization
  intl: any
  shared_preferences: ^2.1.2
  # to use google fonts
  google_fonts: ^4.0.4
  flutter_bloc: ^8.1.3
  equatable: ^2.0.5
  flutter_svg: ^2.0.5
  # ocr
  permission_handler: ^10.3.0
  dio: ^5.2.1+1
  flutter_image_compress: ^2.0.3
  path_provider: ^2.0.15
  path: ^1.8.3
  # Packages to support data sync
  ftpconnect: ^2.0.5
  flutter_isolate: ^2.0.4
  async: ^2.11.0
  timeago: ^3.5.0
  material_symbols_icons: ^4.2663.0
  uuid: ^3.0.7
  json_annotation: ^4.8.1
  android_id: ^0.3.3
  package_info_plus: ^4.1.0
  convert: ^3.1.1
  connectivity_plus: ^4.0.2
  sqlite3: ^2.4.4
  sqlite3_flutter_libs: ^0.5.24
  azblob: ^2.5.0
  mime: ^1.0.4
  appcenter: 
    path: ./packages/appcenter
  flutter_pdfview: ^1.3.1
  video_player: ^2.7.0
  chewie: ^1.7.0
  audioplayers: ^5.2.0
  vibration: ^1.8.2
  azure_application_insights: ^4.0.0
  http: ^1.1.0
  collection: ^1.18.0
  camera: ^0.10.5+5
  google_mlkit_barcode_scanning: ^0.6.0
  image: ^4.1.3
  hydrated_bloc: 9.1.2 # unsure why atm but version 9.1.3 seems to break the LookupCubit's hydration. Will investigate but for now we're stuck with this version
  flutter_keyboard_visibility: ^5.4.1
  webview_flutter: ^4.4.4
  csv: ^6.0.0
  workmanager: ^0.5.2


dependency_overrides:
  # Unfortunately these packages are forcing the Java version to be 17 in the latest releases
  # Pipeline currently uses version 11, so we'll pin down these two
  # More info https://pub.dev/packages/device_info_plus/changelog#1001
  device_info_plus: 9.1.2
  # More info https://pub.dev/packages/wakelock_plus/changelog#120
  chewie: 1.7.0
  wakelock_plus: 1.1.4

dev_dependencies:
  flutter_test:
    sdk: flutter
  # Helps quickly change the app's package name
  change_app_package_name: ^1.1.0
  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^2.0.0
  test: ^1.24.1
  mockito: ^5.4.2
  flutter_launcher_icons: "^0.13.1"
  build_runner: ^2.4.6
  json_serializable: ^6.7.1
flutter_icons:
  image_path: "assets/app_icon.png"
  android: true

flutter:
  uses-material-design: true
  generate: true

  assets:
   - assets/
   - assets/whack-a-mole/
   - assets/help/
   - assets/sounds/
   - assets/camera/

