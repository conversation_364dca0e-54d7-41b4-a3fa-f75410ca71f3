# This is a generated file; do not edit or check into version control.
android_id=/Users/<USER>/.pub-cache/hosted/pub.dev/android_id-0.3.6/
appcenter=/Users/<USER>/Downloads/sortproprinter_flutter/packages/appcenter/
audioplayers=/Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers-5.2.1/
audioplayers_android=/Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_android-4.0.3/
audioplayers_darwin=/Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_darwin-5.0.2/
audioplayers_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_linux-3.1.0/
audioplayers_web=/Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_web-4.1.0/
audioplayers_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_windows-3.1.0/
camera=/Users/<USER>/.pub-cache/hosted/pub.dev/camera-0.10.6/
camera_android=/Users/<USER>/.pub-cache/hosted/pub.dev/camera_android-0.10.9+11/
camera_avfoundation=/Users/<USER>/.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.19/
camera_web=/Users/<USER>/.pub-cache/hosted/pub.dev/camera_web-0.3.5/
connectivity_plus=/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-4.0.2/
device_info_plus=/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-9.1.2/
flutter_image_compress=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_image_compress-2.4.0/
flutter_image_compress_common=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/
flutter_image_compress_macos=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_image_compress_macos-1.0.3/
flutter_image_compress_ohos=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_image_compress_ohos-0.0.3/
flutter_image_compress_web=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_image_compress_web-0.1.5/
flutter_isolate=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_isolate-2.1.0/
flutter_keyboard_visibility=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_keyboard_visibility-5.4.1/
flutter_keyboard_visibility_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_keyboard_visibility_linux-1.0.0/
flutter_keyboard_visibility_macos=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_keyboard_visibility_macos-1.0.0/
flutter_keyboard_visibility_web=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_keyboard_visibility_web-2.0.0/
flutter_keyboard_visibility_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_keyboard_visibility_windows-1.0.0/
flutter_pdfview=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_pdfview-1.4.1+1/
flutter_plugin_android_lifecycle=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.22/
google_mlkit_barcode_scanning=/Users/<USER>/.pub-cache/hosted/pub.dev/google_mlkit_barcode_scanning-0.6.0/
google_mlkit_commons=/Users/<USER>/.pub-cache/hosted/pub.dev/google_mlkit_commons-0.3.0/
package_info_plus=/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-4.2.0/
path_provider=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/
path_provider_android=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.10/
path_provider_foundation=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/
path_provider_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/
path_provider_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/
permission_handler=/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler-10.4.5/
permission_handler_android=/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-10.3.6/
permission_handler_apple=/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.1.4/
permission_handler_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.1.3/
shared_preferences=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.3.3/
shared_preferences_android=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.3.2/
shared_preferences_foundation=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/
shared_preferences_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/
shared_preferences_web=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/
shared_preferences_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/
sqlite3_flutter_libs=/Users/<USER>/.pub-cache/hosted/pub.dev/sqlite3_flutter_libs-0.5.34/
vibration=/Users/<USER>/.pub-cache/hosted/pub.dev/vibration-1.9.0/
video_player=/Users/<USER>/.pub-cache/hosted/pub.dev/video_player-2.9.5/
video_player_android=/Users/<USER>/.pub-cache/hosted/pub.dev/video_player_android-2.7.1/
video_player_avfoundation=/Users/<USER>/.pub-cache/hosted/pub.dev/video_player_avfoundation-2.7.1/
video_player_web=/Users/<USER>/.pub-cache/hosted/pub.dev/video_player_web-2.3.5/
wakelock_plus=/Users/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus-1.1.4/
webview_flutter=/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter-4.8.0/
webview_flutter_android=/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_android-3.16.8/
webview_flutter_wkwebview=/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.14.0/
workmanager=/Users/<USER>/.pub-cache/hosted/pub.dev/workmanager-0.5.2/
