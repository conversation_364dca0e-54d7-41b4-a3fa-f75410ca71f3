import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:hydrated_bloc/hydrated_bloc.dart';
import 'package:path_provider/path_provider.dart';
import 'package:sort_pro_printer_flutter/services/logs/i_log_service.dart';
import 'package:sort_pro_printer_flutter/services/logs/log_service.dart';
import 'package:workmanager/workmanager.dart';
import 'app/app.dart';
import 'services/scan_logs/scanlog_db_repository.dart';

late final ILogService _logService;

/// Dispatcher for WorkManager tasks
/// Currently only used to run a periodic 1 hour task to export the scanLog db
@pragma('vm:entry-point')
void callbackDispatcher() {
  Workmanager().executeTask((task, inputData) async {
    if(task == "exportScanLogDb") {
      try {
        await ScanLogDbRepository.exportScanLogs();
      } catch(e, s) {
        LogService.instance.error(e, s, false);
      }
    }
    return Future.value(true);
  });
}

void main() {
  if (kReleaseMode) {
    runWithCrashReporting(codeToExecute: run);
  } else {
    run();
  }
}

Future<void> run() async {
  // we need to ensure we have WidgetsFlutterBinding initialized
  WidgetsFlutterBinding.ensureInitialized();

  Workmanager().initialize(
      callbackDispatcher, // The top level function, aka callbackDispatcher
      isInDebugMode: !kReleaseMode // If enabled it will post a notification whenever the task is running. Handy for debugging tasks
  );
  // Periodic task registration
  Workmanager().registerPeriodicTask(
    "export-scanlog-db",
    "exportScanLogDb",
    frequency: const Duration(hours: 1),
    existingWorkPolicy: ExistingWorkPolicy.replace
  );

  HydratedBloc.storage = await HydratedStorage.build(storageDirectory: await getTemporaryDirectory());
  _logService = LogService.instance;
  runApp(SortProPrinter(
    logService: _logService,
  ));
}

Future<void> runWithCrashReporting({
  required VoidCallback codeToExecute,
}) async {
  // Hook into Flutter error handling.
  FlutterError.onError = (details) => submitErrorAsTelemetry(
        isFatal: true,
        error: details.exception,
        trace: details.stack ?? StackTrace.empty,
      );

  // Run the code to execute in a zone and handle all errors within.
  runZonedGuarded(
    codeToExecute,
    (error, trace) => submitErrorAsTelemetry(
      isFatal: true,
      error: error,
      trace: trace,
    ),
  );
}

Future<void> submitErrorAsTelemetry({
  required bool isFatal,
  required Object error,
  required StackTrace trace,
}) async {
  debugPrint('reporting ${isFatal ? 'fatal' : 'non-fatal'} error: $error');
  debugPrint('$trace');

  _logService.error(
    error,
    trace,
    isFatal,
  );

  try {
    if (isFatal) {
      await _logService.flush();
    }
  } on Object catch (e, t) {
    // We print synchronously here to ensure the output is written in the case we force exit.
    debugPrintSynchronously('Sending error telemetry failed: $e\r\n$t');
    debugPrintSynchronously('Original error: $error');
  } finally {
    if (isFatal && kReleaseMode) {
      debugPrintSynchronously('Forcing exit');
    }
  }
}
