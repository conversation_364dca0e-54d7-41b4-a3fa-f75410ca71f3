class EnvironmentConfig {
  static String get env => const String.fromEnvironment('ENV');
  static String get ftpSsHost => const String.fromEnvironment('FTP_SS_HOST');
  static String get ftpSsPort => const String.fromEnvironment('FTP_SS_PORT');
  static String get ftpSsUser => const String.fromEnvironment('FTP_SS_USER');
  static String get ftpSsPass => const String.fromEnvironment('FTP_SS_PASS');
  static String get sslwsUrl => const String.fromEnvironment('SSLWS_URL');
  static String get sslwsDomain => const String.fromEnvironment('SSLWS_DOMAIN');
  static String get sslwsUser => const String.fromEnvironment('SSLWS_USER');
  static String get sslwsPassword => const String.fromEnvironment('SSLWS_PASS');
  static String get azureCognitiveServicesApiKey => const String.fromEnvironment('ACS_API_KEY');
  static String get azureCognitiveServicesApiUrl => const String.fromEnvironment('ACS_API_URL');
  static String get azureCognitiveServicesPuroLabelModelId => const String.fromEnvironment('ACS_PURO_LABEL_MODEL_ID');
  static String get azureStorageAccountConnectionString => const String.fromEnvironment('ASA_CONN_STRING');
  static String get azureStorageAccountContainerName => const String.fromEnvironment('ASA_CONTAINER_NAME');
  static String get appCenterKey => const String.fromEnvironment('APP_CENTER_KEY');
  static String get appInsightsInstrumentationKey => const String.fromEnvironment('APP_INSIGHT_INSTRUMENTATION_KEY');
  static int get sessionExpireTimeInSeconds => int.parse(const String.fromEnvironment('SESSION_EXPIRE_TIME_SECONDS'));
  static int get activeSessionTimeoutInSeconds => int.parse(const String.fromEnvironment('ACTIVE_SESSION_TIMEOUT_SECONDS'));
  static String get feedbackFormLink => const String.fromEnvironment('FEEDBACK_FORM_LINK');
  static String get logLevel => const String.fromEnvironment('LOG_LEVEL');
}
