import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:sort_pro_printer_flutter/services/logs/i_log_service.dart';
import 'package:sort_pro_printer_flutter/services/sslws/sslws_service.dart';

class LoggingInterceptor extends Interceptor {
  final ILogService logService;

  Stopwatch stopwatch = Stopwatch();

  LoggingInterceptor(this.logService);

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    stopwatch.start();
    super.onRequest(options, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    stopwatch.stop();
    final Map<String, Object> additionalProperties = {};
    if(err.requestOptions.data != null) {
      additionalProperties["requestData"] = jsonEncode(err.requestOptions.data);
    }
    if(err.response?.data != null) {
      additionalProperties["responseData"] = jsonEncode(err.response?.data);
    }
    logService.request(
        duration: stopwatch.elapsed,
        responseCode: err.response?.statusCode ?? -1,
        success: false,
        url: err.requestOptions.path,
        additionalProperties: additionalProperties
    );
    if(!err.requestOptions.path.endsWith(SslwsService.healthServiceRequestEndpoint)){
      logService.error(err, err.stackTrace, false);
    }
    stopwatch.reset();
    super.onError(err, handler);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    stopwatch.stop();
    final statusCode = response.statusCode ?? -1;
    final success = (statusCode ~/ 100) == 2;
    final Map<String, Object> additionalProperties = {};
    if(response.requestOptions.data != null) {
      additionalProperties["requestData"] = jsonEncode(response.requestOptions.data);
    }
    if(response.data != null) {
      additionalProperties["responseData"] = jsonEncode(response.data);
    }
    logService.request(
      duration: stopwatch.elapsed,
      responseCode: statusCode,
      success: success,
      url: response.requestOptions.path,
      additionalProperties: additionalProperties,
    );
    stopwatch.reset();
    super.onResponse(response, handler);
  }

}
