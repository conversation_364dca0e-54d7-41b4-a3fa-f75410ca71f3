import 'dart:core';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:ftpconnect/ftpconnect.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'package:sort_pro_printer_flutter/services/logs/i_log_service.dart';

class SmartSortFtpClient {
  final String host;
  final int port;
  final String user;
  final String pass;
  final ILogService? logService;

  late final FTPConnect ftpClient;

  final String _dbFilesFolder = "/SSMA_DbFiles";
  static const List<String> fileIdentifiers = [
    "_PIN",
    "_PREPRINT",
    "_RPM",
    "_ParkingPlan",
    "_HFPULocationMaster",
    "_HFPULocToPC",
  ];

  /// A suffix added to use this DB while syncing
  static const whileSyncingSuffix = "_SYNCING";

  SmartSortFtpClient({
    required this.host,
    this.port = 21,
    required this.user,
    required this.pass,
    this.logService,
  }) : ftpClient = FTPConnect(
          host,
          port: port,
          user: user,
          pass: pass,
          securityType: SecurityType.FTP,
          showLog: kDebugMode,
        )..listCommand = ListCommand.LIST; // MLSD is used by default and the SS server doesn't seem to support it

  Future<bool> connect() async {
    logService?.trace(LogLevel.verbose, "Connecting to FTP Server...");
    final connected = await ftpClient.connect();
    if (connected) {
      logService?.trace(LogLevel.verbose, "Connected to FTP Server.");
    } else {
      logService?.trace(LogLevel.verbose, "Could not connect to FTP Server.");
    }
    return connected;
  }

  Future<List<String>> getNewestDbFilesForTerminal(String terminalId) async {
    final List<String> filepaths = [];
    logService?.trace(LogLevel.verbose, "Started syncing db files for terminal $terminalId");
    final String terminalDir = _getDbTerminalFolder(terminalId);
    await ftpClient.changeDirectory(terminalDir); // switch directory to the terminal's db files dir
    final dirContent = await ftpClient.listDirectoryContent();
    for (String fileIdentifier in fileIdentifiers) {
      final fileList = dirContent.where((element) => element.name.contains(fileIdentifier)).toList();
      if (fileList.isNotEmpty) {
        // sort files alphabetically
        fileList.sort((a, b) => a.name.compareTo(b.name));
        // Grab newest
        filepaths.add(fileList.last.name);
      }
    }
    return filepaths;
  }

  Future<List<String>> downloadNewestDbFilesForTerminal(List<String> currentFiles, String terminalId) async {
    final List<String> downloadedFilepaths = [];
    logService?.trace(LogLevel.verbose, "Started syncing db files for terminal $terminalId");
    logService?.trace(LogLevel.verbose, "current files: $currentFiles");
    final String terminalDir = _getDbTerminalFolder(terminalId);
    await ftpClient.changeDirectory(terminalDir); // switch directory to the terminal's db files dir
    final dirContent = await ftpClient.listDirectoryContent();

    // Local directory to save into
    final dirPath = "${(await getTemporaryDirectory()).path}/dbs";
    final directory = Directory(dirPath);
    if (!directory.existsSync()) {
      directory.createSync();
    }
    // Go through all the file identifiers and get the latest file version
    for (String fileIdentifier in fileIdentifiers) {
      final fileList = dirContent.where((element) => element.name.contains(fileIdentifier)).toList();
      if (fileList.isNotEmpty) {
        // sort files alphabetically
        fileList.sort((a, b) => a.name.compareTo(b.name));
        // Grab newest
        final newestPinFile = fileList.last;
        // Join the filename with the path to the databases
        final filepath = join(dirPath, newestPinFile.name);
        final dbFile = File(filepath);
        if(currentFiles.contains(newestPinFile.name)) {
          logService?.trace(LogLevel.verbose, "No need to download a new $fileIdentifier file");
          logService?.event("No new FTP File", additionalProperties: {"Terminal": terminalId, "Filename": newestPinFile.name, "Identifier": fileIdentifier});
          downloadedFilepaths.add(dbFile.path);
        } else {
          logService?.trace(LogLevel.verbose, "Downloading ${newestPinFile.name}...");
          // download the file
          await ftpClient.downloadFileWithRetry(newestPinFile.name, dbFile);
          downloadedFilepaths.add(dbFile.path);
          logService?.trace(LogLevel.verbose, "File downloaded in $filepath");
          logService?.event("FTP File downloaded", additionalProperties: {"Terminal": terminalId, "Filename": newestPinFile.name, "Identifier": fileIdentifier});
        }
      }
    }
    logService?.event("FTP Files sync success", additionalProperties: {"Terminal": terminalId});
    return downloadedFilepaths;
  }

  String _getDbTerminalFolder(String terminalId) {
    return "$_dbFilesFolder/$terminalId";
  }

  Future<bool> isFtpServerReachable() async {
    var connected = false;
    try {
      connected = await connect();
      disconnect();
    } catch (_) {}
    return connected;
  }

  Future<void> disconnect() async {
    logService?.trace(LogLevel.verbose, "Disconnecting from FTP Server...");
    await ftpClient.disconnect();
    logService?.trace(LogLevel.verbose, "Disconnected from FTP Server.");
  }
}
