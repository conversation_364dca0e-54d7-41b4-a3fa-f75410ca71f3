import 'dart:io';

import 'package:package_info_plus/package_info_plus.dart';
import 'package:path/path.dart';
import 'package:sort_pro_printer_flutter/network/smart_sort_ftp_client.dart';
import 'package:sqlite3/sqlite3.dart';

/// Manages access to all the databases related to the SmartSort FTP Server
class SswsDatabaseManager {
  static const _pinDbIdentifier = "_PIN";
  static const _prePrintDbIdentifier = "_PREPRINT";
  static const _rpmDbIdentifier = "_RPM";
  static const _parkingPlanIdentifier = "_ParkingPlan";
  static const _hFPULocationMasterIdentifier = "_HFPULocationMaster";
  static const _hFPULocToPCIdentifier = "_HFPULocToPC";

  static const List<String> fileIdentifiers = [
    _pinDbIdentifier,
    _prePrintDbIdentifier,
    _rpmDbIdentifier,
    _parkingPlanIdentifier,
    _hFPULocationMasterIdentifier,
    _hFPULocToPCIdentifier,
  ];

  Future<Database> getPinDatabase(String terminalId) async {
    return sqlite3.open(await _getDbPath(terminalId, _pinDbIdentifier));
  }

  Future<Database> getPrePrintDatabase(String terminalId) async {
    return sqlite3.open(await _getDbPath(terminalId, _prePrintDbIdentifier));
  }

  Future<Database> getRpmDatabase(String terminalId) async {
    return sqlite3.open(await _getDbPath(terminalId, _rpmDbIdentifier));
  }

  Future<Database> getParkingPlanDatabase(String terminalId) async {
    return sqlite3.open(await _getDbPath(terminalId, _parkingPlanIdentifier));
  }

  Future<String> getParkingPlanDatabasePath(String terminalId) async {
    return await _getDbPath(terminalId, _parkingPlanIdentifier);
  }

  Future<Database> getHFPULocationMasterDatabase(String terminalId) async {
    return sqlite3.open(await _getDbPath(terminalId, _hFPULocationMasterIdentifier));
  }

  Future<Database> getHFPULocToPCDatabase(String terminalId) async {
    return sqlite3.open(await _getDbPath(terminalId, _hFPULocToPCIdentifier));
  }

  Future<String> _getDbPath(String terminalId, String dbIdentifier) async {
    final currentDir = await getCurrentDatabasesDirectory();
    return currentDir.listSync().firstWhere((element) => basename(element.path).contains(dbIdentifier)).path;
  }

  Future<void> saveDbFiles(List<String> downloadedFilePaths) async {
    final currentDir = await getCurrentDatabasesDirectory();
    final prevDir = await getPreviousDatabasesDirectory();

    final currentFiles = currentDir.listSync().map((e) => basename(e.path)).toList();
    final downloadedFileNames = downloadedFilePaths.map((e) => basename(e)).toList();

    /// Delete the files from the previous folder
    await deleteFilesFromDir(prevDir);

    List<String> filesToDelete = [];

    // Move current files to the 'previous' folder
    for (FileSystemEntity file in currentDir.listSync()) {
      final filename = basename(file.path); // extract the name from the path
      if (file is File && !downloadedFileNames.contains(filename)) {
        await file.copy(join(prevDir.path, filename)); // Copy the 'current' file into the 'previous' folder
        filesToDelete.add(file.path);
      }
    }

    // Save the new files into the 'current' folder
    for (String filepath in downloadedFilePaths) {
      final filename = basename(filepath); // extract the name from the path
      if(!currentFiles.contains(filename)) {
        await File(filepath).rename(join(currentDir.path, filename)); // Save the new files in the 'current' directory
      }
    }

    // Delete the old files from the 'current' folder
    for (String filepath in filesToDelete) {
      File(filepath).deleteSync();
    }
  }

  Future<List<String>> getCurrentDbFiles() async {
    final directory = await getCurrentDatabasesDirectory();
    final files = directory.listSync();
    return files.map((e) => basename(e.path)).toList();
  }

  /// Delete all the db files
  deleteFilesFromDir(Directory dir) async {
    final dir = await getPreviousDatabasesDirectory();
    for (FileSystemEntity file in dir.listSync()) {
      await file.delete();
    }
  }

  Future<bool> databasesExist(String terminalId) async {
    final currentDir = await getCurrentDatabasesDirectory();
    final dirFiles = currentDir.listSync();
    if (dirFiles.where((element) => basename(element.path).startsWith(terminalId)).isEmpty) return false;
    for (String fileIdentifier in SmartSortFtpClient.fileIdentifiers) {
      if (dirFiles.where((element) => basename(element.path).contains(fileIdentifier)).isEmpty) return false;
    }
    return true;
  }

  Future<Directory> getCurrentDatabasesDirectory() async {
    final dbPath = await getDatabasesPath();
    final path = "$dbPath/current-ftp-files";
    return _getOrCreateDirectory(path);
  }

  Future<Directory> getPreviousDatabasesDirectory() async {
    final dbPath = await getDatabasesPath();
    final path = "$dbPath/previous-ftp-files";
    return _getOrCreateDirectory(path);
  }

  Directory _getOrCreateDirectory(String path) {
    final dir = Directory(path);
    if (!dir.existsSync()) {
      dir.createSync();
    }
    return dir;
  }

  static Future<String> getDatabasesPath() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    return "data/data/${packageInfo.packageName}/databases";
  }
}
