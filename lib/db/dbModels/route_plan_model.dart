class RoutePlan {
  final String terminalId;
  final String routePlanId;
  final String routePlanVersionId;
  final String addressRecordType;
  final String postalCode;
  final String streetName;
  final String streetType;
  final String streetDirection;
  final String fromStreetNumber;
  final String toStreetNumber;
  final String routeNumber;
  final String shelfNumber;
  final String truckShelfOverride;
  final String deliverySequenceId;
  final String provinceCode;
  final String municipalityName;

  const RoutePlan({
    required this.terminalId,
    required this.routePlanId,
    required this.routePlanVersionId,
    required this.addressRecordType,
    required this.postalCode,
    required this.streetName,
    required this.streetType,
    required this.streetDirection,
    required this.fromStreetNumber,
    required this.toStreetNumber,
    required this.routeNumber,
    required this.shelfNumber,
    required this.truckShelfOverride,
    required this.deliverySequenceId,
    required this.provinceCode,
    required this.municipalityName,
  });
}
