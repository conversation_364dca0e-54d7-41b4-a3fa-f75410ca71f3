class PIN {
  final String terminalId;
  final String pIN;
  final String? primarySort;
  final String? sideOfBelt;
  final String routeNumber;
  final String? shelfNumber;
  final String? truckShelfOverride;
  final String? deliverySequenceId;

  const PIN({
    required this.terminalId,
    required this.pIN,
    this.primarySort,
    this.sideOfBelt,
    required this.routeNumber,
    this.shelfNumber,
    this.truckShelfOverride,
    this.deliverySequenceId,
  });
}
