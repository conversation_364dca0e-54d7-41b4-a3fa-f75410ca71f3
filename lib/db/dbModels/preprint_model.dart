class PrePrint {
  final String terminalId;
  final String prePrintId;
  final int fromPIN;
  final int toPIN;
  final String primarySort;
  final String sideOfBelt;
  final String routeNumber;
  final String shelfNumber;
  final String? truckShelfOverride;
  final String deliverySequenceId;

  const PrePrint({
    required this.terminalId,
    required this.prePrintId,
    required this.fromPIN,
    required this.toPIN,
    required this.primarySort,
    required this.sideOfBelt,
    required this.routeNumber,
    required this.shelfNumber,
    this.truckShelfOverride,
    required this.deliverySequenceId,
  });
}
