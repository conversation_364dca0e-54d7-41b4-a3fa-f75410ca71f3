import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:sort_pro_printer_flutter/services/disposable.dart';

enum NetworkStatus {
  // the device is successfully connected
  connected,
  // the device is connected, but it can't access the smart sort server
  noSmartSortAccess,
  // the device doesn't have connection
  disconnected,
}

abstract class INetworkService implements Disposable {
  StreamSubscription<NetworkStatus> subscribeToNetworkChanges(ValueSetter<NetworkStatus> onNetworkStatusChanged);
  Future<NetworkStatus> checkConnection();
}