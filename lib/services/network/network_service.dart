import 'dart:async';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:sort_pro_printer_flutter/environment/environment_config.dart';
import 'package:sort_pro_printer_flutter/services/logs/log_service.dart';
import 'package:sort_pro_printer_flutter/services/network/i_network_service.dart';
import 'package:sort_pro_printer_flutter/services/scan_logs/scan_logs_server_client.dart';

/// This class leverages the connectivity_plus to give results of the network connectivity (wifi, network, no network)
/// however, since this app relies heavily on having ftp access, we'll also leverage the FTP connection
class NetworkService implements INetworkService {
  final Connectivity _connectivity = Connectivity();
  final ScanLogsServerClient scanLogServerClient = ScanLogsServerClient(
  EnvironmentConfig.sslwsDomain,
  EnvironmentConfig.sslwsUrl,
  EnvironmentConfig.sslwsUser,
  EnvironmentConfig.sslwsPassword,
  );

  // This subscribes to the type of connection (wifi, mobile, vpn.. etc)
  StreamSubscription<ConnectivityResult>? _connectivitySubscription;

  // A StreamController to control internal stream for NetworkStatus updates
  final StreamController<NetworkStatus> _networkStatusController = StreamController.broadcast();

  // We combine results from _connectivitySubscription and _networkSubscription
  // and abstract it to a NetworkStatus
  Stream<NetworkStatus> get _networkStatusStream => _networkStatusController.stream;

  // Keeps track of the current status
  NetworkStatus? _currentNetworkStatus;

  NetworkService();

  initialize() async {
    _initializeSubscriptions();
  }

  _initializeSubscriptions() async {
    _connectivitySubscription = _connectivity.onConnectivityChanged.listen((ConnectivityResult connectivityResult) async {
      LogService.instance.event("Network Connection Changed", additionalProperties: {"ConnectivityResult": connectivityResult.name});
      _setCurrentNetworkStatus(connectivityResult != ConnectivityResult.none ? NetworkStatus.connected : NetworkStatus.disconnected);
    });
  }

  @override
  StreamSubscription<NetworkStatus> subscribeToNetworkChanges(ValueSetter<NetworkStatus> onNetworkStatusChanged) =>
      _networkStatusStream.listen(onNetworkStatusChanged);

  @override
  Future<NetworkStatus> checkConnection() async {
    NetworkStatus newStatus = NetworkStatus.disconnected;

    final connectivityResult = await _connectivity.checkConnectivity();
    newStatus = connectivityResult != ConnectivityResult.none ? NetworkStatus.connected : NetworkStatus.disconnected;
    _setCurrentNetworkStatus(newStatus);
    return newStatus;
  }

  _setCurrentNetworkStatus(
    NetworkStatus networkStatus, {
    bool notify = true,
  }) {
    if (_currentNetworkStatus == networkStatus) return;
    _currentNetworkStatus = networkStatus;

    if (!notify) return;
    _networkStatusController.add(networkStatus);
  }

  @override
  Future<void> dispose() async {
    _connectivitySubscription?.cancel();
    _networkStatusController.close();
  }
}
