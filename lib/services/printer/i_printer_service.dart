import 'dart:async';
import 'package:sort_pro_printer_flutter/app/pin_lookup/data/models/lookup_result.dart';
import 'package:sort_pro_printer_flutter/services/printer/printer_info.dart';

abstract class IPrinterService {
  final StreamController<PrinterInfo> printerStatusStreamController = StreamController.broadcast();
  Stream<PrinterInfo> get printerInfo => printerStatusStreamController.stream;

  Future<void> connect(String macAddress);

  Future<void> disconnect();

  Future<bool> isPrinterConnected();

  Future<String?> getPairedPrinterMacAddress();

  Future<void> printLookupLabel(LookupResult? result, String languageCode);
}
