import 'dart:async';
import 'dart:convert';

import 'package:flutter/services.dart';
import 'package:sort_pro_printer_flutter/app/pin_lookup/data/models/lookup_result.dart';
import 'package:sort_pro_printer_flutter/services/logs/i_log_service.dart';
import 'package:sort_pro_printer_flutter/services/printer/printer_exception.dart';
import 'package:sort_pro_printer_flutter/services/printer/i_printer_service.dart';
import 'package:sort_pro_printer_flutter/services/printer/printer_info.dart';

class ZebraPrinterService extends IPrinterService {
  // Method names used in the Android MethodChannel to communicate with the printer
  static const String _connectPrinterMethod = "connectPrinter";
  static const String _getPairedPrinterMacAddress = "getPairedPrinterMacAddress";
  static const String _isPrinterConnectedMethod = "isPrinterConnected";
  static const String _disconnectPrinterMethod = "disconnectPrinter";
  static const String _printLabelMethod = "printLabel";

  // MethodChannel to communicate with the Android Platform
  static const MethodChannel _methodChannel = MethodChannel('com.pdl.sortproprinter/printer');
  static const EventChannel _scanChannel = EventChannel('com.pdl.sortproprinter/printer/status');
  StreamSubscription? _channelSubscription;

  final ILogService _logService;

  ZebraPrinterService(this._logService);

  void _onPrinterInfoReceived(event) {
    final jsonData = jsonDecode(event);
    printerStatusStreamController.add(PrinterInfo.fromJson(jsonData));
  }

  void _onPrinterInfoException(Object error, StackTrace stackTrace) {
    _logService.error(error, stackTrace, false);
  }

  @override
  Future<void> connect(macAddress) async {
    _channelSubscription = _scanChannel.receiveBroadcastStream().listen(_onPrinterInfoReceived, onError: _onPrinterInfoException);

    try {
      await _methodChannel.invokeMethod(_connectPrinterMethod, macAddress);
    } on PlatformException catch (e, s) {
      _logService.error(e, s, false);
      throw PrinterException(e.code, e.message ?? "Could not connect to the printer");
    }
  }

  @override
  Future<void> disconnect() async {
    // calling this first will make us stop listening for printer status/info before actually getting the status after we disconnect
    // this is what we want since we want to change the Printer disconnected alert from an error to a success instead
    _channelSubscription?.cancel();
    _channelSubscription = null;

    await _methodChannel.invokeMethod(_disconnectPrinterMethod);
  }

  @override
  Future<bool> isPrinterConnected() async {
    return await _methodChannel.invokeMethod<bool>(_isPrinterConnectedMethod) ?? false;
  }

  @override
  Future<void> printLookupLabel(LookupResult? result, String languageCode) async {
    try {
      await _methodChannel.invokeMethod(_printLabelMethod, jsonEncode(result?.toLabelInfo(languageCode)));
    } on PlatformException catch (e, s) {
      _logService.error(e, s, false);
      throw PrinterException(e.code, e.message ?? "Label not printed - Could not communicate with printer");
    }
  }

  @override
  Future<String?> getPairedPrinterMacAddress() async {
    return await _methodChannel.invokeMethod<String?>(_getPairedPrinterMacAddress);
  }
}
