import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'printer_info.g.dart';


@JsonSerializable(createToJson: false)
class PrinterInfo extends Equatable {
  final bool isPartialFormatInProgress;
  final bool isConnected;
  final bool isReadyToPrint;
  final bool isHeadCold;
  final bool isHeadOpen;
  final bool isHeadTooHot;
  final bool isPaperOut;
  final bool isRibbonOut;
  final bool isReceiveBufferFull;
  final bool isPaused;

  const PrinterInfo({
    this.isPartialFormatInProgress = false,
    this.isConnected = false,
    this.isReadyToPrint = false,
    this.isHeadCold = false,
    this.isHeadOpen = false,
    this.isHeadTooHot = false,
    this.isPaperOut = false,
    this.isRibbonOut = false,
    this.isReceiveBufferFull = false,
    this.isPaused = false,
  });

  factory PrinterInfo.fromJson(Map<String, dynamic> json) => _$PrinterInfoFromJson(json);

  @override
  List<Object> get props => [
        isPartialFormatInProgress,
        isConnected,
        isReadyToPrint,
        isHeadCold,
        isHeadOpen,
        isHeadTooHot,
        isPaperOut,
        isRibbonOut,
        isReceiveBufferFull,
        isPaused,
      ];
}
