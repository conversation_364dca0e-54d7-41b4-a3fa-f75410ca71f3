// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'printer_info.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PrinterInfo _$PrinterInfoFromJson(Map<String, dynamic> json) => PrinterInfo(
      isPartialFormatInProgress:
          json['isPartialFormatInProgress'] as bool? ?? false,
      isConnected: json['isConnected'] as bool? ?? false,
      isReadyToPrint: json['isReadyToPrint'] as bool? ?? false,
      isHeadCold: json['isHeadCold'] as bool? ?? false,
      isHeadOpen: json['isHeadOpen'] as bool? ?? false,
      isHeadTooHot: json['isHeadTooHot'] as bool? ?? false,
      isPaperOut: json['isPaperOut'] as bool? ?? false,
      isRibbonOut: json['isRibbonOut'] as bool? ?? false,
      isReceiveBufferFull: json['isReceiveBufferFull'] as bool? ?? false,
      isPaused: json['isPaused'] as bool? ?? false,
    );
