import 'dart:async';

import 'package:flutter/services.dart';

class ZebraPrinterInfoChannel {
  // EventChannels to listen to events from Android
  static const EventChannel _scanChannel = EventChannel('com.pdl.sortproprinter/printer/info');

  StreamSubscription? _channelSubscription;

  ZebraPrinterInfoChannel() {
    _initialize();
  }

  _initialize() {
    _channelSubscription = _scanChannel.receiveBroadcastStream().listen(
          _onPrinterInfoReceived,
        );
  }

  final StreamController<String> _printerMacAddressStreamController = StreamController.broadcast();

  Stream<String> get printerMacAddress => _printerMacAddressStreamController.stream;

  void _onPrinterInfoReceived(event) {
    _printerMacAddressStreamController.add(event);
  }

  dispose() {
    _printerMacAddressStreamController.close();
    _channelSubscription?.cancel();
  }

}
