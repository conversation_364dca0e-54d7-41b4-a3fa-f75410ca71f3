import 'package:sort_pro_printer_flutter/services/vibration/i_vibration_service.dart';
import 'package:vibration/vibration.dart';

class VibrationService extends IVibrationService {
  @override
  Future<void> vibrate({required VibrationIntensity intensity}) async {
    if (!(await Vibration.hasVibrator() ?? false)) return;

    /// Amplitude is a range from 1 to 255, if supported.
    switch (intensity) {
      case VibrationIntensity.low:
        await Vibration.vibrate(amplitude: 85);
      case VibrationIntensity.medium:
        await Vibration.vibrate(amplitude: 170, duration: 250);
        await Future.delayed(const Duration(milliseconds: 500));
        await Vibration.vibrate(amplitude: 170, duration: 250);
      case VibrationIntensity.high:
        await Vibration.vibrate(amplitude: 255, duration: 250);
        await Future.delayed(const Duration(milliseconds: 500));
        await Vibration.vibrate(amplitude: 255, duration: 250);
        await Future.delayed(const Duration(milliseconds: 500));
        await Vibration.vibrate(amplitude: 255, duration: 250);
    }
  }
}
