import 'dart:async';
import 'dart:convert';

import 'package:shared_preferences/shared_preferences.dart';
import 'package:sort_pro_printer_flutter/services/data_provider/ss_data_description.dart';
import 'package:sort_pro_printer_flutter/services/disposable.dart';

abstract class IDataProviderService implements Disposable {
  final StreamController<DateTime> lastSuccessfulSyncStreamController = StreamController<DateTime>();
  final StreamController<SsDataDescription> routePlanInfoStreamController = StreamController<SsDataDescription>.broadcast();
  final StreamController<bool> lastSyncSuccessStreamController = StreamController<bool>();
  final StreamController<int> numOfRetriesStreamController = StreamController<int>();

  Stream<DateTime> get lastSuccessfulSyncStream => lastSuccessfulSyncStreamController.stream;
  Stream<SsDataDescription> get routePlanInfoSyncStream => routePlanInfoStreamController.stream;

  Stream<bool> get lastSyncSuccessStream => lastSyncSuccessStreamController.stream;
  Stream<int> get numOfRetriesSuccessStream => numOfRetriesStreamController.stream;

  SharedPreferences? _sharedPreferences;

  static const _dataDescriptionKey = "dataDescriptionKey";
  SsDataDescription? _dataDescription;

  Future<SsDataDescription?> getDataDescriptionForTerminal(String terminalId) async {
    if (_dataDescription != null) _dataDescription!;

    _sharedPreferences ??= await SharedPreferences.getInstance();
    final dataDescriptionJsonStr = _sharedPreferences?.getString("${_dataDescriptionKey}_$terminalId") ?? "";
    if (dataDescriptionJsonStr.isEmpty) return null;

    _dataDescription = SsDataDescription.fromJson(jsonDecode(dataDescriptionJsonStr));
    return _dataDescription!;
  }

  Future<void> saveDataDescriptionForTerminal(SsDataDescription dataDescription, String terminalId) async {
    _sharedPreferences ??= await SharedPreferences.getInstance();
    _dataDescription = dataDescription;
    _sharedPreferences?.setString("${_dataDescriptionKey}_$terminalId", jsonEncode(dataDescription.toJson()));
  }

  Future<void> startTerminalPeriodicDataDownload({
    required String terminalId,
    required String empNumber,
    required Duration frequency,
  });

  Future<void> stopTerminalPeriodicDataDownload();

  Future<bool> terminalDataExists(String terminalId);

  @override
  Future<void> dispose() async {
    lastSuccessfulSyncStreamController.close();
    routePlanInfoStreamController.close();
    lastSyncSuccessStreamController.close();
  }
}
