// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ss_data_description.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SsDataDescription _$SsDataDescriptionFromJson(Map<String, dynamic> json) =>
    SsDataDescription(
      routePlanVersionId: json['routePlanVersionId'] as String,
      routePlanId: json['routePlanId'] as String,
      parkingPlanVersionId: json['parkingPlanVersionId'] as String,
      streetTypes: (json['streetTypes'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      streetDirections: (json['streetDirections'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      downloadedDateTime: json['downloadedDateTime'] == null
          ? null
          : DateTime.parse(json['downloadedDateTime'] as String),
    );

Map<String, dynamic> _$SsDataDescriptionToJson(SsDataDescription instance) =>
    <String, dynamic>{
      'routePlanVersionId': instance.routePlanVersionId,
      'routePlanId': instance.routePlanId,
      'parkingPlanVersionId': instance.parkingPlanVersionId,
      'downloadedDateTime': instance.downloadedDateTime?.toIso8601String(),
      'streetTypes': instance.streetTypes,
      'streetDirections': instance.streetDirections,
    };
