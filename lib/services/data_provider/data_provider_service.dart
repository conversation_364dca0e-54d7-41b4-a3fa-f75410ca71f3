import 'dart:async';
import 'dart:convert';
import 'dart:isolate';

import 'package:async/async.dart';
import 'package:flutter_isolate/flutter_isolate.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:sort_pro_printer_flutter/db/ssws_database_manager.dart';
import 'package:sort_pro_printer_flutter/environment/environment_config.dart';
import 'package:sort_pro_printer_flutter/network/smart_sort_ftp_client.dart';
import 'package:sort_pro_printer_flutter/services/data_provider/ss_data_description.dart';
import 'package:sort_pro_printer_flutter/services/data_provider/i_data_provider_service.dart';
import 'package:sort_pro_printer_flutter/services/device_info/device_info_service.dart';
import 'package:sort_pro_printer_flutter/services/device_info/i_device_info_service.dart';
import 'package:sort_pro_printer_flutter/services/logs/i_log_service.dart';
import 'package:sort_pro_printer_flutter/services/logs/log_service.dart';

class DataProviderService extends IDataProviderService {
  bool _initialized = false;

  Timer? _timer;
  SendPort? _sendPort;
  StreamQueue? _eventsFromIsolate;
  final SswsDatabaseManager _dbManager;

  static const maxRetryCount = 5;
  static const _maxRetryCountSharedPrefKey = "DATA_DOWNLOAD_RETRY_COUNT";

  // These are sent from the Main Isolate to our Data Sync Isolate
  static const commandKey = "command";
  static const dataKey = "data";
  static const syncTerminalDataCommand = "syncTerminalData";
  static const pauseSyncTerminalDataCommand = "pauseSyncTerminalData";
  static const connectCommand = "connect";
  static const disconnectCommand = "disconnect";

  // These are sent from our Data Sync Isolate to the Main Isolate
  static const responseCodeKey = "responseCode";
  static const responseDataKey = "responseData";
  static const responseDataKeyDbFilepaths = "dbFilepaths";
  static const responseDataKeyTerminalId = "terminalId";
  static const syncFailedResponseCodeKey = "syncFailed";
  static const syncSuccess = "syncSuccess";
  static const syncRetrying = "syncRetrying";
  static const responseDataKeyNumOfRetries = "numOfRetries";

  DataProviderService(this._dbManager);

  _init() async {
    if (!_initialized) {
      await _startIsolate();
      _initialized = true;
    }
  }

  /// Function that starts the iot hub Isolate
  /// It will pass a SendPort from a ReceivePort to the isolate's top-level function
  /// so that it sends the SendPort back from the isolate to communicate between each-other
  Future<void> _startIsolate() async {
    final p = ReceivePort();
    await FlutterIsolate.spawn(_dataSyncFunction, p.sendPort);

    // Convert the ReceivePort into a StreamQueue to receive messages from the
    // spawned isolate using a pull-based interface. Events are stored in this
    // queue until they are accessed by `events.next`.
    _eventsFromIsolate = StreamQueue<dynamic>(p);

    // The first message from the spawned isolate is a SendPort. This port is
    // used to communicate with the spawned isolate.
    _sendPort = await _eventsFromIsolate!.next;
    _listenToEventsFromIsolate();
  }

  @pragma('vm:entry-point')
  static Future<void> _dataSyncFunction(SendPort sendPort) async {
    final logService = LogService.instance;
    final IDeviceInfoService deviceInfoService = DeviceInfoService();
    final SswsDatabaseManager dbManager = SswsDatabaseManager();
    logService.trace(LogLevel.verbose, "Data Provider Isolate started.");
    SmartSortFtpClient? ftpClient;

    // Send a SendPort to the main isolate so that it can send JSON strings to
    // this isolate.
    final commandPort = ReceivePort();
    sendPort.send(commandPort.sendPort);

    StreamQueue messages = StreamQueue<dynamic>(commandPort);

    while (await messages.hasNext) {
      final message = await messages.next;
      if (message is String) {
        final jsonMessage = jsonDecode(message) as Map<String, dynamic>;
        final command = jsonMessage[commandKey];
        final inputData = jsonMessage[dataKey];

        final ftpHost = inputData?["host"];
        final ftpPort = inputData?["port"];
        final ftpUser = inputData?["user"];
        final ftpPass = inputData?["pass"];
        final terminalId = inputData?["terminalId"];
        final empNumber = inputData?["empNumber"];

        switch (command) {
          case syncTerminalDataCommand:
            try {
              logService.setUser(empNumber);
              // This LogService is running in a different isolate so we need to set the params again
              logService.setDeviceId(await deviceInfoService.getDeviceId());
              logService.setAppVersion(await deviceInfoService.getAppVersion());
              ftpClient = SmartSortFtpClient(
                host: ftpHost,
                port: ftpPort as int,
                user: ftpUser,
                pass: ftpPass,
                logService: logService,
              );
              final currentFiles = await dbManager.getCurrentDbFiles();
              await ftpClient.connect();
              final dbFilepaths = await ftpClient.downloadNewestDbFilesForTerminal(currentFiles, terminalId);
              await ftpClient.disconnect();
              sendPort.send(jsonEncode({
                responseCodeKey: syncSuccess,
                responseDataKey: {responseDataKeyDbFilepaths: dbFilepaths, responseDataKeyTerminalId: terminalId},
              }));
              logService.trace(LogLevel.verbose, "Finished enqueued work at: ${DateTime.now()}");
              break;
            } catch (e, s) {
              logService.trace(LogLevel.verbose, "Enqueued work failed at: ${DateTime.now()}");
              logService.error(e, s, false);
              final shouldAbort = await _shouldAbortFailedJob();
              if (shouldAbort) {
                /// Notify user about data not up to date
                sendPort.send(jsonEncode({
                  responseCodeKey: syncFailedResponseCodeKey,
                }));
                logService.trace(LogLevel.verbose, "Retry max count reached. Aborting worker.");
                logService.trace(LogLevel.error, "Sync DB Files failure", additionalProperties: {"terminal": terminalId});
              } else {
                logService.trace(LogLevel.verbose, "enqueued work failed. retrying...");

                SharedPreferences sharedPreferences = await SharedPreferences.getInstance();
                var retryCount = sharedPreferences.getInt(_maxRetryCountSharedPrefKey) ?? 0;
                sendPort.send(jsonEncode({
                  responseCodeKey: syncRetrying,
                  responseDataKey: {responseDataKeyNumOfRetries: retryCount},
                }));

                // Retry
                Timer(const Duration(seconds: 10), () {
                  commandPort.sendPort.send(jsonEncode({
                    commandKey: syncTerminalDataCommand,
                    dataKey: {
                      "host": ftpHost,
                      "port": ftpPort,
                      "user": ftpUser,
                      "pass": ftpPass,
                      "terminalId": terminalId,
                      "empNumber": empNumber,
                    }
                  }));
                });
              }
            }
        }
      } else if (message == null) {
        await ftpClient?.disconnect();
        // Exit if the main isolate sends a null message
        break;
      }
    }
    logService.trace(LogLevel.verbose, "Isolate closed.");
    Isolate.exit();
  }

  _listenToEventsFromIsolate() async {
    while (await _eventsFromIsolate!.hasNext) {
      final message = await _eventsFromIsolate!.next;
      if (message is String) {
        final jsonMessage = jsonDecode(message) as Map<String, dynamic>;
        final responseCode = jsonMessage[responseCodeKey];
        if (responseCode == syncFailedResponseCodeKey) {
          lastSyncSuccessStreamController.add(false);
        }
        if (responseCode == syncSuccess) {
          final List<String> filepaths = List<String>.from(jsonMessage[responseDataKey][responseDataKeyDbFilepaths]);
          final String terminalId = jsonMessage[responseDataKey][responseDataKeyTerminalId];
          if (filepaths.isNotEmpty) {
            await _dbManager.saveDbFiles(filepaths);
            _saveVersionIds(terminalId);
          }
          lastSuccessfulSyncStreamController.add(DateTime.now());
          lastSyncSuccessStreamController.add(true);
        }
        if (responseCode == syncRetrying) {
          final int numOfRetries = jsonMessage[responseDataKey][responseDataKeyNumOfRetries];
          numOfRetriesStreamController.add(numOfRetries);
        }
      }
    }
  }

  Future<void> _saveVersionIds(String terminalId) async {
    // get a hold of the databases
    final rpmDb = await _dbManager.getRpmDatabase(terminalId);
    final parkingPlanDb = await _dbManager.getParkingPlanDatabase(terminalId);

    // Getting RoutePlan Info
    final List<Map<String, dynamic>> rpmQueryResult = rpmDb.select("select RoutePlanID,RoutePlanVersionID from RoutePlan LIMIT 1");
    final routePlanId = rpmQueryResult.first['RoutePlanID'];
    final routePlanVersionId = rpmQueryResult.first['RoutePlanVersionID'];

    // Getting ParkingPlan Info
    final List<Map<String, dynamic>> parkingPlanQueryResult =
        parkingPlanDb.select("select DISTINCT ParkingPlanID as ParkingPlanID from RouteMaster WHERE ParkingPlanID IS NOT NULL LIMIT 1");
    final parkingPlanVersionId = parkingPlanQueryResult.first['ParkingPlanID'];

    final currentData = await getDataDescriptionForTerminal(terminalId);
    //Update RoutePlan Data Description only when a new version has come in
    // We also update if the street type or street directions from the current route plan are empty
    if (currentData?.routePlanVersionId != routePlanVersionId ||
        currentData?.routePlanId != routePlanId ||
        currentData?.downloadedDateTimeFormatted == null ||
        (currentData?.streetTypes.isEmpty ?? true) ||
        (currentData?.streetDirections.isEmpty ?? true)) {
      /// Get the latest street types and street directions for the new route plan
      final List<Map<String, dynamic>> rpmStreetDirections =
          rpmDb.select("SELECT DISTINCT StreetDirection FROM RoutePlan WHERE StreetDirection <> '' AND StreetDirection IS NOT NULL");
      final List<Map<String, dynamic>> rpmStreetTypes =
          rpmDb.select("SELECT DISTINCT StreetType FROM RoutePlan WHERE StreetType <> '' AND StreetType IS NOT NULL");

      final dataDescription = SsDataDescription(
        routePlanVersionId: routePlanVersionId,
        routePlanId: routePlanId,
        parkingPlanVersionId: parkingPlanVersionId,
        downloadedDateTime: DateTime.now(),
        streetDirections: rpmStreetDirections.map((e) => e["StreetDirection"].toString()).toList(),
        streetTypes: rpmStreetTypes.map((e) => e["StreetType"].toString()).toList(),
      );

      routePlanInfoStreamController.add(dataDescription);
      await saveDataDescriptionForTerminal(dataDescription, terminalId);
    }

    // dispose the databases
    rpmDb.dispose();
    parkingPlanDb.dispose();
  }

  static Future<bool> _shouldAbortFailedJob() async {
    /// WorkManager doesn't stop retrying when the function returns false.
    /// This is a manually created retry policy that will keep returning false,
    /// until the _maxRetryCount has been reached.
    SharedPreferences sharedPreferences = await SharedPreferences.getInstance();
    var retryCount = sharedPreferences.getInt(_maxRetryCountSharedPrefKey) ?? 0;
    // we've reached the limit. Let it go and mark the failure time.
    if (retryCount >= maxRetryCount) {
      await sharedPreferences.setInt(_maxRetryCountSharedPrefKey, 0);
      return true; // return true to abort the worker
    } else {
      retryCount++;
      await sharedPreferences.setInt(_maxRetryCountSharedPrefKey, retryCount);
      return false; // return false to retry
    }
  }

  @override
  Future<void> startTerminalPeriodicDataDownload({
    required String terminalId,
    required String empNumber,
    required Duration frequency,
  }) async {
    await _init();
    stopTerminalPeriodicDataDownload();
    // Send the command to sync the data immediately
    _sendPort?.send(jsonEncode({
      commandKey: syncTerminalDataCommand,
      dataKey: {
        "host": EnvironmentConfig.ftpSsHost,
        "port": int.tryParse(EnvironmentConfig.ftpSsPort) ?? 21, // default port just in case
        "user": EnvironmentConfig.ftpSsUser,
        "pass": EnvironmentConfig.ftpSsPass,
        "terminalId": terminalId,
        "empNumber": empNumber,
      }
    }));
    // Create a timer to sync at the given interval
    _timer = Timer.periodic(frequency, (_) {
      _sendPort?.send(jsonEncode({
        commandKey: syncTerminalDataCommand,
        dataKey: {
          "host": EnvironmentConfig.ftpSsHost,
          "port": int.tryParse(EnvironmentConfig.ftpSsPort) ?? 21, // default port just in case
          "user": EnvironmentConfig.ftpSsUser,
          "pass": EnvironmentConfig.ftpSsPass,
          "terminalId": terminalId,
          "empNumber": empNumber,
        }
      }));
    });
  }

  @override
  Future<void> stopTerminalPeriodicDataDownload() async {
    _timer?.cancel();
    _timer = null;
  }

  @override
  Future<void> dispose() async {
    stopTerminalPeriodicDataDownload();
    _sendPort?.send(null); // sending null to stop the isolate
    super.dispose();
  }

  @override
  Future<bool> terminalDataExists(String terminalId) async {
    return await _dbManager.databasesExist(terminalId);
  }
}
