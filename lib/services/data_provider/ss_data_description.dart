import 'package:intl/intl.dart';
import 'package:json_annotation/json_annotation.dart';

part 'ss_data_description.g.dart';

@JsonSerializable()
class SsDataDescription {
  final String routePlanVersionId;
  final String routePlanId;
  final String parkingPlanVersionId;
  final DateTime? downloadedDateTime;
  final List<String> streetTypes;
  final List<String> streetDirections;

  SsDataDescription({
    required this.routePlanVersionId,
    required this.routePlanId,
    required this.parkingPlanVersionId,
    this.streetTypes = const [],
    this.streetDirections = const [],
    this.downloadedDateTime,
  });

  String? get downloadedDateTimeFormatted {
    if (downloadedDateTime != null) {
      return DateFormat('yyyy-MM-dd').format(downloadedDateTime!);
    }
    return null;
  }

  factory SsDataDescription.fromJson(Map<String, dynamic> json) => _$SsDataDescriptionFromJson(json);

  Map<String, dynamic> toJson() => _$SsDataDescriptionToJson(this);
}
