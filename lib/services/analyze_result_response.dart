/// A VERY simplified version of the response we get from the Analyze Result Response from Azure Form Recognizer Endpoint
class AnalyzeResultResponse {
  final String status;
  final String createdDateTime;
  final String lastUpdatedDateTime;
  final AnalyzeResult? analyzeResult;

  AnalyzeResultResponse(this.status, this.createdDateTime, this.lastUpdatedDateTime, this.analyzeResult);

  factory AnalyzeResultResponse.fromJson(Map<String, dynamic> json) => AnalyzeResultResponse(
        json['status'],
        json['createdDateTime'],
        json['lastUpdatedDateTime'],
        json['analyzeResult'] == null ? null : AnalyzeResult.fromJson(json['analyzeResult']),
      );
}

class AnalyzeResult {
  List<Document> documents;

  AnalyzeResult({this.documents = const []});

  factory AnalyzeResult.fromJson(Map<String, dynamic> json) =>
      AnalyzeResult(documents: List<Document>.from(json["documents"].map((data) => Document.fromJson(data))));
}

class Document {
  final Fields fields;

  Document(this.fields);

  factory Document.fromJson(Map<String, dynamic> json) => Document(Fields.fromJson(json["fields"]));
}

class Fields {
  final Field postCode;
  final Field city;
  final Field provinceCode;
  final Field streetName;
  final Field streetNumber;
  final Field recipientName;
  final Field pin;

  Fields({
    required this.postCode,
    required this.city,
    required this.provinceCode,
    required this.streetName,
    required this.streetNumber,
    required this.recipientName,
    required this.pin,
  });

  factory Fields.fromJson(Map<String, dynamic> json) => Fields(
        postCode: Field.fromJson(json["PostCode"]),
        city: Field.fromJson(json["City"]),
        provinceCode: Field.fromJson(json["ProvinceCode"]),
        streetName: Field.fromJson(json["StreetName"]),
        streetNumber: Field.fromJson(json["StreetNumber"]),
        recipientName: Field.fromJson(json["Recipient Name"]),
        pin: Field.fromJson(json["PIN"]),
      );
}

class Field {
  final String type;
  final String? valueString;
  final String? content;
  final num confidence;
  final List<BoundingRegion> boundingRegions;
  final List<Span> spans;

  Field({
    required this.type,
    required this.valueString,
    required this.content,
    required this.confidence,
    required this.boundingRegions,
    required this.spans,
  });

  factory Field.fromJson(Map<String, dynamic> json) {

    return Field(
      type: json["type"],
      valueString: json["valueString"],
      content: json["content"],
      confidence: json["confidence"],
      boundingRegions: json["boundingRegions"] == null ? const [] : List<BoundingRegion>.from(json["boundingRegions"].map((e) => BoundingRegion.fromJson(e))),
      spans: json["spans"] == null ? const [] : List<Span>.from(json["spans"].map((e) => Span.fromJson(e))));
  }
}

class BoundingRegion {
  final List<int> polygon;

  BoundingRegion(this.polygon);

  factory BoundingRegion.fromJson(Map<String, dynamic> json) => BoundingRegion(List<int>.from(json["polygon"]));
}

class Span {
  final num offset;
  final int length;

  Span(this.offset, this.length);

  factory Span.fromJson(Map<String, dynamic> json) => Span(
        json["offset"],
        json["length"],
      );
}
