import 'dart:convert';
import 'dart:io';

import 'package:path/path.dart';
import 'package:sort_pro_printer_flutter/db/ssws_database_manager.dart';
import 'package:sort_pro_printer_flutter/services/logs/i_log_service.dart';
import 'package:sort_pro_printer_flutter/services/logs/log_service.dart';
import 'package:sort_pro_printer_flutter/services/scan_logs/i_scan_log_repository.dart';
import 'package:sort_pro_printer_flutter/services/scan_logs/models/scan_log.dart';
import 'package:sort_pro_printer_flutter/services/scan_logs/models/sqlite_result_codes.dart';
import 'package:sqlite3/sqlite3.dart';


class ScanLogDbRepository extends IScanLogRepository {
  Database? _scanLogDb;
  static const _scanLogDbName = "scan_log.db";
  static const _scanLogTableName = "ScanLog";
  static const _exportScanLogPath = "/sdcard/Documents/SortPro Exports";

  static const upSyncRecordPurgeTriggerIntervalInHours = 72;
  static const upSyncRetryAttemptLimit = 3;
  static const skipRetryForErrorCodes = "'2001'";

  Future<Database> get db async {
    _scanLogDb ??= sqlite3.open(
      // Set the path to the database. Note: Using the `join` function from the
      // `path` package is best practice to ensure the path is correctly
      // constructed for each platform.
        join(await SswsDatabaseManager.getDatabasesPath(), _scanLogDbName),
    );
    // WAL mode improves concurrency by allowing reads to happen even during a write operation.
    _scanLogDb?.execute('PRAGMA journal_mode=WAL');
    return _scanLogDb!;
  }

  ScanLogDbRepository() {
    _initDb();
  }

  _initDb() async {
    // create databases directory if it doesnt exist
    await Directory(await SswsDatabaseManager.getDatabasesPath()).create();
    await _createScanLogTable(await db);
  }

  Future<void> _createScanLogTable(Database db) async {
    // Run the CREATE TABLE statement on the database.
    return db.execute("""CREATE TABLE IF NOT EXISTS [$_scanLogTableName](
    [ID] INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT UNIQUE,
    [SSDeviceID] VARCHAR(60) NULL,
    [UserLoginID] INTEGER NULL,
    [RoutePlanVersionId] VARCHAR NULL,
    [ParkingPlanMasterVersionID] VARCHAR NULL,
    [TerminalID] VARCHAR(3) NULL,
    [PIN] VARCHAR NULL,
    [PrePrintID] VARCHAR NULL,
    [PrimarySort] VARCHAR(5) NULL,
    [BeltSide] VARCHAR(1) NULL,
    [Route] VARCHAR(5) NULL,
    [SSStatusReason] VARCHAR(3) NULL,
    [Shelf] VARCHAR(5) NULL,
    [DeliverySequenceID] VARCHAR(5) NULL,
    [TruckShelfOverride] VARCHAR(3) NULL,
    [SSMode] VARCHAR(2) NULL,
    [ScanDateTime] VARCHAR NULL,
    [PostalCode] VARCHAR(6) NULL,
    [ProvinceCode] VARCHAR NULL,
    [MunicipalityName] VARCHAR(50) NULL,
    [StreetNumber] VARCHAR(5) NULL,
    [StreetNumberSuffix] VARCHAR NULL,
    [StreetName] VARCHAR NULL,
    [StreetType] VARCHAR(20) NULL,
    [StreetDir] VARCHAR(20) NULL,
    [UnitNumber] VARCHAR(20) NULL,
    [CustomerName] VARCHAR(100) NULL,
    [PrintDateTime] VARCHAR NULL,
    [DeliveryTime] VARCHAR(2) NULL,
    [ShipmentType] VARCHAR(1) NULL,
    [DeliveryType] VARCHAR(1) NULL,
    [DiversionCode] VARCHAR(1) NULL,
    [PackageType] INTEGER NULL,
    [HandlingClassType] VARCHAR(2) NULL,
    [BarcodeType] VARCHAR(1) NULL,
    [ResolvedBy] VARCHAR(1) NULL,
    [AlternateAddressFlag] VARCHAR(1) NULL,
    [RetryCount] INTEGER NULL,
    [UpSyncErrorCode] VARCHAR(10) NULL
    )""");
  }

  @override
  Future<void> deleteScanLogsByPin(List<String> pins) async {
    LogService.instance.trace(LogLevel.verbose, "deleteScanLogsByPin: Delete ScanLogs by PIN", additionalProperties: {"PIN": pins.join(",")});
    // Get a reference to the database.
    final query = "delete from $_scanLogTableName WHERE PIN IN (${pins.map<String>((String value) => "'$value'").join(',')})";
    LogService.instance.trace(LogLevel.verbose, "deleteScanLogsByPin: Running SQLite query", additionalProperties: {"query": query});
    await _executeWriteCommand(query);
    LogService.instance.trace(LogLevel.verbose, "deleteScanLogsByPin: ScanLogs deleted.", additionalProperties: {"pins": pins});
  }

  @override
  Future<List<ScanLog>> getScanLogs() async {
    LogService.instance.trace(LogLevel.verbose, "getScanLogs: Get ScanLogs for table $_scanLogTableName");
    final scanLogDb = await db;
    const query = """SELECT sl.* FROM $_scanLogTableName sl
        INNER JOIN (SELECT PIN, MAX(ID) as MaxLogId from $_scanLogTableName 
	      GROUP BY PIN) sub ON sl.PIN=sub.PIN and sl.ID=sub.MaxLogId
        WHERE sl.RetryCount <= $upSyncRetryAttemptLimit""";
    LogService.instance.trace(LogLevel.verbose, "getScanLogs: Running SQLite query", additionalProperties: {"query": query});
    final scanLogs = (scanLogDb.select(query))
        .map((e) => ScanLog.fromJson(e))
        .toList();

    return scanLogs;
  }

  @override
  Future<bool> insertScanLog(ScanLog scanLog) async {
    LogService.instance.trace(LogLevel.verbose, "insertScanLog: Insert ScanLog", additionalProperties: {
      "record": jsonEncode(scanLog)
    });
    // Insert the ScanLog
    // we put a ? in the beginning because it's the ID field and we dont want to track it within the code, so we let sql autofill
    // note: if the order of columns changes or we add/remove columns/columNames, this will need to be revisited
    try {
      final query = "INSERT INTO $_scanLogTableName VALUES (?,${scanLog.toSqlString()})";
      LogService.instance.trace(LogLevel.verbose, "insertScanLog: Running SQLite query", additionalProperties: {"query": query});
      await _executeWriteCommand(query);

      LogService.instance.trace(LogLevel.information, "insertScanLog: ScanLog record saved locally",
          additionalProperties: {"record": jsonEncode(scanLog), "pin": scanLog.pin.toString()});
      return true;
    } on SqliteException catch (_) {
      return false;
    }
  }

  @override
  Future<void> incrementRetryCountForPins(List<String> pins) async {
    LogService.instance.trace(LogLevel.verbose, "incrementRetryCountForPins: Updating RetryCounts for ${pins.length} records");

    // Get a reference to the database.
    String dbTransaction = "";
    // Perform mass update
    for (String pin in pins) {
      dbTransaction += "UPDATE $_scanLogTableName SET RetryCount = RetryCount + 1 WHERE PIN = '$pin';";
    }
    LogService.instance.trace(LogLevel.verbose, "incrementRetryCountForPins: Running SQLite query", additionalProperties: {"query": dbTransaction});
    await _executeWriteCommand('''
    BEGIN TRANSACTION;
    $dbTransaction
    COMMIT;
    ''');
    LogService.instance.trace(LogLevel.verbose, "incrementRetryCountForPins: RetryCounts incremented for pins.");
  }

  @override
  dispose() {
    _scanLogDb?.dispose();
  }

  @override
  Future<void> deleteInvalidScanLogs() async {
    LogService.instance.trace(LogLevel.verbose, "deleteInvalidScanLogs: Delete invalid ScanLogs");
    const query = "delete from $_scanLogTableName WHERE PIN IS NULL OR PIN = '' OR (datetime(PrintDateTime) < datetime('now','-$upSyncRecordPurgeTriggerIntervalInHours hours') AND ( RetryCount > $upSyncRetryAttemptLimit OR UpSyncErrorCode IN ($skipRetryForErrorCodes)))";
    LogService.instance.trace(LogLevel.verbose, "deleteInvalidScanLogs: Running SQLite Query", additionalProperties: {"query": query});
    await _executeWriteCommand(query);
    LogService.instance.trace(LogLevel.verbose, "deleteInvalidScanLogs: ScanLogs purged");
  }

  static Future<void> exportScanLogs() async {
    final file = File(join(await SswsDatabaseManager.getDatabasesPath(), _scanLogDbName));

    if(!file.existsSync()) return; // if the db hasn't been create it, don't don anything

    final directory = Directory(_exportScanLogPath);
    if(!directory.existsSync()) directory.createSync();

    if (file.existsSync()) {
      final filename = basename(file.path);
      final path = "$_exportScanLogPath/$filename";
      await file.copy(path);
    }
  }

  _executeWriteCommand(String command) async {
    const maxAttempts = 5;
    final scanLogDb = await db;

    for (int retryNumber = 0; retryNumber < maxAttempts; ++retryNumber) {
      try {
        scanLogDb.execute(command);
        return; // we're done, successfully, we can just exit :)
      } on SqliteException catch (e, s) {
        if (e.resultCode != SqliteResultCodes.busy.id && e.resultCode != SqliteResultCodes.locked.id) {
          // retry policy only applies to busy and locked
          LogService.instance.error(e, s, false);
          return;
        }

        LogService.instance.trace(LogLevel.verbose, "Database locked.", additionalProperties: {
          "causingStatement": e.causingStatement ?? "",
          "affectedStatement": command
        });
        LogService.instance.trace(LogLevel.verbose, "re-queueing sqlite query.", additionalProperties: {
          "query": command
        });

        // wait some time to re-execute the item
        await Future.delayed(const Duration(milliseconds: 500));
      }
    }

    // if we get here, we've exhausted retries and not gotten success (due to early returns above)
    LogService.instance.trace(LogLevel.verbose, "Max retryCount reached trying to write query", additionalProperties: {"query": command});
    LogService.instance.trace(LogLevel.warning, "A database query was not able to execute due to a database lock");
  }
}
