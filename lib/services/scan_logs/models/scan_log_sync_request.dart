import 'package:sort_pro_printer_flutter/app/utils/xml_parser.dart';
import 'package:sort_pro_printer_flutter/services/logs/log_service.dart';
import 'package:sort_pro_printer_flutter/services/scan_logs/models/scan_log.dart';
import 'package:convert/convert.dart';

class ScanLogSyncRequest {
  final String ssDeviceId;
  final String userLoginID;
  final String terminalId;
  final List<ScanLog> scanLogList;

  ScanLogSyncRequest({required this.ssDeviceId, required this.userLoginID, required this.terminalId, required this.scanLogList});

  // I know... nothing we can do, this is how the request is structured :(
  String toScanLogUpSyncServiceRequest() {
    StringBuffer sb = StringBuffer();
    const fieldDelimiter = "|";
    const recordDelimiter = "^";

    sb.write(ssDeviceId);
    sb.write(fieldDelimiter);
    sb.write(userLoginID);
    sb.write(fieldDelimiter);
    sb.write(terminalId);

    for (ScanLog scanLog in scanLogList) {
      sb.write(recordDelimiter);
      sb.write(_encodeField(scanLog.routePlanVersionId));
      sb.write(fieldDelimiter);
      sb.write(_encodeField(scanLog.parkingPlanMasterVersionId));
      sb.write(fieldDelimiter);
      sb.write(_encodeField(scanLog.pin));
      sb.write(fieldDelimiter);
      sb.write(_encodeField(scanLog.prePrintId));
      sb.write(fieldDelimiter);
      sb.write(_encodeField(scanLog.primarySort));
      sb.write(fieldDelimiter);
      sb.write(_encodeField(scanLog.beltSide));
      sb.write(fieldDelimiter);
      sb.write(_encodeField(scanLog.route));
      sb.write(fieldDelimiter);
      sb.write(_encodeField(scanLog.ssStatusReason));
      sb.write(fieldDelimiter);
      sb.write(_encodeField(scanLog.shelf));
      sb.write(fieldDelimiter);
      sb.write(_encodeField(scanLog.deliverySequenceId));
      sb.write(fieldDelimiter);
      sb.write(_encodeField(scanLog.truckShelfOverride));
      sb.write(fieldDelimiter);
      sb.write(_encodeField(scanLog.ssMode));
      sb.write(fieldDelimiter);
      sb.write((_encodeField((scanLog.scanDateTime ?? "").replaceAll(" ", ""))));
      sb.write(fieldDelimiter);
      sb.write(_encodeField(scanLog.postalCode));
      sb.write(fieldDelimiter);
      sb.write(_encodeField(scanLog.provinceCode));
      sb.write(fieldDelimiter);
      sb.write(_encodeField(scanLog.municipalityName));
      sb.write(fieldDelimiter);
      sb.write(_encodeField(scanLog.streetNumber));
      sb.write(fieldDelimiter);
      sb.write(_encodeField(scanLog.streetNumberSuffix));
      sb.write(fieldDelimiter);
      sb.write(_encodeField(scanLog.streetName));
      sb.write(fieldDelimiter);
      sb.write(_encodeField(scanLog.streetType));
      sb.write(fieldDelimiter);
      sb.write(_encodeField(scanLog.streetDir));
      sb.write(fieldDelimiter);
      sb.write((_encodeField(scanLog.unitNumber ?? "") == "0" ? "" : scanLog.unitNumber));
      sb.write(fieldDelimiter);
      sb.write(_encodeField(scanLog.customerName));
      sb.write(fieldDelimiter);
      sb.write((_encodeField((scanLog.printDateTime ?? "").replaceAll(" ", ""))));
      sb.write(fieldDelimiter);
      sb.write(_encodeField(scanLog.deliveryTime));
      sb.write(fieldDelimiter);
      sb.write(_encodeField(scanLog.shipmentType));
      sb.write(fieldDelimiter);
      sb.write(_encodeField(scanLog.deliveryType));
      sb.write(fieldDelimiter);
      sb.write(_encodeField(scanLog.diversionCode));
      sb.write(fieldDelimiter);
      sb.write(_encodeField(scanLog.handlingClassType));
      sb.write(fieldDelimiter);
      sb.write(_encodeField(scanLog.packageType.toString()));
      sb.write(fieldDelimiter);
      sb.write(_encodeField(scanLog.barcodeType));
      sb.write(fieldDelimiter);
      sb.write(_encodeField(scanLog.resolvedBy));
      sb.write(fieldDelimiter);
      sb.write(_encodeField(scanLog.alternateAddressFlag ?? "N"));
    }

    return sb.toString();
  }

  String _encodeField(String? field) {
    if (field == null || field == "null") return "";
    final sanitizedField = _sanitizeField(field);

    // split the string into individual characters
    final List<String> characters = sanitizedField.split("");
    String encodedStr = "";
    for (String character in characters) {
      try {
        // try to encode and add each individual character
        String encodedChar = percent.encode(character.codeUnits);
        encodedStr+=encodedChar;
      } on FormatException catch (e, s) {
        // if we fail to encode the character, add a ? symbol instead
        encodedStr+=percent.encode("?".codeUnits);
        LogService.instance.error(e, s, false, additionalProperties: {"character": character, "full-string": sanitizedField});
      }
    }
    return encodedStr;
  }

  String _sanitizeField(String field) {
    return XmlParser.sanitizeXMLString(field);
  }
}
