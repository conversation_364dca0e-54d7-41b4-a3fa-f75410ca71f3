// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'scan_log.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ScanLog _$Scan<PERSON>og<PERSON>rom<PERSON>son(Map<String, dynamic> json) => ScanLog(
      id: json['ID'] as int?,
      ssDeviceId: json['SSDeviceID'] as String?,
      userLoginId: json['UserLoginID'] as int?,
      routePlanVersionId: json['RoutePlanVersionId'] as String?,
      parkingPlanMasterVersionId: json['ParkingPlanMasterVersionID'] as String?,
      terminalId: json['TerminalID'] as String?,
      pin: json['PIN'] as String?,
      prePrintId: json['PrePrintID'] as String?,
      primarySort: json['PrimarySort'] as String?,
      beltSide: json['BeltSide'] as String?,
      route: json['Route'] as String?,
      ssStatusReason: json['SSStatusReason'] as String?,
      shelf: json['Shelf'] as String?,
      deliverySequenceId: json['DeliverySequenceID'] as String?,
      truckShelfOverride: json['TruckShelfOverride'] as String?,
      ssMode: json['SSMode'] as String?,
      scanDateTime: json['ScanDateTime'] as String?,
      postalCode: json['PostalCode'] as String?,
      provinceCode: json['ProvinceCode'] as String?,
      municipalityName: json['MunicipalityName'] as String?,
      streetNumber: json['StreetNumber'] as String?,
      streetNumberSuffix: json['StreetNumberSuffix'] as String?,
      streetName: json['StreetName'] as String?,
      streetType: json['StreetType'] as String?,
      streetDir: json['StreetDir'] as String?,
      unitNumber: json['UnitNumber'] as String?,
      customerName: json['CustomerName'] as String?,
      printDateTime: json['PrintDateTime'] as String?,
      deliveryTime: json['DeliveryTime'] as String?,
      shipmentType: json['ShipmentType'] as String?,
      deliveryType: json['DeliveryType'] as String?,
      diversionCode: json['DiversionCode'] as String?,
      packageType: json['PackageType'] as int?,
      handlingClassType: json['HandlingClassType'] as String?,
      barcodeType: json['BarcodeType'] as String?,
      resolvedBy: json['ResolvedBy'] as String?,
      alternateAddressFlag: json['AlternateAddressFlag'] as String?,
      retryCount: json['RetryCount'] as int? ?? 0,
      upSyncErrorCode: json['UpSyncErrorCode'] as String?,
    );

Map<String, dynamic> _$ScanLogToJson(ScanLog instance) => <String, dynamic>{
      'SSDeviceID': instance.ssDeviceId,
      'UserLoginID': instance.userLoginId,
      'RoutePlanVersionId': instance.routePlanVersionId,
      'ParkingPlanMasterVersionID': instance.parkingPlanMasterVersionId,
      'TerminalID': instance.terminalId,
      'PIN': instance.pin,
      'PrePrintID': instance.prePrintId,
      'PrimarySort': instance.primarySort,
      'BeltSide': instance.beltSide,
      'Route': instance.route,
      'SSStatusReason': instance.ssStatusReason,
      'Shelf': instance.shelf,
      'DeliverySequenceID': instance.deliverySequenceId,
      'TruckShelfOverride': instance.truckShelfOverride,
      'SSMode': instance.ssMode,
      'ScanDateTime': instance.scanDateTime,
      'PostalCode': instance.postalCode,
      'ProvinceCode': instance.provinceCode,
      'MunicipalityName': instance.municipalityName,
      'StreetNumber': instance.streetNumber,
      'StreetNumberSuffix': instance.streetNumberSuffix,
      'StreetName': instance.streetName,
      'StreetType': instance.streetType,
      'StreetDir': instance.streetDir,
      'UnitNumber': instance.unitNumber,
      'CustomerName': instance.customerName,
      'PrintDateTime': instance.printDateTime,
      'DeliveryTime': instance.deliveryTime,
      'ShipmentType': instance.shipmentType,
      'DeliveryType': instance.deliveryType,
      'DiversionCode': instance.diversionCode,
      'PackageType': instance.packageType,
      'HandlingClassType': instance.handlingClassType,
      'BarcodeType': instance.barcodeType,
      'ResolvedBy': instance.resolvedBy,
      'AlternateAddressFlag': instance.alternateAddressFlag,
      'RetryCount': instance.retryCount,
      'UpSyncErrorCode': instance.upSyncErrorCode,
    };
