// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'scan_log_sync_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ScanLogSyncResponse _$ScanLogSyncResponseFromJson(Map<String, dynamic> json) =>
    ScanLogSyncResponse(
      json['statusCode'] as String? ?? '',
      (json['errorList'] as List<dynamic>?)
              ?.map((e) =>
                  ClsScanLogSyncError.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
    );

Map<String, dynamic> _$ScanLogSyncResponseToJson(
        ScanLogSyncResponse instance) =>
    <String, dynamic>{
      'statusCode': instance.statusCode,
      'errorList': instance.errorList,
    };

ClsScanLogSyncError _$ClsScanLogSyncErrorFromJson(Map<String, dynamic> json) =>
    ClsScanLogSyncError(
      json['Pin'] as String? ?? '',
      json['ErrorCode'] as String? ?? '',
      json['ErrorMessage'] as String? ?? '',
    );

Map<String, dynamic> _$ClsScanLogSyncErrorToJson(
        ClsScanLogSyncError instance) =>
    <String, dynamic>{
      'Pin': instance.pinThatFailed,
      'ErrorCode': instance.errorCode,
      'ErrorMessage': instance.errorMessage,
    };
