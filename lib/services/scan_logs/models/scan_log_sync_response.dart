import 'package:json_annotation/json_annotation.dart';

part 'scan_log_sync_response.g.dart';

@JsonSerializable()
class ScanLogSyncResponse {
  @Json<PERSON>ey(name: "statusCode", defaultValue: "")
  final String statusCode;
  @J<PERSON><PERSON><PERSON>(name: "errorList", defaultValue: [])
  final List<ClsScanLogSyncError> errorList;

  ScanLogSyncResponse(this.statusCode, this.errorList);

  factory ScanLogSyncResponse.fromJson(Map<String, dynamic> json) => _$ScanLogSyncResponseFromJson(json);
  Map<String, dynamic> toJson() => _$ScanLogSyncResponseToJson(this);

}

@JsonSerializable()
class ClsScanLogSyncError {
  @JsonKey(name: "Pin", defaultValue: "")
  final String pinThatFailed;
  @JsonKey(name: "ErrorCode", defaultValue: "")
  final String errorCode;
  @Json<PERSON>ey(name: "ErrorMessage", defaultValue: "")
  final String errorMessage;

  ClsScanLogSyncError(this.pinThatFailed, this.errorCode, this.errorMessage);

  factory ClsScanLogSyncError.fromJson(Map<String, dynamic> json) => _$ClsScanLogSyncErrorFromJson(json);
  Map<String, dynamic> toJson() => _$ClsScanLogSyncErrorToJson(this);

}
