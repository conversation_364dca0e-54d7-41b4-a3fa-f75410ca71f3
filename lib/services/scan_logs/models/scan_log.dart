import 'package:json_annotation/json_annotation.dart';
import 'package:sort_pro_printer_flutter/app/pin_lookup/data/models/lookup_result.dart';

part 'scan_log.g.dart';

@JsonSerializable(fieldRename: FieldRename.pascal)
class ScanLog {
  @Json<PERSON>ey(includeToJson: false, name: "ID")
  final int? id;
  @<PERSON><PERSON><PERSON><PERSON>(name: "SSDeviceID")
  final String? ssDeviceId;
  @<PERSON><PERSON><PERSON><PERSON>(name: "UserLoginID")
  final int? userLoginId;
  final String? routePlanVersionId;
  @<PERSON><PERSON><PERSON><PERSON>(name: "ParkingPlanMasterVersionID")
  final String? parkingPlanMasterVersionId;
  @<PERSON><PERSON><PERSON><PERSON>(name: "TerminalID")
  final String? terminalId;
  @J<PERSON><PERSON><PERSON>(name: "PIN")
  final String? pin;
  @J<PERSON><PERSON><PERSON>(name: "PrePrintID")
  final String? prePrintId;
  final String? primarySort;
  final String? beltSide;
  final String? route;
  @<PERSON><PERSON><PERSON><PERSON>(name: "SSStatusReason")
  final String? ssStatusReason;
  final String? shelf;
  @<PERSON><PERSON><PERSON><PERSON>(name: "DeliverySequenceID")
  final String? deliverySequenceId;
  final String? truckShelfOverride;
  @<PERSON><PERSON><PERSON><PERSON>(name: "SSMode")
  final String? ssMode;
  final String? scanDateTime;
  final String? postalCode;
  final String? provinceCode;
  final String? municipalityName;
  final String? streetNumber;
  final String? streetNumberSuffix;
  final String? streetName;
  final String? streetType;
  final String? streetDir;
  final String? unitNumber;
  final String? customerName;
  final String? printDateTime;
  final String? deliveryTime;
  final String? shipmentType;
  final String? deliveryType;
  final String? diversionCode;
  final int? packageType;
  final String? handlingClassType;
  final String? barcodeType;
  final String? resolvedBy;
  final String? alternateAddressFlag;
  final int? retryCount;
  final String? upSyncErrorCode;

  ScanLog({
    this.id,
    this.ssDeviceId,
    this.userLoginId,
    this.routePlanVersionId,
    this.parkingPlanMasterVersionId,
    this.terminalId,
    this.pin,
    this.prePrintId,
    this.primarySort,
    this.beltSide,
    this.route,
    this.ssStatusReason,
    this.shelf,
    this.deliverySequenceId,
    this.truckShelfOverride,
    this.ssMode,
    this.scanDateTime,
    this.postalCode,
    this.provinceCode,
    this.municipalityName,
    this.streetNumber,
    this.streetNumberSuffix,
    this.streetName,
    this.streetType,
    this.streetDir,
    this.unitNumber,
    this.customerName,
    this.printDateTime,
    this.deliveryTime,
    this.shipmentType,
    this.deliveryType,
    this.diversionCode,
    this.packageType,
    this.handlingClassType,
    this.barcodeType,
    this.resolvedBy,
    this.alternateAddressFlag,
    this.retryCount = 0,
    this.upSyncErrorCode,
  });

  factory ScanLog.fromJson(Map<String, dynamic> json) => _$ScanLogFromJson(json);

  factory ScanLog.fromLookupResult(LookupResult lookupResult) => ScanLog(
        municipalityName: lookupResult.municipalityName,
        postalCode: lookupResult.postalCode,
        ssStatusReason: lookupResult.lookupResultType.ssReasonKey,
        beltSide: lookupResult.conveyorSide,
        primarySort: lookupResult.pudroNumber,
        pin: lookupResult.pin,
        terminalId: lookupResult.terminalNumber,
        route: lookupResult.routeNumber,
        shelf: lookupResult.shelfNumber,
        routePlanVersionId: lookupResult.routePlanVersionId,
        deliverySequenceId: lookupResult.sequenceNumber,
        resolvedBy: lookupResult.lookupResolvedBy.id.toString(),
        deliveryTime: lookupResult.deliveryTime,
        deliveryType: lookupResult.deliveryType,
        diversionCode: lookupResult.diversionCode,
        ssDeviceId: lookupResult.ssDeviceId,
        ssMode: lookupResult.ssMode,
        scanDateTime: lookupResult.scanDateTime,
        shipmentType: lookupResult.shipmentType,
        streetDir: lookupResult.streetDir,
        streetNumber: lookupResult.fromStreetNumber,
        streetNumberSuffix: lookupResult.streetNumberSuffix,
        streetType: lookupResult.streetType,
        truckShelfOverride: lookupResult.truckShelfOverride,
        unitNumber: lookupResult.unitNumber,
        barcodeType: lookupResult.barcodeType,
        userLoginId: lookupResult.userLoginId,
        customerName: lookupResult.customerName,
        alternateAddressFlag: lookupResult.alternateAddressFlag,
        handlingClassType: lookupResult.handlingClassType,
        provinceCode: lookupResult.provinceCode,
        packageType: lookupResult.packageType,
        parkingPlanMasterVersionId: lookupResult.parkingPlanMasterVersionId,
        prePrintId: lookupResult.prePrintId,
        printDateTime: lookupResult.printDateTime,
        streetName: lookupResult.streetName,
      );

  Map<String, dynamic> toJson() => _$ScanLogToJson(this);

  String toSqlString() =>
      "'${ssDeviceId ?? ""}',$userLoginId,'${routePlanVersionId ?? ""}','${parkingPlanMasterVersionId ?? ""}','${terminalId ?? ""}','${pin ?? ""}','${prePrintId ?? ""}','${primarySort ?? ""}','${beltSide ?? ""}','${route ?? ""}','${ssStatusReason ?? ""}','${shelf ?? ""}','${deliverySequenceId ?? ""}','${truckShelfOverride ?? ""}','${ssMode ?? ""}','${scanDateTime ?? ""}','${postalCode ?? ""}','${provinceCode ?? ""}','${municipalityName ?? ""}','${streetNumber ?? ""}','${streetNumberSuffix ?? ""}','${streetName?.replaceAll("'", "''") ?? ""}','${streetType ?? ""}','${streetDir ?? ""}','${unitNumber ?? ""}','${customerName?.replaceAll("'", "''") ?? ""}','${printDateTime ?? ""}','${deliveryTime ?? ""}','${shipmentType ?? ""}','${deliveryType ?? ""}','${diversionCode ?? ""}',$packageType,'${handlingClassType ?? ""}','${barcodeType ?? ""}','${resolvedBy ?? ""}','${alternateAddressFlag ?? ""}',$retryCount,'${upSyncErrorCode ?? ""}'";
}
