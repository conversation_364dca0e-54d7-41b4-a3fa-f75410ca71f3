import 'package:sort_pro_printer_flutter/services/disposable.dart';
import 'package:sort_pro_printer_flutter/services/scan_logs/models/scan_log.dart';

abstract class IScanLogRepository implements Disposable {
    Future<List<ScanLog>> getScanLogs();
    Future<bool> insertScanLog(ScanLog scanLog);
    Future<void> deleteScanLogsByPin(List<String> pins);
    Future<void> incrementRetryCountForPins(List<String> pins);
    Future<void> deleteInvalidScanLogs();
}