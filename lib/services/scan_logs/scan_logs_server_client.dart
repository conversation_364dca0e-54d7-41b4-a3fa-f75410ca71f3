import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:dio/io.dart';
import 'package:sort_pro_printer_flutter/network/logging_interceptor.dart';
import 'package:sort_pro_printer_flutter/services/logs/log_service.dart';
import 'package:sort_pro_printer_flutter/services/scan_logs/models/scan_log_sync_request.dart';
import 'package:sort_pro_printer_flutter/services/scan_logs/models/scan_log_sync_response.dart';

import '../sslws/sslws_http_client.dart';

class ScanLogsServerClient {
  late final Dio _dio;

  final String domain;
  final String baseUrl;
  final String user;
  final String password;

  ScanLogsServerClient(this.domain, this.baseUrl, this.user, this.password) {
    _dio = Dio(
      BaseOptions(
        connectTimeout: const Duration(seconds: 5),
        receiveTimeout: const Duration(seconds: 10),
        sendTimeout: const Duration(seconds: 5),
      ),
    )..interceptors.add(LoggingInterceptor(LogService.instance));
    // The web service is only accessible through VPN so is safe to trust all here.
    (_dio.httpClientAdapter as IOHttpClientAdapter).createHttpClient = () => SslwsHttpClient.client;
  }

  // This request gets a Lookup result back from the Smart Sort Lookup WebService
  Future<ScanLogSyncResponse> uploadScanLogs(ScanLogSyncRequest request) async {
    final String authInfo = "Basic ${base64Encode(utf8.encode("$domain\\$user:$password"))}";

    final requestData = request.toScanLogUpSyncServiceRequest();

    try {
      final response = await _dio.post(
        "$baseUrl/RestServices/SmartSortScanService.svc/SmartSortScan",
        options: Options(
          contentType: Headers.jsonContentType,
          headers: {
            "Accept": "*/*",
            "Authorization": authInfo,
          },
        ),
        // backend expects the data as a string, so we wrap it in quotes
        data: '"$requestData"',
      );

      if (response.statusCode != HttpStatus.ok) throw Exception("Something went wrong syncing the scan logs");

      final result = ScanLogSyncResponse.fromJson(response.data);
      return result;
    } on DioException catch (_) {
      rethrow;
    } catch (e) {
      rethrow;
    }
  }
}
