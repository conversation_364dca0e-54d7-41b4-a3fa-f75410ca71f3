import 'dart:async';
import 'dart:collection';
import 'dart:isolate';

import 'package:flutter/services.dart';
import 'package:sort_pro_printer_flutter/app/utils/timer_mixin.dart';
import 'package:sort_pro_printer_flutter/services/logs/i_log_service.dart';
import 'package:sort_pro_printer_flutter/services/logs/log_service.dart';
import 'package:sort_pro_printer_flutter/services/scan_logs/i_scan_log_repository.dart';
import 'package:sort_pro_printer_flutter/services/scan_logs/i_scan_log_service.dart';
import 'package:sort_pro_printer_flutter/services/scan_logs/models/scan_log.dart';
import 'package:sort_pro_printer_flutter/services/scan_logs/scan_log_isolate_entrypoint.dart';
import 'package:uuid/uuid.dart';

class ScanLogService extends IScanLogService with TimerMixin {
  final IScanLogRepository scanLogRepository;
  final Queue<ScanLog> insertScanLogQueue = Queue<ScanLog>();
  int _scanLogCount = 0;

  static const maxScanLogsToUpload = 50;
  static const scanLogUploadIntervalInSeconds = 30;

  Isolate? isolate;
  String isolateId = "";
  StreamSubscription<dynamic>? isolateSubscription;

  bool insertingScanLog = false;

  ScanLogService(this.scanLogRepository);

  @override
  Future<void> enqueueScanLogUpSyncScanLogs() async {
    await purgeScanLogs();
    startTimer(const Duration(seconds: scanLogUploadIntervalInSeconds), () async {
      // nothing to do, isolate is doing its job already.
      if (isolate != null){
        LogService.instance.trace(LogLevel.information, "Cannot spawn new isolate. Isolate already running.", additionalProperties: {"IsolateId": isolateId});
        return;
      }
      await stopIsolate();
      startIsolate();
    });
  }

  @override
  Future<void> dequeueScanLogUpSyncScanLogs() async {
    cancelTimer();
  }

  @override
  dispose() {
    scanLogRepository.dispose();
  }

  @override
  Future<void> publishScanLogEvent(ScanLog scanLog) async {
    // add the scanlog into a queue for inserting in the db
    // we do this in case the db is busy at the time of inserting, so we can retry inserting after
    _queueScanLogForInsert(scanLog);
  }

  @override
  Future<void> purgeScanLogs() async {
    await scanLogRepository.deleteInvalidScanLogs();
  }

  _queueScanLogForInsert(ScanLog scanLog) async {
    // add the recently scanned barcode to the queue.
    insertScanLogQueue.add(scanLog);

    // if already inserting scanlogs then there's no need to re-run the loop
    if (insertingScanLog) return;

    insertingScanLog = true;

    // while the Queue is not empty, process them one by one
    while (insertScanLogQueue.isNotEmpty) {
      final log = insertScanLogQueue.removeFirst();
      final inserted = await scanLogRepository.insertScanLog(log);
      if (inserted) {
        _scanLogCount++;
        if (_scanLogCount == maxScanLogsToUpload) {
          LogService.instance.trace(LogLevel.information, "ScanLog limit reached", additionalProperties: {"limit": maxScanLogsToUpload});
          _scanLogCount = 0;
          // Interrupt the current sync if any, and upload the scan log file
          await stopIsolate();
          startIsolate();
        }
      } else {
        // Add it back into the queue
        insertScanLogQueue.add(scanLog);
      }
    }

    // done processing
    insertingScanLog = false;
  }

  Future<void> startIsolate() async {
    final receivePort = ReceivePort();
    final token = RootIsolateToken.instance!;
    isolateId = const Uuid().v1();
    LogService.instance.trace(LogLevel.information, "Spawning a new isolate", additionalProperties: {"IsolateId": isolateId});
    isolate = await Isolate.spawn(isolateEntryPoint, (token, receivePort.sendPort, isolateId));
    await isolateSubscription?.cancel();
    isolateSubscription = receivePort.listen(_onIsolateMessageReceived);
  }

  _onIsolateMessageReceived(dynamic message) {
    if(message == "terminated") {
      LogService.instance.trace(LogLevel.information,
          "onIsolateMessageReceived: ScanLog isolate terminated",
          additionalProperties: {"IsolateId": isolateId});
      isolate = null;
      isolateId = '';
      // re-queue when the isolate it's done.
      enqueueScanLogUpSyncScanLogs();
    }
  }   

  Future<void> stopIsolate() async {
    if (isolate == null) return;
    isolate!.kill(priority: Isolate.immediate);
    LogService.instance.trace(LogLevel.information, "ScanLog isolate killed", additionalProperties: {"IsolateId": isolateId});
    await isolateSubscription?.cancel();
    isolate = null;
    isolateId = '';
  }
}
