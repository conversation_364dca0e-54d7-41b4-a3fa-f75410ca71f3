import 'dart:isolate';

import 'package:dio/dio.dart';
import 'package:flutter/services.dart';
import 'package:sort_pro_printer_flutter/environment/environment_config.dart';
import 'package:sort_pro_printer_flutter/services/device_info/device_info_service.dart';
import 'package:sort_pro_printer_flutter/services/device_info/i_device_info_service.dart';
import 'package:sort_pro_printer_flutter/services/logs/i_log_service.dart';
import 'package:sort_pro_printer_flutter/services/logs/log_service.dart';
import 'package:sort_pro_printer_flutter/services/scan_logs/i_scan_log_repository.dart';
import 'package:sort_pro_printer_flutter/services/scan_logs/models/scan_log.dart';
import 'package:sort_pro_printer_flutter/services/scan_logs/scan_logs_server_client.dart';
import 'package:sort_pro_printer_flutter/services/scan_logs/scanlog_db_repository.dart';
import 'models/scan_log_sync_request.dart';

/// The isolate entry point
/// in charge of uploading the scanLogs
isolateEntryPoint((RootIsolateToken, SendPort, String) args) async {
  final id = args.$3;
  BackgroundIsolateBinaryMessenger.ensureInitialized(args.$1);
  final sendPort = args.$2;
  final logService = LogService.instance;
  final IDeviceInfoService deviceInfoService = DeviceInfoService();
  final appVersion = await deviceInfoService.getAppVersion();
  final deviceId = await deviceInfoService.getDeviceId();
  logService.setDeviceId(deviceId);
  logService.setAppVersion(appVersion);

  LogService.instance.trace(LogLevel.verbose, "LogFromIsolate: ScanLog isolate spawned", additionalProperties: {"IsolateId": id});

  terminate(String reason) async {
    LogService.instance
        .trace(LogLevel.verbose, "LogFromIsolate: Terminating isolate", additionalProperties: {"reason": reason, "IsolateId": id});
    await LogService.instance.flush();
    Isolate.exit(sendPort, "terminated");
  }

  late IScanLogRepository scanLogRepository;
  List<ScanLog> scanLogs = [];
  List<String> uploadedPins = [];
  List<String> failedPins = [];

  try {
    scanLogRepository = ScanLogDbRepository();
    scanLogs = await scanLogRepository.getScanLogs();
    if (scanLogs.isEmpty) {
      LogService.instance
          .trace(LogLevel.information, "LogFromIsolate: No ScanLog records to UpSync", additionalProperties: {"IsolateId": id});
      await terminate("No ScanLog records to UpSync");
    }
  } catch (e, s) {
    LogService.instance.error(e, s, false, additionalProperties: {"IsolateId": id});
    await terminate("Something went wrong getting the scanLogs");
  }

  try {
    LogService.instance
        .trace(LogLevel.verbose, "LogFromIsolate: Attempting to UpSync ScanLog records", additionalProperties: {"IsolateId": id});

    /// Prepare the client and request
    final scanLogServerClient = ScanLogsServerClient(
      EnvironmentConfig.sslwsDomain,
      EnvironmentConfig.sslwsUrl,
      EnvironmentConfig.sslwsUser,
      EnvironmentConfig.sslwsPassword,
    );

    LogService.instance.trace(LogLevel.verbose, "LogFromIsolate: ScanLog sync request information", additionalProperties: {
      "ssDeviceId": scanLogs.first.ssDeviceId ?? "",
      "userLoginID": scanLogs.first.userLoginId.toString(),
      "terminalId": scanLogs.first.terminalId ?? "",
      "IsolateId": id
    });

    final scanLogRequest = ScanLogSyncRequest(
      ssDeviceId: scanLogs.first.ssDeviceId ?? "",
      userLoginID: scanLogs.first.userLoginId.toString(),
      terminalId: scanLogs.first.terminalId ?? "",
      scanLogList: scanLogs,
    );

    /// Send the request
    LogService.instance.trace(LogLevel.verbose, "LogFromIsolate: Uploading ScanLogs", additionalProperties: {"IsolateId": id});
    final response = await scanLogServerClient.uploadScanLogs(scanLogRequest);

    failedPins = response.errorList.map((e) => e.pinThatFailed).toList();
    uploadedPins =
        scanLogs.map((e) => e.pin ?? "").toList().where((element) => !failedPins.contains(element) && element.isNotEmpty).toList();

    for (var element in uploadedPins) {
      LogService.instance
          .trace(LogLevel.verbose, "LogFromIsolate: ScanLog uploaded", additionalProperties: {"pin": element, "IsolateId": id});
    }

    /// delete all records related to the uploaded pins
    await scanLogRepository.deleteScanLogsByPin(uploadedPins);

    LogService.instance.trace(LogLevel.information, "LogFromIsolate: UpSync ScanLog Finished", additionalProperties: {
      "UploadedPins": "${uploadedPins.length}",
      "FailedPins": "${failedPins.length}",
      "IsolateId": id,
    });
  } catch (e, s) {
    LogService.instance.error(e, s, false, additionalProperties: {"IsolateId": id});
    if (e is DioException &&
        (DioExceptionType.receiveTimeout == e.type ||
            DioExceptionType.connectionTimeout == e.type ||
            DioExceptionType.connectionError == e.type)) {
      LogService.instance
          .trace(LogLevel.information, "LogFromIsolate: ScanLog server unreachable", additionalProperties: {"IsolateId": id});
    } else {
      failedPins = scanLogs.where((element) => element.pin != null).map((e) => e.pin!).toList();
    }
  } finally {
    /// Increment the RetryCount for all records related to the failed pins
    if (failedPins.isNotEmpty) {
      for (var element in failedPins) {
        LogService.instance
            .trace(LogLevel.verbose, "LogFromIsolate: ScanLog failed to upload", additionalProperties: {"pin": element, "IsolateId": id});
      }
      scanLogRepository.incrementRetryCountForPins(failedPins);
    }

    scanLogRepository.dispose(); // close the db
    LogService.instance.trace(LogLevel.verbose, "LogFromIsolate: Dispose ScanLogRepository", additionalProperties: {"IsolateId": id});
    terminate("Done uploading the scanLogs");
  }
}
