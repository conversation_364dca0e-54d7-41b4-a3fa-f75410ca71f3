import 'package:sort_pro_printer_flutter/app/remediate/models/delivery_info.dart';
import 'package:sort_pro_printer_flutter/db/ssws_database_manager.dart';
import 'package:sort_pro_printer_flutter/services/autocomplete/i_autocomplete_service.dart';

class RoutePlanAutocompleteService extends IAutocompleteService {
  final SswsDatabaseManager dbManager;

  RoutePlanAutocompleteService(this.dbManager);

  @override
  Future<List<String>> getAllCitiesForTerminal(String terminalId) async {
    final rpmDb = await dbManager.getRpmDatabase(terminalId);
    final List<Map<String, dynamic>> rpmQueryResult = rpmDb.select("SELECT TRIM(UPPER(Name)) AS Name FROM AllCities");
    rpmDb.dispose();
    return List.generate(rpmQueryResult.length, (i) => rpmQueryResult[i]["Name"]);
  }

  @override
  Future<List<String>> getAllPostalCodesForTerminal(String terminalId) async {
    final rpmDb = await dbManager.getRpmDatabase(terminalId);
    final List<Map<String, dynamic>> rpmQueryResult = rpmDb.select("SELECT TRIM(UPPER(PC)) AS PC FROM TerminalBoundaries");
    rpmDb.dispose();
    return List.generate(rpmQueryResult.length, (i) => rpmQueryResult[i]["PC"]);
  }

  @override
  Future<List<FullStreetDetails>> getAllAddressNamesForCity(String terminalId, String city) async {
    final rpmDb = await  dbManager.getRpmDatabase(terminalId);
    const String query = """
      SELECT TRIM(UPPER(StreetName)) as StreetName, 
             TRIM(UPPER(StreetType)) as StreetType, 
             TRIM(UPPER(StreetDirection)) as StreetDirection 
      FROM StreetNames 
      WHERE UPPER(CityName) LIKE UPPER(?)
    """;

    // We execute the query with the city, or using a wildcard if city is empty.
    final List<Map<String, dynamic>> rpmQueryResult = rpmDb.select(query, [city.isEmpty ? '%' : city]);

    rpmDb.dispose();
     return List.generate(rpmQueryResult.length, (i) => FullStreetDetails(rpmQueryResult[i]["StreetName"], rpmQueryResult[i]["StreetType"], rpmQueryResult[i]["StreetDirection"]));
  }
}
