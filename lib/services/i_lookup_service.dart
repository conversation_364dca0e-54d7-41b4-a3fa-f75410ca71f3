import 'dart:async';

import 'package:sort_pro_printer_flutter/app/pin_lookup/data/models/lookup_result.dart';
import 'package:sort_pro_printer_flutter/app/remediate/models/delivery_info.dart';
import 'package:sort_pro_printer_flutter/app/scanner/data/models/barcode.dart';
import 'package:sort_pro_printer_flutter/app/scanner/data/models/barcode_data.dart';
import 'package:sort_pro_printer_flutter/services/disposable.dart';

abstract class ILookupService implements Disposable {
  /// A stream system to be able to listen to lookup results from scanning parcels and remediation triage
  /// Currently used by the [ScanLogService] to publish ScanLogs
  final StreamController<LookupResult> _lookupResultStreamController = StreamController<LookupResult>.broadcast();

  Stream<LookupResult> get lookupResultStream => _lookupResultStreamController.stream;

  // in the event we call SLWSS when looking up sort instructions, we'll need everything we know from the barcode (not just pin and postal code)
  // we also pass in userId to be used as deviceId when calling SLWSS
  Future<LookupResult> lookUpSortInstructions(BarcodeData barcodeData, BarcodeType barcodeType, String terminalId, String userId);
  Future<LookupResult> lookUpSortByPin(String pin, String terminalId);
  Future<LookupResult> lookUpSortByPinAndPostalCode(String pin, String postalCode, String terminalId);
  Future<LookupResult> lookUpSortInstructionsByDeliveryInfo(DeliveryInfo deliveryInfo, String terminalId, String userId);

  notifyLookupResultFound(LookupResult result) {
    _lookupResultStreamController.add(result);
  }

  @override
  Future<void> dispose() async {
    _lookupResultStreamController.close();
  }
}
