import 'package:sort_pro_printer_flutter/extensions/string_nullable.dart';
import 'package:sort_pro_printer_flutter/app/remediate/models/delivery_info.dart';
import 'package:sort_pro_printer_flutter/app/scanner/data/models/barcode_data.dart';
import 'package:sort_pro_printer_flutter/db/ssws_database_manager.dart';

import 'package:sort_pro_printer_flutter/app/pin_lookup/data/models/lookup_result.dart';
import 'package:sort_pro_printer_flutter/app/scanner/data/models/barcode.dart';
import 'package:sort_pro_printer_flutter/services/data_provider/i_data_provider_service.dart';
import 'package:sort_pro_printer_flutter/services/i_lookup_service.dart';
import 'package:sort_pro_printer_flutter/db/dbModels/db_models.dart';
import 'package:sort_pro_printer_flutter/services/logs/i_log_service.dart';
import 'package:sort_pro_printer_flutter/services/logs/log_service.dart';
import 'package:sort_pro_printer_flutter/services/sslws/sslws_request.dart';
import 'package:sort_pro_printer_flutter/services/sslws/sslws_result.dart';
import 'package:sort_pro_printer_flutter/services/sslws/sslws_service.dart';
import 'package:sort_pro_printer_flutter/utils/postal_code_validator.dart';
import '../utils/remove_type_and_direction.dart';

class LookupService extends ILookupService {
  final SswsDatabaseManager dbManager;
  final SslwsService sslwsService;
  final IDataProviderService dataProviderService;
  final ILogService logService;

  LookupService(this.dbManager, this.sslwsService, this.logService, this.dataProviderService);



  @override
  Future<LookupResult> lookUpSortByPin(String pin, String terminalId) async {
    LookupResult? singleResult;

    try {
      singleResult = await _retrievePins(pin, terminalId);
      if (singleResult is LookupResult) {
        return singleResult;
      }
      singleResult = await _retrievePrePrints(pin, terminalId);
      if (singleResult is LookupResult) {
        return singleResult;
      }
      } catch (e, s) {
      LogService.instance.error(e, s, false);
    }

    // all checks failed
    return LookupResult(
      lookupResultType: LookupResultType.remediation,
      lookupResolvedBy: LookupResolvedBy.none,
      pin: pin,
      terminalNumber: terminalId,
    );
  }



  @override
  Future<LookupResult> lookUpSortByPinAndPostalCode(String pin, String postalCode, String terminalId) async {
    LookupResult? singleResult;
    try {
      singleResult = await lookUpSortByPin(pin, terminalId);
      if(singleResult.lookupResultType != LookupResultType.remediation) {
        return singleResult;
      }
      singleResult = await _retrievePostalCode(pin, postalCode, terminalId);
      if (singleResult is LookupResult) {
        return singleResult;
      }
    } catch (e, s) {
      LogService.instance.error(e, s, false);
    }
    return LookupResult(
      lookupResultType: LookupResultType.remediation,
      lookupResolvedBy: LookupResolvedBy.none,
      pin: pin,
      terminalNumber: terminalId,
    );
  }

  /// Used in Print&Scan Flow
  @override
  Future<LookupResult> lookUpSortInstructions(BarcodeData barcodeData, BarcodeType barcodeType, String terminalId, String userId) async {
    String pin = barcodeData.pinNumber!;
    String? postalCode = barcodeData.postalCode;

    try {
      LookupResult? singleResult = await _retrievePins(pin, terminalId);
      if (singleResult is LookupResult) {
        return singleResult;
      }

      if (barcodeType == BarcodeType.legacyPurolator || barcodeType == BarcodeType.manualWayBill) {
        singleResult = await _retrievePrePrints(pin, terminalId);
        if (singleResult is LookupResult) {
          return singleResult;
        }
      }

      if (barcodeType == BarcodeType.ngb || barcodeType == BarcodeType.puro2d || barcodeType == BarcodeType.upsMaxicode) {
        singleResult = await _retrievePostalCode(pin, postalCode, terminalId);
        if (singleResult is LookupResult) {
          return singleResult;
        }
      }

      if ((barcodeType == BarcodeType.ngb || barcodeType == BarcodeType.puro2d) && barcodeData.deliveryType == DeliveryType.hfpu.id.toString()) {
        singleResult = await _retrieveHFPU(pin, postalCode, terminalId);
        if (singleResult is LookupResult) {
          return singleResult;
        }
      }

      singleResult = await _retrieveFromSSLWS(
        pin: barcodeData.pinNumber!,
        terminalId: terminalId,
        userId: userId,
        customerName: barcodeData.customerName,
        suiteNumber: barcodeData.suiteNumber,
        streetNumber: barcodeData.streetNumber,
        addressLine1: barcodeData.addressLine1,
        addressLine2: barcodeData.addressLine2,
        postalCode: barcodeData.postalCode,
        deliveryTime: barcodeData.deliveryTime,
        shipmentType: barcodeData.shipmentType,
        deliveryType: barcodeData.deliveryType,
        diversionCode: barcodeData.diversionCode,
        handlingClassType: barcodeData.handlingClassType,
      );
      if (singleResult is LookupResult) {
        return singleResult;
      }
      // remaining checks have to do with postal code and addresses, cant do that with these barcode types
      if (barcodeType == BarcodeType.legacyPurolator || barcodeType == BarcodeType.manualWayBill) {
        return LookupResult(
          lookupResultType: LookupResultType.remediation,
          pin: pin,
          terminalNumber: terminalId,
          // although it wasnt quite resolved, this field has to point to webService to indicate we reached out and still got REM
          lookupResolvedBy: LookupResolvedBy.webService,
        );
      }

      singleResult = await _retrieveHVR(
        pin: pin,
        postalCode: barcodeData.postalCode ?? "",
        terminalId: terminalId,
        deliveryTimeCode: barcodeData.deliveryTime,
      );
      if (singleResult is LookupResult) {
        return singleResult;
      }

      if (barcodeType == BarcodeType.puro2d) {
        final rpmData = await dataProviderService.getDataDescriptionForTerminal(terminalId);
        final listOfDirections = rpmData?.streetDirections ?? const [];
        final listOfTypes = rpmData?.streetTypes ?? const [];
        singleResult = await _retrieveAddressParsing(
          pin: barcodeData.pinNumber ?? "",
          terminalId: terminalId,
          streetNumber: barcodeData.streetNumber ?? "",
          streetName: RemoveTypeAndDirection.cleanAddress(barcodeData.addressLine1 ?? "",
                      listOfDirections: listOfDirections, listOfTypes: listOfTypes)
                  .streetName ??
              "",
          municipalityName: barcodeData.city ?? "",
          postalCode: barcodeData.postalCode ?? "",
          customerName: barcodeData.customerName ?? "",
          unitNumber: barcodeData.suiteNumber ?? "",
        );
        if (singleResult is LookupResult) {
          return singleResult;
        }
      }
    } catch (e, s) {
      LogService.instance.error(e, s, false);
    }

    // all checks failed
    return LookupResult(
      lookupResultType: LookupResultType.remediation,
      // to indicate we reached out to webService and still got no route/shelf
      lookupResolvedBy: LookupResolvedBy.webService,
      pin: pin,
      terminalNumber: terminalId,
    );
  }

  /// Used in Remediation Flow
  @override
  Future<LookupResult> lookUpSortInstructionsByDeliveryInfo(DeliveryInfo deliveryInfo, String terminalId, String userId) async {
    String pin = deliveryInfo.pin;
    String? postalCode = deliveryInfo.deliveryAddress.postalCode;
    if (postalCode != null) postalCode = postalCode.replaceAll(" ", "");
    LookupResult? singleResult;

    try {
      singleResult = await _retrievePins(pin, terminalId);
      if (singleResult is LookupResult) {
        return singleResult;
      }

      singleResult = await _retrievePostalCode(pin, postalCode, terminalId);
      if (singleResult is LookupResult) {
        return singleResult;
      }

      if (deliveryInfo.deliveryDetails.deliveryType == DeliveryType.hfpu) {
        singleResult = await _retrieveHFPU(pin, postalCode, terminalId);
        if (singleResult is LookupResult) {
          return singleResult;
        }
      }

      singleResult = await _retrieveFromSSLWS(
        pin: pin,
        terminalId: terminalId,
        userId: userId,
        customerName: deliveryInfo.deliveryAddress.customerName,
        suiteNumber: deliveryInfo.deliveryAddress.suiteNumber,
        streetNumber: deliveryInfo.deliveryAddress.streetNumber,
        addressLine1: deliveryInfo.deliveryAddress.addressLine1,
        addressLine2: deliveryInfo.deliveryAddress.addressLine2,
        postalCode: deliveryInfo.deliveryAddress.postalCode,
        deliveryTime: deliveryInfo.deliveryDetails.deliveryTime.id,
        shipmentType: deliveryInfo.deliveryDetails.shipmentType,
        deliveryType: deliveryInfo.deliveryDetails.deliveryType.id.toString(),
        diversionCode: deliveryInfo.deliveryDetails.diversionCode.id.toString(),
        handlingClassType: deliveryInfo.deliveryDetails.handlingClassType,
      );
      if (singleResult is LookupResult) {
        return singleResult;
      }

      singleResult = await _retrieveHVR(
        pin: pin,
        postalCode: postalCode ?? "",
        terminalId: terminalId,
        deliveryTimeCode: deliveryInfo.deliveryDetails.deliveryTime.id,
      );
      if (singleResult is LookupResult) {
        return singleResult;
      }

      singleResult = await _retrieveAddressParsing(
        pin: deliveryInfo.pin,
        terminalId: terminalId,
        streetNumber: deliveryInfo.deliveryAddress.streetNumber ?? "",
        streetName: deliveryInfo.deliveryAddress.streetName ?? "",
        municipalityName: deliveryInfo.deliveryAddress.city ?? "",
        postalCode: deliveryInfo.deliveryAddress.postalCode ?? "",
        customerName: deliveryInfo.deliveryAddress.customerName ?? "",
        unitNumber: deliveryInfo.deliveryAddress.suiteNumber ?? "",
      );
      if (singleResult is LookupResult) {
        return singleResult;
      }
    } catch (e, s) {
      LogService.instance.error(e, s, false);
    }

    // all checks failed
    return LookupResult(
      lookupResultType: LookupResultType.remediation,
      // to indicate we reached out to webService and still got no route/shelf
      lookupResolvedBy: LookupResolvedBy.webService,
      pin: pin,
      terminalNumber: terminalId,
    );
  }

  Future<LookupResult?> _retrievePins(String pin, String terminalId) async {
    final pinDb = await dbManager.getPinDatabase(terminalId);
    final List<Map<String, dynamic>> pins = pinDb.select("SELECT * FROM PIN WHERE PIN='$pin'");
    var results = List.generate(pins.length, (i) {
      return PIN(
        terminalId: pins[i]['TerminalID'],
        pIN: pins[i]['PIN'],
        primarySort: pins[i]['PrimarySort'],
        sideOfBelt: pins[i]['SideofBelt'],
        routeNumber: pins[i]['RouteNumber'],
        shelfNumber: pins[i]['ShelfNumber'],
        truckShelfOverride: pins[i]['TruckShelfOverride'],
        deliverySequenceId: pins[i]['DeliverySequenceID'],
      );
    });
    pinDb.dispose();

    if (results.isEmpty) {
      return null; //nothing found in pin.db3
    }
    if (results.length == 1 && results.first.routeNumber != "SRR") {
      // means we got 1 good hit, return routing label
      return LookupResult(
        lookupResolvedBy: LookupResolvedBy.pinFile,
        lookupResultType: LookupResultType.routing,
        pin: results.first.pIN,
        terminalNumber: terminalId,
        pudroNumber: results.first.primarySort,
        conveyorSide: results.first.sideOfBelt,
        routeNumber: results.first.routeNumber,
        shelfNumber: results.first.shelfNumber,
        truckShelfOverride: results.first.truckShelfOverride,
        sequenceNumber: results.first.deliverySequenceId,
      );
    } else if (results.length > 1 || results.first.routeNumber == "SRR") {
      // return SRR label if we got multiple matches from db or if routeNumber is SRR
      return LookupResult(
        lookupResolvedBy: LookupResolvedBy.pinFile,
        lookupResultType: LookupResultType.srrA, // If Pin file contains RouteNumber=SRR, when SSMA scans the pin
        pin: results.first.pIN,
        terminalNumber: terminalId,
      );
    }
    // catch all
    return null;
  }

  Future<LookupResult?> _retrievePrePrints(String pin, String terminalId) async {
    final prePrintDb = await dbManager.getPrePrintDatabase(terminalId);
    final List<Map<String, dynamic>> prePrints = prePrintDb.select("SELECT * FROM PrePrintPIN WHERE FromPIN<='$pin' AND ToPIN>='$pin'");
    var results = List.generate(prePrints.length, (i) {
      return PrePrint(
        terminalId: prePrints[i]['TerminalID'],
        prePrintId: prePrints[i]['PrePrintID'],
        fromPIN: prePrints[i]['FromPIN'],
        toPIN: prePrints[i]['ToPIN'],
        primarySort: prePrints[i]['PrimarySort'],
        sideOfBelt: prePrints[i]['SideofBelt'],
        routeNumber: prePrints[i]['RouteNumber'],
        shelfNumber: prePrints[i]['ShelfNumber'],
        truckShelfOverride: prePrints[i]['TruckShelfOverride'],
        deliverySequenceId: prePrints[i]['DeliverySequenceID'],
      );
    });
    prePrintDb.dispose();

    if (results.length == 1) {
      // 1 good match found in preprint file, return routing label
      return LookupResult(
        lookupResolvedBy: LookupResolvedBy.prePrintFile,
        lookupResultType: LookupResultType.routing,
        pin: pin,
        terminalNumber: terminalId,
        pudroNumber: results.first.primarySort,
        conveyorSide: results.first.sideOfBelt,
        routeNumber: results.first.routeNumber,
        shelfNumber: results.first.shelfNumber,
        truckShelfOverride: results.first.truckShelfOverride,
        sequenceNumber: results.first.deliverySequenceId,
        prePrintId: results.first.prePrintId,
      );
    } else if (results.length > 1) {
      // multiple matches means we should return SRR label
      return LookupResult(
        lookupResolvedBy: LookupResolvedBy.prePrintFile,
        lookupResultType: LookupResultType.srr,
        pin: pin,
        prePrintId: results.first.prePrintId,
        terminalNumber: terminalId,
      );
    }
    // nothing found in preprint.db3 file
    return null;
  }

  Future<LookupResult?> _retrievePostalCode(String pin, String? postalCode, String terminalId) async {
    if (!PostalCodeValidator.validatePostalCode(postalCode ?? "")) return null;
    final rpmDb = await dbManager.getRpmDatabase(terminalId);

    // Look for Postal Code and Route SRR match
    final List<Map<String, dynamic>> srr = rpmDb.select(
        "SELECT PostalCode from RoutePlan where PostalCode='$postalCode' AND Lower(AddressRecordType) = LOWER('PORRGD') AND Upper(RouteNumber) = 'SRR'");
    if (srr.isNotEmpty) {
      rpmDb.dispose();
      return LookupResult(
        lookupResolvedBy: LookupResolvedBy.localRemediation,
        lookupResultType: LookupResultType.srrP,
        pin: pin,
        terminalNumber: terminalId,
        routeNumber: "SRR",
        postalCode: postalCode,
      );
    }

    final List<Map<String, dynamic>> crossDock = rpmDb.select("SELECT Unicode FROM CrossDock WHERE PC='$postalCode'");
    if (crossDock.isNotEmpty) {
      String routeNumber = crossDock.first.values.first;
      rpmDb.dispose();
      return LookupResult(
        lookupResolvedBy: LookupResolvedBy.localRemediation,
        lookupResultType: LookupResultType.crossDock,
        pin: pin,
        terminalNumber: terminalId,
        routeNumber: routeNumber,
        postalCode: postalCode,
      );
    }

    // postal code is not cross dock, next we check if postal code is within terminal boundaries
    final List<Map<String, dynamic>> terminalBoundaries = rpmDb.select("SELECT COUNT(PC) FROM TerminalBoundaries WHERE PC='$postalCode'");
    int numberOfTerminalBoundaries = terminalBoundaries.first.values.first;
    if (numberOfTerminalBoundaries == 0) {
      // return misdirect label if postal code is not found in RPM.TerminalBoundaries table
      rpmDb.dispose();
      return LookupResult(
          lookupResolvedBy: LookupResolvedBy.none,
          lookupResultType: LookupResultType.misdirect,
          pin: pin,
          terminalNumber: terminalId,
          postalCode: postalCode);
    }

    rpmDb.dispose();
    return null; // postal code is valid but it's not HFPU
  }

  Future<LookupResult?> _retrieveHFPU(String pin, String? postalCode, String terminalId) async {
    if (postalCode == null) return null;
    final hfpuLocDb = await dbManager.getHFPULocToPCDatabase(terminalId);
    final List<Map<String, dynamic>> hfpuLocations =
        hfpuLocDb.select("SELECT DISTINCT HFPULocationID FROM HFPULocationToPC WHERE PostalCode='$postalCode'");
    hfpuLocDb.dispose();
    if (hfpuLocations.isEmpty) {
      return null; // delivery not marked for HFPU
    }

    // delivery is HFPU (hold for pickup), now we need to check for a match
    String hfpuLocationId = hfpuLocations.first.values.first;
    final hfpuDb = await dbManager.getHFPULocationMasterDatabase(terminalId);
    final List<Map<String, dynamic>> hfpuResult = hfpuDb.select("SELECT * FROM HFPULocationMaster WHERE HFPULocationID='$hfpuLocationId'");
    hfpuDb.dispose();

    if (hfpuResult.isEmpty) {
      // return SRR if HFPU location ID (from hfpuLocToPC) is not in HFPULocationMaster db
      return LookupResult(
        lookupResolvedBy: LookupResolvedBy.barcodeParsing,
        lookupResultType: LookupResultType.srr,
        pin: pin,
        terminalNumber: terminalId,
      );
    }

    // look up RoutePlan from RPM through HFPUlocationID, then use the route from there to lookup parking plan
    if (hfpuResult.length == 1) {
      // return routing label for the given HFPU location from HFPULocationMaster
      final rpmDb = await dbManager.getRpmDatabase(terminalId);
      final List<Map<String, dynamic>> rpmQueryResult =
          rpmDb.select("SELECT * FROM RoutePlan WHERE HFPULocationID='$hfpuLocationId' AND AddressRecordType='HFPU'");
      rpmDb.dispose();
      var rpmResults = List.generate(rpmQueryResult.length, (i) {
        return RoutePlan(
          terminalId: rpmQueryResult[i]['TerminalID'],
          routePlanId: rpmQueryResult[i]['RoutePlanID'],
          routePlanVersionId: rpmQueryResult[i]['RoutePlanVersionID'],
          addressRecordType: rpmQueryResult[i]['AddressRecordType'],
          postalCode: rpmQueryResult[i]['PostalCode'],
          streetName: rpmQueryResult[i]['StreetName'],
          streetDirection: rpmQueryResult[i]['StreetDirection'],
          streetType: rpmQueryResult[i]['StreetType'],
          fromStreetNumber: rpmQueryResult[i]['FromStreetNumber'].toString(),
          toStreetNumber: rpmQueryResult[i]['ToStreetNumber'].toString(),
          routeNumber: rpmQueryResult[i]['RouteNumber'],
          shelfNumber: rpmQueryResult[i]['ShelfNumber'],
          truckShelfOverride: rpmQueryResult[i]['TruckShelfOverride'],
          deliverySequenceId: rpmQueryResult[i]['DeliverySequenceID'],
          provinceCode: rpmQueryResult[i]['ProvinceCode'],
          municipalityName: rpmQueryResult[i]['MunicipalityName'],
        );
      });
      if (rpmResults.isEmpty) {
        return null;
      }

      final parkingPlanDb = await dbManager.getParkingPlanDatabase(terminalId);
      final List<Map<String, dynamic>> parkingPlanQueryResult =
          parkingPlanDb.select("SELECT * FROM RouteMaster WHERE RouteNumber='${rpmResults.first.routeNumber}'");
      parkingPlanDb.dispose();
      var parkingPlanResults = List.generate(parkingPlanQueryResult.length, (i) {
        return ParkingPlan(
          terminalId: parkingPlanQueryResult[i]['TerminalID'],
          parkingPlanId: parkingPlanQueryResult[i]['ParkingPlanID'],
          routeNumber: parkingPlanQueryResult[i]['RouteNumber'],
          primarySort: parkingPlanQueryResult[i]['PrimarySort'],
          sideOfBelt: parkingPlanQueryResult[i]['SideofBelt'],
        );
      });

      LookupResult result = LookupResult(
        streetName: rpmResults.first.streetName,
        streetType: rpmResults.first.streetType,
        fromStreetNumber: rpmResults.first.fromStreetNumber,
        streetDir: rpmResults.first.streetDirection,
        lookupResolvedBy: LookupResolvedBy.localRemediation,
        lookupResultType: LookupResultType.routing,
        pin: pin,
        terminalNumber: terminalId,
        pudroNumber: parkingPlanResults.first.primarySort,
        conveyorSide: parkingPlanResults.first.sideOfBelt,
        routeNumber: rpmResults.first.routeNumber,
        shelfNumber: rpmResults.first.shelfNumber,
        truckShelfOverride: rpmResults.first.truckShelfOverride,
        sequenceNumber: rpmResults.first.deliverySequenceId,
        routePlanId: rpmResults.first.routePlanVersionId,
      );
      result = assignExtraFieldsFromDatabaseToLookupResult(rpmQueryResult.first, result);
      result = assignExtraFieldsFromDatabaseToLookupResult(parkingPlanQueryResult.first, result);
      return result;
    }
    return null; // nothing found
  }

  Future<LookupResult?> _retrieveFromSSLWS({
    required String pin,
    required String terminalId,
    required String userId,
    String? customerName,
    String? suiteNumber,
    String? streetNumber,
    String? addressLine1,
    String? addressLine2,
    String? postalCode,
    String? deliveryTime,
    String? shipmentType,
    String? deliveryType,
    String? diversionCode,
    String? handlingClassType,
  }) async {
    SslwsRequest request = SslwsRequest(
      terminalId: terminalId,
      pin: pin,
      customerName: customerName,
      deviceId: userId,
      unitNumber: suiteNumber,
      streetNumber: streetNumber,
      addressLine1: addressLine1,
      addressLine2: addressLine2,
      postalCode: postalCode,
      premiumService: deliveryTime,
      shipmentType: shipmentType,
      deliveryType: deliveryType,
      diversionCode: diversionCode,
      handlingClassType: handlingClassType,
    );
    SslwsResult? result;
    try {
      result = await sslwsService.getSSLWData(request, retryOn400Failure: true);
    } catch (e, s) {
      logService.error(e, s, false);
    }
    if (result == null) return null;

    switch (result.statusCode) {
      case SslwsStatusCode.resolved:
        if (result.routeNumber.isNullOrEmpty()) {
          return null;
        }
        return LookupResult(
          lookupResolvedBy: LookupResolvedBy.webService,
          lookupResultType: LookupResultType.routing,
          pin: pin,
          terminalNumber: terminalId,
          pudroNumber: result.primarySort,
          conveyorSide: result.conveyerSideIdentifier,
          routeNumber: result.routeNumber,
          shelfNumber: result.shelfNumber,
          truckShelfOverride: result.truckShelfOverride,
          sequenceNumber: result.deliverySequenceID,
          postalCode: postalCode,
        );
      case SslwsStatusCode.cd:
        return LookupResult(
          lookupResolvedBy: LookupResolvedBy.webService,
          lookupResultType: LookupResultType.crossDock,
          pin: pin,
          terminalNumber: terminalId,
          routeNumber: result.routeNumber,
          postalCode: postalCode,
        );
      case SslwsStatusCode.srr:
        return LookupResult(
          lookupResolvedBy: LookupResolvedBy.webService,
          lookupResultType: LookupResultType.srr,
          pin: pin,
          terminalNumber: terminalId,
          postalCode: postalCode,
        );
      case SslwsStatusCode.mdr:
        if(postalCode.isNullOrEmpty()) {
          return LookupResult(
            lookupResultType: LookupResultType.remediation,
            pin: pin,
            terminalNumber: terminalId,
            lookupResolvedBy: LookupResolvedBy.webService,
          );
        }
        return LookupResult(
          lookupResolvedBy: LookupResolvedBy.webService,
          lookupResultType: LookupResultType.misdirect,
          pin: pin,
          terminalNumber: terminalId,
          postalCode: postalCode,
        );
      case SslwsStatusCode.error:
      case SslwsStatusCode.rem:
        return null; // instead of returning REM label here, we want to continue with the flow
    }
  }

  Future<LookupResult?> _retrieveHVR({
    required String postalCode,
    required String terminalId,
    required String pin,
    String? deliveryTimeCode,
  }) async {
    // NOTE: this logic is copied straight from legacy app
    String deliveryTime = _checkDeliveryTime(deliveryTimeCode);
    String uniqueRecordPrioritySql =
        "SELECT COUNT(PostalCode) FROM RoutePlan WHERE PostalCode = '$postalCode' AND AddressRecordType IN ('UniqueUnit','UniqueAddress') AND ServicePriority='$deliveryTime'";
    String uniqueRecordStdSql =
        "SELECT COUNT(PostalCode) FROM RoutePlan WHERE PostalCode = '$postalCode' AND AddressRecordType IN ('UniqueUnit','UniqueAddress') AND ServicePriority='STD'";
    String rangeSql =
        "SELECT COUNT(PostalCode) FROM RoutePlan WHERE PostalCode = '$postalCode' AND AddressRecordType IN ('UnitRange','AddressRange')";
    String sqlCommand =
        "Select ($uniqueRecordPrioritySql) as UniqueRecordPriority, ($uniqueRecordStdSql) as UniqueRecordSTD, ($rangeSql) as Range";

    final rpmDb = await dbManager.getRpmDatabase(terminalId);
    final List<Map<String, dynamic>> results = rpmDb.select(sqlCommand);
    int uniqueRecordPriority = results.first['UniqueRecordPriority'];
    int uniqueRecordSTD = results.first['UniqueRecordSTD'];
    int range = results.first['Range'];

    if (uniqueRecordPriority == 1 && range == 0) {
      // to get a routing label, we must query RPM db for shelf+route then parking plan for sideOfBelt and primarySort
      final List<Map<String, dynamic>> rpmQueryResult = rpmDb.select(
          "SELECT DISTINCT * FROM RoutePlan WHERE PostalCode='$postalCode' AND AddressRecordType IN ('UniqueUnit','UniqueAddress') AND ServicePriority='$deliveryTime'");
      rpmDb.dispose();
      var rpmResults = List.generate(rpmQueryResult.length, (i) {
        return RoutePlan(
          terminalId: rpmQueryResult[i]['TerminalID'],
          routePlanId: rpmQueryResult[i]['RoutePlanID'],
          routePlanVersionId: rpmQueryResult[i]['RoutePlanVersionID'],
          addressRecordType: rpmQueryResult[i]['AddressRecordType'],
          postalCode: rpmQueryResult[i]['PostalCode'],
          streetName: rpmQueryResult[i]['StreetName'],
          streetDirection: rpmQueryResult[i]['StreetDirection'],
          streetType: rpmQueryResult[i]['StreetType'],
          fromStreetNumber: rpmQueryResult[i]['FromStreetNumber'].toString(),
          toStreetNumber: rpmQueryResult[i]['ToStreetNumber'].toString(),
          routeNumber: rpmQueryResult[i]['RouteNumber'],
          shelfNumber: rpmQueryResult[i]['ShelfNumber'],
          truckShelfOverride: rpmQueryResult[i]['TruckShelfOverride'],
          deliverySequenceId: rpmQueryResult[i]['DeliverySequenceID'],
          provinceCode: rpmQueryResult[i]['ProvinceCode'],
          municipalityName: rpmQueryResult[i]['MunicipalityName'],
        );
      });

      final parkingPlanDb = await dbManager.getParkingPlanDatabase(terminalId);
      final List<Map<String, dynamic>> parkingPlanQueryResult =
          parkingPlanDb.select("SELECT * FROM RouteMaster WHERE RouteNumber='${rpmResults.first.routeNumber}'");
      parkingPlanDb.dispose();
      var parkingPlanResults = List.generate(parkingPlanQueryResult.length, (i) {
        return ParkingPlan(
          terminalId: parkingPlanQueryResult[i]['TerminalID'],
          parkingPlanId: parkingPlanQueryResult[i]['ParkingPlanID'],
          routeNumber: parkingPlanQueryResult[i]['RouteNumber'],
          primarySort: parkingPlanQueryResult[i]['PrimarySort'],
          sideOfBelt: parkingPlanQueryResult[i]['SideofBelt'],
        );
      });

      LookupResult result = LookupResult(
        streetName: rpmResults.first.streetName,
        streetType: rpmResults.first.streetType,
        fromStreetNumber: rpmResults.first.fromStreetNumber,
        streetDir: rpmResults.first.streetDirection,
        lookupResolvedBy: LookupResolvedBy.barcodeParsing,
        lookupResultType: LookupResultType.routing,
        pin: pin,
        terminalNumber: terminalId,
        pudroNumber: parkingPlanResults.first.primarySort,
        conveyorSide: parkingPlanResults.first.sideOfBelt,
        routeNumber: rpmResults.first.routeNumber,
        shelfNumber: rpmResults.first.shelfNumber,
        truckShelfOverride: rpmResults.first.truckShelfOverride,
        sequenceNumber: rpmResults.first.deliverySequenceId,
        routePlanId: rpmResults.first.routePlanVersionId,
      );
      result = assignExtraFieldsFromDatabaseToLookupResult(rpmQueryResult.first, result);
      result = assignExtraFieldsFromDatabaseToLookupResult(parkingPlanQueryResult.first, result);
      return result;
    } else if (uniqueRecordPriority == 0 && uniqueRecordSTD == 1 && range == 0) {
      final List<Map<String, dynamic>> rpmQueryResult = rpmDb.select(
          "SELECT DISTINCT * FROM RoutePlan WHERE PostalCode='$postalCode' AND AddressRecordType IN ('UniqueUnit','UniqueAddress') AND ServicePriority='STD'");
      rpmDb.dispose();
      var rpmResults = List.generate(rpmQueryResult.length, (i) {
        return RoutePlan(
          terminalId: rpmQueryResult[i]['TerminalID'],
          routePlanId: rpmQueryResult[i]['RoutePlanID'],
          routePlanVersionId: rpmQueryResult[i]['RoutePlanVersionID'],
          addressRecordType: rpmQueryResult[i]['AddressRecordType'],
          postalCode: rpmQueryResult[i]['PostalCode'],
          streetName: rpmQueryResult[i]['StreetName'],
          streetDirection: rpmQueryResult[i]['StreetDirection'],
          streetType: rpmQueryResult[i]['StreetType'],
          fromStreetNumber: rpmQueryResult[i]['FromStreetNumber'].toString(),
          toStreetNumber: rpmQueryResult[i]['ToStreetNumber'].toString(),
          routeNumber: rpmQueryResult[i]['RouteNumber'],
          shelfNumber: rpmQueryResult[i]['ShelfNumber'],
          truckShelfOverride: rpmQueryResult[i]['TruckShelfOverride'],
          deliverySequenceId: rpmQueryResult[i]['DeliverySequenceID'],
          provinceCode: rpmQueryResult[i]['ProvinceCode'],
          municipalityName: rpmQueryResult[i]['MunicipalityName'],
        );
      });

      final parkingPlanDb = await dbManager.getParkingPlanDatabase(terminalId);
      final List<Map<String, dynamic>> parkingPlanQueryResult =
          parkingPlanDb.select("SELECT * FROM RouteMaster WHERE RouteNumber='${rpmResults.first.routeNumber}'");
      parkingPlanDb.dispose();
      var parkingPlanResults = List.generate(parkingPlanQueryResult.length, (i) {
        return ParkingPlan(
          terminalId: parkingPlanQueryResult[i]['TerminalID'],
          parkingPlanId: parkingPlanQueryResult[i]['ParkingPlanID'],
          routeNumber: parkingPlanQueryResult[i]['RouteNumber'],
          primarySort: parkingPlanQueryResult[i]['PrimarySort'],
          sideOfBelt: parkingPlanQueryResult[i]['SideofBelt'],
        );
      });

      LookupResult result = LookupResult(
        streetName: rpmResults.first.streetName,
        streetType: rpmResults.first.streetType,
        fromStreetNumber: rpmResults.first.fromStreetNumber,
        streetDir: rpmResults.first.streetDirection,
        lookupResolvedBy: LookupResolvedBy.barcodeParsing,
        lookupResultType: LookupResultType.routing,
        pin: pin,
        terminalNumber: terminalId,
        pudroNumber: parkingPlanResults.first.primarySort,
        conveyorSide: parkingPlanResults.first.sideOfBelt,
        routeNumber: rpmResults.first.routeNumber,
        shelfNumber: rpmResults.first.shelfNumber,
        truckShelfOverride: rpmResults.first.truckShelfOverride,
        sequenceNumber: rpmResults.first.deliverySequenceId,
        routePlanId: rpmResults.first.routePlanVersionId,
      );
      result = assignExtraFieldsFromDatabaseToLookupResult(rpmQueryResult.first, result);
      result = assignExtraFieldsFromDatabaseToLookupResult(parkingPlanQueryResult.first, result);
      return result;
    }

    // not HVR postal code
    return null;
  }

  Future<LookupResult?> _retrieveAddressParsing({
    required String terminalId,
    required String pin,
    required String streetNumber,
    required String streetName,
    required String municipalityName,
    required String postalCode,
    required String customerName,
    required String unitNumber,
  }) async {
    // NOTE: this logic is copied straight from legacy app
    final rpmDb = await dbManager.getRpmDatabase(terminalId);
    if (streetName.isEmpty) {
      final List<Map<String, dynamic>> rpmQueryResult = rpmDb.select(
          "SELECT COUNT(DISTINCT RouteNumber || ShelfNumber) FROM RoutePlan WHERE PostalCode='$postalCode' AND AddressRecordType NOT IN ('PostalCode', 'HFPU')");
      rpmDb.dispose();
      int numberOfRouteShelf = rpmQueryResult.first['COUNT(DISTINCT RouteNumber || ShelfNumber)'];
      bool isSingleRouteShelf = numberOfRouteShelf == 1 ? true : false;
      if (isSingleRouteShelf) {
        return null;
      }
      return LookupResult(
        lookupResultType: LookupResultType.remediation,
        pin: pin,
        terminalNumber: terminalId,
        lookupResolvedBy: LookupResolvedBy.webService,
      );
    }

    String postalCodeSql = "PostalCode='$postalCode'";
    String streetNumberSql = "AND '$streetNumber' BETWEEN FromStreetNumber AND ToStreetNumber";
    String sequenceCodeSql = "AND (('$streetNumber' % 2 = 1 AND AddressSequenceCode IN ('O', 'A', '')) OR ('$streetNumber' % 2 = 0 AND AddressSequenceCode IN ('E', 'A', '')))";
    String streetNameSql = "AND StreetName='${streetName.replaceAll("'", "''")}'";
    String citySql = "AND MunicipalityName='${municipalityName.replaceAll("'", "''")}'";
    String customerNameSql = "AND CustomerName='${customerName.replaceAll("'", "''")}'";
    String unitNumberSql;
    if (unitNumber.isEmpty) {
      // look for empty unit numbers
      unitNumberSql = "AND FromUnitNumber = '' AND ToUnitNumber = ''";
    } else if (int.tryParse(unitNumber) != null) {
      // look in between numeric unit numbers
      unitNumberSql =
          "AND CAST(COALESCE('$unitNumber', '0') AS INTEGER) BETWEEN CAST(COALESCE(FromUnitNumber, '0') AS INTEGER) AND CAST(COALESCE(ToUnitNumber, '0') AS INTEGER)";
    } else {
      // look in between alpha-numeric unit numbers
      // We also make sure the FromUnitNumber and ToUnitNumber are not only numbers but also alphanumeric as well
      unitNumberSql = "AND '$unitNumber' BETWEEN FromUnitNumber AND ToUnitNumber "
          "AND FromUnitNumber GLOB '*[a-zA-Z]*' AND FromUnitNumber GLOB '*[0-9]*' "
          "AND ToUnitNumber GLOB '*[a-zA-Z]*' AND ToUnitNumber GLOB '*[0-9]*'";
    }
    // makes it easy to copy-paste from debugger into an SQLite editor
    String query = 'SELECT * FROM RoutePlan WHERE $postalCodeSql $streetNumberSql $sequenceCodeSql $streetNameSql $citySql $customerNameSql $unitNumberSql';
    // Address lookup
    final List<Map<String, dynamic>> rpmQueryResult = rpmDb.select(query);
    rpmDb.dispose();
    if (rpmQueryResult.length == 1) {
      var rpmResults = List.generate(rpmQueryResult.length, (i) {
        return RoutePlan(
          terminalId: rpmQueryResult[i]['TerminalID'],
          routePlanId: rpmQueryResult[i]['RoutePlanID'],
          routePlanVersionId: rpmQueryResult[i]['RoutePlanVersionID'],
          addressRecordType: rpmQueryResult[i]['AddressRecordType'],
          postalCode: rpmQueryResult[i]['PostalCode'],
          streetName: rpmQueryResult[i]['StreetName'],
          streetDirection: rpmQueryResult[i]['StreetDirection'],
          streetType: rpmQueryResult[i]['StreetType'],
          fromStreetNumber: rpmQueryResult[i]['FromStreetNumber'].toString(),
          toStreetNumber: rpmQueryResult[i]['ToStreetNumber'].toString(),
          routeNumber: rpmQueryResult[i]['RouteNumber'],
          shelfNumber: rpmQueryResult[i]['ShelfNumber'],
          truckShelfOverride: rpmQueryResult[i]['TruckShelfOverride'],
          deliverySequenceId: rpmQueryResult[i]['DeliverySequenceID'],
          provinceCode: rpmQueryResult[i]['ProvinceCode'],
          municipalityName: rpmQueryResult[i]['MunicipalityName'],
        );
      });

      final parkingPlanDb = await dbManager.getParkingPlanDatabase(terminalId);
      final List<Map<String, dynamic>> parkingPlanQueryResult =
          parkingPlanDb.select("SELECT * FROM RouteMaster WHERE RouteNumber='${rpmResults.first.routeNumber}'");
      parkingPlanDb.dispose();
      var parkingPlanResults = List.generate(parkingPlanQueryResult.length, (i) {
        return ParkingPlan(
          terminalId: parkingPlanQueryResult[i]['TerminalID'],
          parkingPlanId: parkingPlanQueryResult[i]['ParkingPlanID'],
          routeNumber: parkingPlanQueryResult[i]['RouteNumber'],
          primarySort: parkingPlanQueryResult[i]['PrimarySort'],
          sideOfBelt: parkingPlanQueryResult[i]['SideofBelt'],
        );
      });

      return LookupResult(
        lookupResultType: LookupResultType.routing,
        pin: pin,
        terminalNumber: terminalId,
        pudroNumber: parkingPlanResults.first.primarySort,
        conveyorSide: parkingPlanResults.first.sideOfBelt,
        routeNumber: rpmResults.first.routeNumber,
        shelfNumber: rpmResults.first.truckShelfOverride != "" ? rpmResults.first.truckShelfOverride : rpmResults.first.shelfNumber,
        sequenceNumber: rpmResults.first.deliverySequenceId,
        lookupResolvedBy: LookupResolvedBy.barcodeParsing,
      );
    }

    // address parsing check failed
    return null;
  }

  String _checkDeliveryTime(String? deliveryTime) {
    switch (deliveryTime) {
      case "25":
      case "0900":
        return "0900";
      case "28":
      case "1030":
        return "1030";
      case "29":
      case "EVN":
        return "EVN";
      case "12":
      case "1200":
        return "1200";
      default:
        return "STD";
    }
  }

  // Implementation of their "AssignDataElementsValue" function that overrides stuff from the databases for the ScanLog
  // I wish I didn't have to do this
  // Seems to only be applicable for the ParkingPlan db and the RoutePlan db
  LookupResult assignExtraFieldsFromDatabaseToLookupResult(Map<String, dynamic> databaseTableResults, LookupResult result) {
    final firstResult = databaseTableResults;

    String? terminalId = firstResult["TerminalID"];
    String? parkingPlanId = firstResult["ParkingPlanID"];
    String? routePlanVersionID = firstResult["RoutePlanVersionID"];
    String? primarySort = firstResult["PrimarySort"];
    String? sideOfBelt = firstResult["SideofBelt"];
    String? postalCode = firstResult["PostalCode"];
    String? streetName = firstResult["StreetName"];
    String? streetType = firstResult["StreetType"];
    String? streetDirection = firstResult["StreetDirection"];
    String? customerName = firstResult["CustomerName"];
    String? municipalityName = firstResult["MunicipalityName"];
    String? routeNumber = firstResult["RouteNumber"];
    String? shelfNumber = firstResult["ShelfNumber"];
    String? truckShelfOverride = firstResult["TruckShelfOverride"];
    String? provinceCode = firstResult["ProvinceCode"];
    String? streetNumber;
    String? streetNumberSuffix;
    String? unitNumber;

    if (firstResult.containsKey("FromStreetNumber")) {
      streetNumber = firstResult["FromStreetNumber"].toString();
    }

    if (firstResult.containsKey("FromStreetNumberSuffix")) {
      streetNumberSuffix = firstResult["FromStreetNumberSuffix"];
    }

    if (firstResult.containsKey("FromUnitNumber")) {
      unitNumber = firstResult["FromUnitNumber"];
    }

    final servicePriority = firstResult["ServicePriority"];
    String? deliveryTime;
    switch (servicePriority) {
      case "25":
      case "0900":
        deliveryTime = "25";
      case "28":
      case "1030":
        deliveryTime = "28";
      case "29":
      case "EVN":
        deliveryTime = "29";
      case "12":
      case "1200":
        deliveryTime = "12";
    }
    String alternateAddressFlag = "N";

    String alternateUnitNumber = firstResult["AlternateUnitNumber"] ?? "";
    String alternateStreetNumber = firstResult["AlternateStreetNumber"] ?? "";
    String alternateStreetNumberSuffix = firstResult["AlternateStreetNumberSuffix"] ?? "";
    String alternateStreetName = firstResult["AlternateStreetName"] ?? "";
    String alternateStreetType = firstResult["AlternateStreetType"] ?? "";
    String alternateStreetDirection = firstResult["AlternateStreetDirection"] ?? "";
    String alternateMunicipalityName = firstResult["AlternateMunicipalityName"] ?? "";
    String alternatePostalCode = firstResult["AlternatePostalCode"] ?? "";
    String alternateProvinceCode = firstResult["AlternateProvince"] ?? "";

    if (alternateUnitNumber.isNotEmpty &&
        alternateStreetNumber.isNotEmpty &&
        alternateStreetNumberSuffix.isNotEmpty &&
        alternateStreetName.isNotEmpty &&
        alternateStreetType.isNotEmpty &&
        alternateStreetDirection.isNotEmpty &&
        alternateMunicipalityName.isNotEmpty &&
        alternatePostalCode.isNotEmpty &&
        alternateProvinceCode.isNotEmpty) {
      alternateAddressFlag = "Y";
      unitNumber = alternateUnitNumber;
      streetNumber = alternateStreetNumber;
      streetNumberSuffix = alternateStreetNumberSuffix;
      streetName = alternateStreetName;
      streetType = alternateStreetType;
      streetDirection = alternateStreetDirection;
      municipalityName = alternateMunicipalityName;
      postalCode = alternatePostalCode;
      provinceCode = alternateProvinceCode;
    }

    return result.copyWith(
      terminalNumber: terminalId,
      parkingPlanMasterVersionId: parkingPlanId,
      routePlanVersionId: routePlanVersionID,
      pudroNumber: primarySort,
      conveyorSide: sideOfBelt,
      postalCode: postalCode,
      streetName: streetName,
      streetType: streetType,
      streetDir: streetDirection,
      customerName: customerName,
      municipalityName: municipalityName,
      routeNumber: routeNumber,
      shelfNumber: shelfNumber,
      truckShelfOverride: truckShelfOverride,
      provinceCode: provinceCode,
      fromStreetNumber: streetNumber == "0" ? "" : null,
      streetNumberSuffix: streetNumberSuffix,
      unitNumber: unitNumber,
      deliveryTime: deliveryTime,
      alternateAddressFlag: alternateAddressFlag,
    );
  }

  /// "Old" address parsing for when we were doing route suggestions
  /// Fuzzy matching has been removed from the route search,
  /// however this logic will be modified and used in a future sprint for when doing address suggestions
  /// Which is why I'm keeping it commented out in here
// Future<LookupResult?> _retrieveAddressParsing({
//   required String pin,
//   required String terminalId,
//   String? postalCode,
// }) async {
//   // NOTE: this logic is copied straight from legacy app
//   final rpmDb = await dbManager.getRpmDatabase(terminalId);
//   // if there's no address, then surely there's a postal code
//   // this is basically a hail mary attempt at finding a single routeShelf
//   final List<Map<String, dynamic>> rpmQueryResult = rpmDb.select(
//       "SELECT COUNT(DISTINCT RouteNumber || ShelfNumber) FROM RoutePlan WHERE PostalCode='$postalCode' AND AddressRecordType NOT IN ('PostalCode', 'HFPU')");
//   rpmDb.dispose();
//   int numberOfRouteShelf = rpmQueryResult.first['COUNT(DISTINCT RouteNumber || ShelfNumber)'];
//   bool isSingleRouteShelf = numberOfRouteShelf == 1;
//   if (isSingleRouteShelf) {
//     return null;
//   }
//   return LookupResult(
//     lookupResultType: LookupResultType.remediation,
//     // to indicate we still reach out to webservice and got no route/shelf
//     lookupResolvedBy: LookupResolvedBy.webService,
//     pin: pin,
//     terminalNumber: terminalId,
//     postalCode: postalCode,
//   );
//
//   // // we dont care about fuzzyMatching on the initial lookup
//   // if (!fuzzyMatch) {
//   //   return [];
//   // }
//   // final List<Map<String, dynamic>> rpmQueryResult = await _fuzzyMatchRpm(rpmDb, barcodeData, terminalId);
//   // rpmDb.dispose();
//   //
//   // var remediateResults = List.generate(rpmQueryResult.length, (i) {
//   //   var lookup = LookupResult(
//   //     unitNumber: rpmQueryResult[i]['FromUnitNumber'].toString(),
//   //     customerName: rpmQueryResult[i]['CustomerName'],
//   //     fromStreetNumber: rpmQueryResult[i]['FromStreetNumber'].toString(),
//   //     toStreetNumber: rpmQueryResult[i]['ToStreetNumber'].toString(),
//   //     streetName: rpmQueryResult[i]['StreetName'],
//   //     lookupResultType: LookupResultType.routing,
//   //     lookupResolvedBy: LookupResolvedBy.barcodeParsing,
//   //     pin: barcodeData.pinNumber!,
//   //     terminalNumber: terminalId,
//   //     pudroNumber: rpmQueryResult[i]['Pudro'],
//   //     conveyorSide: rpmQueryResult[i]['SideOfBelt'],
//   //     routeNumber: rpmQueryResult[i]['RouteNumber'],
//   //     shelfNumber: rpmQueryResult[i]['ShelfNumber'],
//   //     truckShelfOverride: rpmQueryResult[i]['TruckShelfOverride'],
//   //     sequenceNumber: rpmQueryResult[i]['DeliverySequenceID'].toString(),
//   //     weight: rpmQueryResult[i]['Weight'],
//   //     duplicateCount: rpmQueryResult[i]['Duplicates'],
//   //   );
//   //   lookup = assignExtraFieldsFromDatabaseToLookupResult(rpmQueryResult[i], lookup);
//   //   return lookup;
//   // });
//   // return remediateResults;
// }

// Future<List<Map<String, dynamic>>> _fuzzyMatchRpm(sqlite3.Database rpmDb, BarcodeData barcodeData, String terminalId) async {
//   final String parkingPlanPath = await dbManager.getParkingPlanDatabasePath(terminalId);
//   const int resultsLimit = 10;
//   int streetNumber = 0;
//   String addressLine1 = "";
//   String city = "";
//   String postalCode = "";
//   _addDamerauLevenshteinDistanceToDb(rpmDb);
//
//   // input sanitization
//   // note: the db schema already has `COLLATE NOCASE` to all its fields, so we dont have to worry about case when comparing strings
//   if (barcodeData.city != null) {
//     city = barcodeData.city!.toUpperCase();
//   }
//   if (barcodeData.postalCode != null) {
//     postalCode = barcodeData.postalCode!.toUpperCase();
//   }
//   if (barcodeData.streetNumber != null && barcodeData.streetNumber?.isNotEmpty == true) {
//     streetNumber = int.tryParse(barcodeData.streetNumber!) ?? 0;
//   }
//   if (barcodeData.addressLine1 != null) {
//     addressLine1 = _removeStreetTypeAndDirection(barcodeData.addressLine1!).toUpperCase();
//   }
//   String rpmQuery = '''
//       SELECT COUNT(*) as Duplicates
//               ,rpm.RouteNumber, rpm.ShelfNumber, rpm.MunicipalityName, rpm.StreetName, rpm.PostalCode
//               ,rpm.TerminalID,rpm.RoutePlanID, rpm.RoutePlanVersionID
//               ,rpm.FromUnitNumber, rpm.StreetType, rpm.StreetDirection
//               ,rpm.FromStreetNumber, rpm.ToStreetNumber, rpm.CustomerName
//               ,rpm.ProvinceCode, rpm.TruckShelfOverride, rpm.DeliverySequenceID
//               ,pp.PrimarySort as Pudro, pp.SideofBelt, pp.ParkingPlanID
//               ,CASE
//                   WHEN
//                       streetName =  '$addressLine1'
//                       AND MunicipalityName = '$city'
//                       AND PostalCode = '$postalCode'
//                       AND $streetNumber between rpm.FromStreetNumber AND rpm.ToStreetNumber
//                   THEN 5
//                   WHEN
//                       streetName = '$addressLine1'
//                       AND PostalCode = '$postalCode'
//                       AND $streetNumber between rpm.FromStreetNumber AND rpm.ToStreetNumber
//                   THEN 4
//                   WHEN
//                       DamLevDiff(StreetName, '$addressLine1', true) >= 80
//                       AND DamLevDiff(MunicipalityName, '$city', true) >= 80
//                       AND PostalCode = '$postalCode'
//                       AND $streetNumber between rpm.FromStreetNumber AND rpm.ToStreetNumber
//                   THEN 3
//                   WHEN
//                       DamLevDiff(StreetName, '$addressLine1', true) >= 80
//                       AND DamLevDiff(MunicipalityName, '$city', true) >= 80
//                       AND ('$postalCode' <> '' AND DamLevDiff(PostalCode, '$postalCode', true) >= 70)
//                   THEN 2
//                   WHEN
//                       DamLevDiff(StreetName, '$addressLine1', true) >= 70
//                       AND DamLevDiff(MunicipalityName, '$city', true) >= 70
//                       AND ('$postalCode' <> '' AND DamLevDiff(PostalCode, '$postalCode', true) >= 60)
//                   THEN 1
//               ELSE 0
//       END as Weight
//       FROM RoutePlan rpm
//       LEFT JOIN parkingPlanDb.RouteMaster pp on rpm.RouteNumber=pp.RouteNumber
//       WHERE
//          (
//               ('$city' <> '' AND DamLevDiff(MunicipalityName, '$city', true) >= 60)
//               AND
//               ('$addressLine1' <> '' AND DamLevDiff(StreetName, '$addressLine1', true) >= 60)
//          )
//          OR
//          (
//               ('$postalCode' <> '' AND DamLevDiff(PostalCode, '$postalCode', true) >= 60)
//               AND
//               ('$addressLine1' <> '' AND DamLevDiff(StreetName, '$addressLine1',  true) >= 60)
//          )
//       GROUP BY rpm.RouteNumber, rpm.ShelfNumber, rpm.StreetName, rpm.PostalCode, rpm.MunicipalityName
//       ORDER BY Weight Desc LIMIT $resultsLimit;
//   ''';
//   rpmDb.execute("ATTACH DATABASE '$parkingPlanPath' as parkingPlanDb;");
//   var results = rpmDb.select(rpmQuery);
//   rpmDb.execute("DETACH DATABASE parkingPlanDb;");
//   return results;
// }

// void _addDamerauLevenshteinDistanceToDb(sqlite3.Database db) {
//   db.createFunction(
//     functionName: 'DamLevDiff',
//     argumentCount: const sqlite3.AllowedArgumentCount(3),
//     function: (args) {
//       String source = args[0]!.toString();
//       String target = args[1]!.toString();
//       bool ignoreCase = args[2] == 1;
//       int sourceLength = source.length;
//       int targetLength = target.length;
//
//       source = ignoreCase ? source.toLowerCase() : source;
//       target = ignoreCase ? target.toLowerCase() : target;
//
//       // Create a 2D list to store the edit distances between substrings.
//       List<List<int>> dp =
//           List.generate(sourceLength + 1, (i) => List.generate(targetLength + 1, (j) => 0, growable: false), growable: false);
//
//       for (int i = 0; i <= sourceLength; i++) {
//         for (int j = 0; j <= targetLength; j++) {
//           // If one of the strings is empty, the edit distance is the length of the other string.
//           if (i == 0) {
//             dp[i][j] = j;
//           } else if (j == 0) {
//             dp[i][j] = i;
//           } else {
//             // Check if the characters are the same.
//             int cost = source[i - 1] == target[j - 1] ? 0 : 1;
//
//             // Calculate the minimum of the insertion, deletion, substitution, and transposition operations.
//             dp[i][j] = [dp[i][j - 1] + 1, dp[i - 1][j] + 1, dp[i - 1][j - 1] + cost].reduce((minVal, val) => val < minVal ? val : minVal);
//
//             // Check if transposition is possible and calculate its cost.
//             if (i > 1 && j > 1 && source[i - 1] == target[j - 2] && source[i - 2] == target[j - 1]) {
//               dp[i][j] = dp[i][j] < dp[i - 2][j - 2] + cost ? dp[i][j] : dp[i - 2][j - 2] + cost;
//             }
//           }
//         }
//       }
//
//       // Return a percentage similarity. Reads like "String A" is 80% similar to "String B"
//       return (100 - dp[sourceLength][targetLength] / max(sourceLength, targetLength) * 100).toInt();
//     },
//   );
// }
}
