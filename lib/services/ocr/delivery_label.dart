import 'dart:io';

import 'package:sort_pro_printer_flutter/app/remediate/models/delivery_info.dart';
import 'package:sort_pro_printer_flutter/services/analyze_result_response.dart';

/// a picture of a delivery label with extracted data from OCR
class DeliveryLabel {
  final File picture;
  final DeliveryInfo deliveryInfo;
  final Fields ocrExtractedFields;

  DeliveryLabel(this.picture, this.deliveryInfo, this.ocrExtractedFields);

  DeliveryLabel copyWith(
    File? picture,
    DeliveryInfo? deliveryInfo,
    Fields? fields,
  ) =>
      DeliveryLabel(picture ?? this.picture, deliveryInfo ?? this.deliveryInfo, fields ?? ocrExtractedFields);
}
