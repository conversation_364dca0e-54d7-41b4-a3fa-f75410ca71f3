import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math';

import 'dart:typed_data';

import 'package:azblob/azblob.dart';
import 'package:dio/dio.dart';
import 'package:intl/intl.dart';
import 'package:mime/mime.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'package:sort_pro_printer_flutter/app/remediate/models/delivery_info.dart';
import 'package:sort_pro_printer_flutter/services/analyze_result_response.dart';
import 'package:sort_pro_printer_flutter/services/logs/i_log_service.dart';
import 'package:sort_pro_printer_flutter/services/network/i_network_service.dart';
import 'package:sort_pro_printer_flutter/services/ocr/delivery_label.dart';
import 'package:sort_pro_printer_flutter/services/ocr/i_ocr_service.dart';
import 'package:path/path.dart' as path;

class AzureCognitiveServicesOcrService extends IOcrService {
  final String _apiVersion = "2022-08-31";

  final String modelId;
  final String endpoint;
  final String apiKey;
  final String storageAccountConnString;
  final String storageAccountContainerName;
  final ILogService logService;
  final INetworkService networkService;
  StreamSubscription<NetworkStatus>? networkStatusSubscription;
  bool retryingFailedFiles = false;
  final Dio _dio = Dio();

  AzureCognitiveServicesOcrService({
    required this.logService,
    required this.networkService,
    required this.modelId,
    required this.endpoint,
    required this.apiKey,
    required this.storageAccountConnString,
    required this.storageAccountContainerName,
  });

  initialize() async {
    final networkStatus = await networkService.checkConnection();
    if (networkStatus != NetworkStatus.disconnected && !retryingFailedFiles) {
      _retryFailedLabels();
    }

    networkStatusSubscription?.cancel();
    networkStatusSubscription = networkService.subscribeToNetworkChanges((networkStatus) {
      if (networkStatus != NetworkStatus.disconnected && !retryingFailedFiles) {
        _retryFailedLabels();
      }
    });
  }

  @override
  Future<DeliveryLabel> extractDeliveryInfoFromLabelFile(File file) async {
    final byteArray = await file.readAsBytes();

    // Analyze the label picture
    final analyzeDocResult = await _makeAnalyzeRequest(byteArray);
    if (analyzeDocResult.statusCode != HttpStatus.accepted || analyzeDocResult.headers.value("Operation-Location") == null) {
      throw Exception("Whoops!");
    }
    final operationLocationUrl = analyzeDocResult.headers.value("Operation-Location")!;

    // Get and return the analyzed results
    final analyzeResultResponse = await _waitForAnalysisCompletion(operationLocationUrl);

    if (analyzeResultResponse.analyzeResult == null) throw Exception("OCR didnt find a single field");

    final fields = analyzeResultResponse.analyzeResult!.documents.first.fields;

    final pin = sanitizeField(fields.pin.valueString);

    final deliveryAddress = DeliveryAddress(
      customerName: sanitizeField(fields.recipientName.valueString),
      streetNumber: sanitizeField(fields.streetNumber.valueString),
      streetName: sanitizeField(fields.streetName.valueString),
      city: sanitizeField(fields.city.valueString),
      provinceCode: sanitizeField(fields.provinceCode.valueString),
      postalCode: sanitizePostalCode(sanitizeField(fields.postCode.valueString)),
    );

    return DeliveryLabel(
        file,
        DeliveryInfo(
          pin: pin ?? "",
          deliveryAddress: deliveryAddress,
        ),
        fields);
  }

  @override
  Future<bool> uploadTrainingImage(File imageFile) async {
    String fileName = basename(imageFile.path);
    try {
      Uint8List content = await imageFile.readAsBytes();
      var storage = AzureStorage.parse(storageAccountConnString);
      // get the mine type of the file
      String contentType = lookupMimeType(fileName) ?? "";
      // let's organize the files by date
      final folderName = DateFormat("yyyy-MM-dd").format(DateTime.now());
      await storage.putBlob('/$storageAccountContainerName/$folderName/$fileName',
          bodyBytes: content, contentType: contentType, type: BlobType.blockBlob);
      return true;
    } on AzureStorageException catch (ex, s) {
      logService.error(ex, s, false);
      return false;
    } on SocketException catch (err, s) {
      // Failed due to internet connection
      final Directory tempDir = await getTemporaryDirectory();
      final String dirPath = '${tempDir.path}/failed-labels';
      final directory = Directory(dirPath);
      if (!(await directory.exists())) {
        await Directory(dirPath).create(recursive: true);
      }
      final newFilePath = path.join(dirPath, fileName);
      if (imageFile.path != newFilePath) {
        await imageFile.rename(newFilePath);
      }
      logService.error(err, s, false);
      return false;
    } catch (err, s) {
      logService.error(err, s, false);
      return false;
    }
  }

  // Make the POST request to analyze the document in Azure Form Recognizer
  Future<Response> _makeAnalyzeRequest(List<int> byteArray) async {
    final response = await _dio.post(
      "$endpoint/formrecognizer/documentModels/$modelId:analyze?api-version=$_apiVersion",
      options: Options(
        contentType: Headers.jsonContentType,
        headers: {
          "Ocp-Apim-Subscription-Key": apiKey,
        },
      ),
      data: {"base64Source": base64Encode(byteArray)},
    );

    return response;
  }

  // Get the analysis from the api. We might need to wait for it to be completed
  Future<AnalyzeResultResponse> _waitForAnalysisCompletion(String operationLocationUrl) async {
    const initialDelay = Duration(seconds: 1);
    const maxRetries = 10;
    const retryDelayFactor = 2;

    for (var i = 0; i < maxRetries; i++) {
      await Future.delayed(initialDelay * (pow(retryDelayFactor, i)));

      final analyzeResponseResult = await _getAnalysisResponse(operationLocationUrl);

      if (analyzeResponseResult.statusCode != HttpStatus.ok) {
        throw Exception("Whoops #2!");
      }

      final analyzeResponse = AnalyzeResultResponse.fromJson(analyzeResponseResult.data);

      if (analyzeResponse.status != "running") {
        return analyzeResponse;
      }
    }

    throw Exception("Analysis not completed within the expected time.");
  }

  Future<Response> _getAnalysisResponse(String operationLocationUrl) async {
    final response = await _dio.get(
      operationLocationUrl,
      options: Options(
        contentType: Headers.jsonContentType,
        headers: {
          "Ocp-Apim-Subscription-Key": apiKey,
        },
      ),
    );

    return response;
  }

  // Sometimes the fields come with a comma
  String? sanitizeField(String? value) {
    if (value == null) return value;
    return value.replaceAll(",", "");
  }

  // We dont want the space in postalCode
  String? sanitizePostalCode(String? value) {
    if (value == null) return value;
    return value.replaceAll(" ", "").toUpperCase();
  }

  _retryFailedLabels() async {
    retryingFailedFiles = true;

    logService.trace(LogLevel.verbose, "Retrying to upload failed labels");
    // Create or get an instance of the directory of the labels
    final Directory tempDir = await getTemporaryDirectory();

    final String dirPath = '${tempDir.path}/failed-labels';
    final directory = Directory(dirPath);
    if (!(await directory.exists())) {
      await Directory(dirPath).create(recursive: true);
    }
    List<FileSystemEntity> entries = directory.listSync(recursive: false).toList();
    if (entries.isEmpty) {
      logService.trace(LogLevel.verbose, "No failed labels to upload!");
    } else {
      logService.trace(LogLevel.verbose, "Retrying ${entries.length} labels");
    }
    for (FileSystemEntity entity in entries) {
      if (entity is File) {
        final uploaded = await uploadTrainingImage(entity);
        if (uploaded) entity.delete();
      }
    }
    retryingFailedFiles = false;
  }

  @override
  dispose() {
    networkStatusSubscription?.cancel();
  }
}
