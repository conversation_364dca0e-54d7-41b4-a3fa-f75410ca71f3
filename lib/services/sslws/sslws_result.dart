enum SslwsStatusCode {
  rem(name: "R<PERSON>"),
  resolved(name: "Resolved"),
  mdr(name: "MD<PERSON>"),
  cd(name: "CD"),
  error(name: "Error"),
  srr(name: "SRR");

  const SslwsStatusCode({
    required this.name,
  });

  final String name;
}

class SslwsResult {
  final SslwsStatusCode statusCode;
  final String? conveyerSideIdentifier;
  final String? deliverySequenceID;
  final String? pin;
  final String? truckShelfOverride;
  final String? primarySort;
  final String? routeNumber;
  final String? shelfNumber;
  final String? terminalID;
  final List<SslwsError> errorList;

  SslwsResult({
    required this.statusCode,
    this.conveyerSideIdentifier,
    this.deliverySequenceID,
    this.pin,
    this.truckShelfOverride,
    this.primarySort,
    this.routeNumber,
    this.shelfNumber,
    this.terminalID,
    this.errorList = const [],
  });

  factory SslwsResult.fromJson(Map<String, dynamic> json) => SslwsResult(
        statusCode: SslwsStatusCode.values.firstWhere((element) => element.name == json["statusCode"], orElse: () => SslwsStatusCode.error),
        conveyerSideIdentifier: json["conveyerSideIdentifier"],
        deliverySequenceID: json["deliverySequenceID"],
        pin: json["pin"],
        truckShelfOverride: json["truckShelfOverride"],
        primarySort: json["primarySort"],
        routeNumber: json["routeNumber"],
        shelfNumber: json["shelfNumber"],
        terminalID: json["terminalID"],
        errorList: json["errorList"] != null ? List<SslwsError>.from(json["errorList"].map((data) => SslwsError.fromJson(data))) : const [],
      );

  Map<String, dynamic> toJson() => {
        "statusCode": statusCode.name,
        "conveyerSideIdentifier": conveyerSideIdentifier,
        "deliverySequenceID": deliverySequenceID,
        "pin": pin,
        "truckShelfOverride": truckShelfOverride,
        "routeNumber": routeNumber,
        "shelfNumber": shelfNumber,
        "terminalID": terminalID,
        "errorList": errorList.map((e) => e.toJson()),
      };
}

class SslwsError {
  final int errorCode;
  final String errorMessage;

  SslwsError(this.errorCode, this.errorMessage);

  factory SslwsError.fromJson(Map<String, dynamic> json) => SslwsError(
        json['ErrorCode'],
        json['ErrorMessage'],
      );

  Map<String, dynamic> toJson() => {
        "errorCode": errorCode,
        "errorMessage": errorMessage,
      };
}
