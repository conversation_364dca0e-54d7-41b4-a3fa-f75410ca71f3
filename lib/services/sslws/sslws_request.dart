class SslwsRequest {
  final String terminalId;
  final String pin;
  final String? customerName;
  final String deviceId;
  final String? unitNumber;
  final String? streetNumber;
  final String? addressLine1;
  final String? addressLine2;
  final String? municipalityName;
  final String? provinceCode;
  final String? postalCode;
  final String? premiumService;
  final String? shipmentType;
  final String? deliveryType;
  final String? diversionCode;
  final String? handlingClassType;
  final bool crossDockFlag;

  SslwsRequest({
    required this.terminalId,
    required this.pin,
    this.customerName,
    required this.deviceId,
    this.unitNumber,
    this.streetNumber,
    this.addressLine1,
    this.addressLine2,
    this.municipalityName,
    this.provinceCode,
    this.postalCode,
    this.premiumService,
    this.shipmentType,
    this.deliveryType,
    this.diversionCode,
    this.handlingClassType,
    this.crossDockFlag = false,
  });

  SslwsRequest copyWith({
    String? terminalId,
    String? pin,
    String? customerName,
    String? deviceId,
    String? unitNumber,
    String? streetNumber,
    String? addressLine1,
    String? addressLine2,
    String? municipalityName,
    String? provinceCode,
    String? postalCode,
    String? premiumService,
    String? shipmentType,
    String? deliveryType,
    String? diversionCode,
    String? handlingClassType,
    bool? crossDockFlag,
  }) =>
      SslwsRequest(
        terminalId: terminalId ?? this.terminalId,
        pin: pin ?? this.pin,
        customerName: customerName ?? this.customerName,
        deviceId: deviceId ?? this.deviceId,
        unitNumber: unitNumber ?? this.unitNumber,
        streetNumber: streetNumber ?? this.streetNumber,
        addressLine1: addressLine1 ?? this.addressLine1,
        addressLine2: addressLine2 ?? this.addressLine2,
        municipalityName: municipalityName ?? this.municipalityName,
        provinceCode: provinceCode ?? this.provinceCode,
        postalCode: postalCode ?? this.postalCode,
        premiumService: premiumService ?? this.premiumService,
        shipmentType: shipmentType ?? this.shipmentType,
        deliveryType: deliveryType ?? this.deliveryType,
        diversionCode: diversionCode ?? this.diversionCode,
        handlingClassType: handlingClassType ?? this.handlingClassType,
        crossDockFlag: crossDockFlag ?? this.crossDockFlag,
      );

  Map<String, dynamic> toJson({bool forLogging = false}) => {
        "TerminalID": terminalId,
        "PIN": pin,
        "CustomerName": !forLogging ? customerName ?? "" : "***",
        "DeviceID": deviceId,
        "UnitNumber": unitNumber ?? "",
        "StreetNumber": streetNumber ?? "",
        "AddressLine1": addressLine1,
        "AddressLine2": addressLine2,
        "MunicipalityName": municipalityName ?? "",
        "ProvinceCode": provinceCode ?? "",
        "PostalCode": postalCode ?? "",
        "PremiumService": premiumService ?? "00",
        "ShipmentType": shipmentType ?? "0",
        "DeliveryType": deliveryType ?? "0",
        "DiversionCode": diversionCode ?? "0",
        "HandlingClassType": handlingClassType ?? "00",
        "CrossDockFlag": crossDockFlag ? "Y" : "N",
      };
}
