import 'dart:io';

class SslwsHttpClient {

  SslwsHttpClient._();

  static HttpClient get client => HttpClient()
  // Increase the idle timeout so we decrease the times we need to create a new connection everytime we make a request.
    ..idleTimeout = const Duration(seconds: 600)
  // The SmartSort Lookup Web Service needs VPN to be accessible, so is safe to use a "Trust All" Policy
  ..badCertificateCallback =
  (X509Certificate cert, String host, int port) => true;

}