import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:dio/io.dart';
import 'package:sort_pro_printer_flutter/network/logging_interceptor.dart';
import 'package:sort_pro_printer_flutter/services/logs/log_service.dart';
import 'package:sort_pro_printer_flutter/services/sslws/sslws_request.dart';
import 'package:sort_pro_printer_flutter/services/sslws/sslws_result.dart';
import 'package:sort_pro_printer_flutter/services/sslws/sslws_http_client.dart';

class SslwsService {
  late final Dio _dio;

  final String domain;
  final String baseUrl;
  final String user;
  final String password;

  static const healthServiceRequestEndpoint = "IsServiceUp";

  SslwsService(this.domain, this.baseUrl, this.user, this.password) {
    _dio = Dio(
      BaseOptions(
        // The legacy version has a timeout of 5 seconds. Probably in case someone makes the call without VPN connection
        receiveTimeout: const Duration(seconds: 5),
        connectTimeout: const Duration(seconds: 5),
        sendTimeout: const Duration(seconds: 5),
      ),
    )..interceptors.add(LoggingInterceptor(LogService.instance));
    (_dio.httpClientAdapter as IOHttpClientAdapter).createHttpClient = () => SslwsHttpClient.client;
  }

  Future<void> activateIoSocket() async {
    final String authInfo = "Basic ${base64Encode(utf8.encode("$domain\\$user:$password"))}";
    try {
      await _dio.get("$baseUrl/RestServices/SmartSortScanService.svc/$healthServiceRequestEndpoint",
          options: Options(
            contentType: Headers.jsonContentType,
            headers: {
              "Accept": "*/*",
              "Authorization": authInfo,
            },
            receiveTimeout: const Duration(milliseconds: 100),
            sendTimeout: const Duration(milliseconds: 100),
          ));
    } on DioException catch (_) {}
  }

  // This request gets a Lookup result back from the Smart Sort Lookup WebService
  Future<SslwsResult> getSSLWData(SslwsRequest request, {bool retryOn400Failure = false}) async {
    final String authInfo = "Basic ${base64Encode(utf8.encode("$domain\\$user:$password"))}";

    try {
      final response = await _dio.post(
        "$baseUrl/RestServices/SmartSortLookupService.svc/getrouteshelf",
        options: Options(
          contentType: Headers.jsonContentType,
          headers: {
            "Accept": "*/*",
            "Authorization": authInfo,
          },
        ),
        data: request.toJson(),
      );

      // Dio does throw an exception when status code is 4** or 5**,
      // but just in case the statusCode ends up being different
      if (response.statusCode != HttpStatus.ok) {
        throw Exception("Something went wrong getting the Lookup data");
      }

      final result = SslwsResult.fromJson(response.data);
      // There is a change the response does return a correct status code but there are errors in the list
      if (result.statusCode == SslwsStatusCode.error && result.errorList.isNotEmpty) {
        throw Exception("Something went wrong getting the Lookup data");
      }
      return result;
    } on DioException catch (e) {
      if (e.type == DioExceptionType.connectionTimeout) {
        throw Exception("Connection timed out");
      }
      // There's a known issue with the SSLWS not accepting DeliveryType or ShipmentType greater than 2
      if (retryOn400Failure &&
          e.response?.statusCode == 400 &&
          ((int.tryParse(request.deliveryType ?? "0") ?? 0) > 2 || ((int.tryParse(request.shipmentType ?? "0") ?? 0) > 2))) {
        return await getSSLWData(request.copyWith(deliveryType: "0", shipmentType: "0"));
      }

      rethrow;
    } catch (e) {
      rethrow;
    }
  }
}
