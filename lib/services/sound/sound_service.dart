import 'package:audioplayers/audioplayers.dart';
import 'package:sort_pro_printer_flutter/services/sound/i_sound_service.dart';

class SoundService extends ISoundService {
  final player = AudioPlayer();

  @override
  Future<void> playSoundFromAssets(String path) async {
    await player.stop();
    await player.play(AssetSource(path));
  }

  @override
  dispose() {
    player.stop().then((value) => player.dispose());
  }
}
