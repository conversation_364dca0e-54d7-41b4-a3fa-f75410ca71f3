import 'package:sort_pro_printer_flutter/db/ssws_database_manager.dart';
import 'package:sort_pro_printer_flutter/services/routes/i_route_service.dart';
import 'package:sort_pro_printer_flutter/services/routes/terminal_route.dart';

class RouteService extends IRouteService {
  final SswsDatabaseManager dbManager;

  RouteService(this.dbManager);

  @override
  Future<List<TerminalRoute>> getRoutesForTerminal(String terminalId) async {
    final parkingPlanDb = await dbManager.getParkingPlanDatabase(terminalId);
    final List<Map<String, dynamic>> parkingPlanQueryResult = parkingPlanDb.select("SELECT * FROM RouteMaster");
    parkingPlanDb.dispose();
    var routes = List.generate(parkingPlanQueryResult.length, (i) {
      return TerminalRoute(
        routeNumber: parkingPlanQueryResult[i]['RouteNumber'],
        primarySort: parkingPlanQueryResult[i]['PrimarySort'],
        sideOfBelt: parkingPlanQueryResult[i]['SideofBelt'],
      );
    });
    return routes;
  }
}
