import 'package:csv/csv.dart';
import 'package:flutter/services.dart';
import 'package:sort_pro_printer_flutter/app/manage/data/models/terminal.dart';
import 'package:sort_pro_printer_flutter/services/logs/log_service.dart';
import 'package:sort_pro_printer_flutter/services/terminals/backup_terminal_list.dart';
import 'package:sort_pro_printer_flutter/services/terminals/i_terminal_service.dart';

// This uses the method channel to get a list of terminals from a csv file from within native android
// Currently not in use, but kept in case in future we decide to move forward with this again.
class MethodChannelTerminalService extends ITerminalService {
  static const String _getTerminalsMethod = "getTerminalsCsvContent";
  static const MethodChannel _methodChannel = MethodChannel('com.pdl.sortproprinter/command');

  final csvToListConverter = const CsvToListConverter();

  @override
  Future<List<Terminal>> getTerminals() async {
    try {
      final String terminalsCsvContent = await _methodChannel.invokeMethod<String>(_getTerminalsMethod) ?? "";
      final List<List<dynamic>> rowsAsListOfValues = csvToListConverter.convert(
        terminalsCsvContent,
        eol: "\n",
        shouldParseNumbers: false,
      );

      return List<Terminal>.from(rowsAsListOfValues.map((e) => Terminal(id: e[0], name: e[1], provinceCode: e[2])));
    } catch (e, s) {
      LogService.instance.error(e, s, false);
    }

    return backupTerminalList;
  }
}
