import 'dart:async';
import 'dart:convert';

import 'package:flutter/services.dart';
import 'package:sort_pro_printer_flutter/app/scanner/data/models/barcode.dart';
import 'package:sort_pro_printer_flutter/app/scanner/data/models/zebra_scanner.dart';
import 'package:sort_pro_printer_flutter/services/logs/i_log_service.dart';
import 'package:sort_pro_printer_flutter/services/scanner/i_scanner_service.dart';

class ScannerService extends IScannerService {
  // MethodChannel to communicate with the Android Platform
  static const MethodChannel _methodChannel = MethodChannel('com.pdl.sortproprinter/command');

  // EventChannels to listen to events from Android
  static const EventChannel _scanChannel = EventChannel('com.pdl.sortproprinter/scan');
  static const EventChannel _scannerTriggeredChannel = EventChannel('com.pdl.sortproprinter/scanner/triggered');

  StreamSubscription? _scanChannelSubscription;
  StreamSubscription? _scannerTriggeredChannelSubscription;

  final ILogService _logService;

  ScannerService(this._logService) {
    _initialize();
  }

  _initialize() {
    _scanChannelSubscription = _scanChannel.receiveBroadcastStream().listen(_onBarcodeScan, onError: _onError);
    _scannerTriggeredChannelSubscription = _scannerTriggeredChannel.receiveBroadcastStream().listen(_onScannerTriggered, onError: _onError);
  }

  void _onBarcodeScan(event) {
    final jsonData = jsonDecode(event);

    if (jsonData is Map) {
      _logService.trace(LogLevel.information, "Single-barcode scanned", additionalProperties: jsonData.map((key, value) => MapEntry(key, value.toString())));
      final zebraScan = ZebraScan.fromJson(jsonData as Map<String, dynamic>);
      _mapSingleScan(zebraScan);
    }
  }

  void _onScannerTriggered(event) {
    scannerTriggeredController.add(null);
  }

  void _mapSingleScan(ZebraScan zebraScan) {
    final barcode = Barcode.fromZebraScan(zebraScan);
    _logService.trace(LogLevel.information, "Barcode decoded", additionalProperties: {
      "rawData": barcode.rawData,
      "symbology": barcode.barcodeSymbology.name,
      "type": barcode.barcodeType.name,
    });
    scannedBarcodesController.add(barcode);
  }

  void _onError(Object error) {}

  @override
  playInvalidBarcodeFeedback() async {
    try {
      // 02 - 3 high short beeps
      // 47 - Turn Red LED ON
      await _methodChannel.invokeMethod("playFeedback", "2 47");
      await Future.delayed(const Duration(milliseconds: 500));
      // 48 - Turn Red LED OFF
      await _methodChannel.invokeMethod("playFeedback", "48");
    } on PlatformException catch (e, s) {
      _logService.error(e, s, false);
    }
  }

  @override
  dispose() {
    _scanChannelSubscription?.cancel();
    _scannerTriggeredChannelSubscription?.cancel();
    super.dispose();
  }
}
