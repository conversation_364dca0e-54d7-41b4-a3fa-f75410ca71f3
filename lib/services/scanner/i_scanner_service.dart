import 'dart:async';

import 'package:sort_pro_printer_flutter/app/scanner/data/models/barcode.dart';
import 'package:sort_pro_printer_flutter/services/disposable.dart';

abstract class IScannerService implements Disposable {

  Future<void> playInvalidBarcodeFeedback();

  final StreamController<Barcode> scannedBarcodesController = StreamController.broadcast();

  Stream<Barcode> get scannedBarcodes => scannedBarcodesController.stream;

  final StreamController<void> scannerTriggeredController = StreamController.broadcast();

  Stream<void> get scannerTriggered => scannerTriggeredController.stream;

  @override
  dispose() {
    scannedBarcodesController.close();
    scannerTriggeredController.close();
  }
}