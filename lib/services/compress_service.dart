import 'dart:io';

import 'package:sort_pro_printer_flutter/services/i_compress_service.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:path/path.dart' as path;

class CompressService extends ICompressService {


  @override
  Future<File> compressFile(File file) async {
    final String dirName = path.dirname(file.path);
    final String extension = path.extension(file.path);
    final String basename = path.basenameWithoutExtension(file.path);

    // Compress the file
    final targetFilepath = path.join(dirName, "${basename}_compressed$extension");
    final xfile = await FlutterImageCompress.compressAndGetFile(
      file.absolute.path,
      targetFilepath,
    );

    if (xfile == null) throw Exception("Failed to compress file");

    return File(xfile.path);
  }

}