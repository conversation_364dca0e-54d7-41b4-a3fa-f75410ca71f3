import 'dart:async';

import 'package:flutter/services.dart';
import 'package:sort_pro_printer_flutter/services/ring_scanner/i_ringer_service.dart';
import 'package:sort_pro_printer_flutter/services/ring_scanner/zebra_ring_scanner_info_channel.dart';

class ZebraRingerScannerService extends IRingScannerService {
  static const String _getCurrentRingScannerStatusMethod = "getCurrentRingScannerStatus";

  // MethodChannel to communicate with the Android Platform
  static const MethodChannel _methodChannel = MethodChannel('com.pdl.sortproprinter/command');

  StreamSubscription<String>? streamSubscription;
  final ZebraRingScannerInfoChannel zebraRingScannerInfoChannel = ZebraRingScannerInfoChannel();

  String lastRingScannerStatus = "";

  ZebraRingerScannerService() {
    streamSubscription = zebraRingScannerInfoChannel.ringScannerStatus.listen((event) {
      if(lastRingScannerStatus != event) {
        // We usually get "WAITING" right after "CONNECTED"
        if ((event == "CONNECTED" || event == "WAITING") && (lastRingScannerStatus != "CONNECTED" && lastRingScannerStatus != "WAITING")) {
          notifyScannerStatusChanged(RingScannerStatus.connected);
        }
        if (event == "DISABLED") {
          notifyScannerStatusChanged(RingScannerStatus.disabled);
        }
        if (event == "DISCONNECTED") {
          notifyScannerStatusChanged(RingScannerStatus.disconnected);
        }
        lastRingScannerStatus = event;
      }
    });
  }

  @override
  Future<RingScannerStatus> getCurrentScannerStatus() async {
    final status = await _methodChannel.invokeMethod<String>(_getCurrentRingScannerStatusMethod);
    if (status == "CONNECTED" || status == 'WAITING') {
      return RingScannerStatus.connected;
    }
    if (status == "DISABLED") {
      return RingScannerStatus.disabled;
    }
    return RingScannerStatus.disconnected;
  }

  @override
  Future<void> dispose() async {
    streamSubscription?.cancel();
    zebraRingScannerInfoChannel.dispose();
  }
}
