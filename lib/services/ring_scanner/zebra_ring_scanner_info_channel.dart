import 'dart:async';

import 'package:flutter/services.dart';

class ZebraRingScannerInfoChannel {
  // EventChannels to listen to events from Android
  static const EventChannel _scanChannel = EventChannel('com.pdl.sortproprinter/ring-scanner/status');

  StreamSubscription? _channelSubscription;

  ZebraRingScannerInfoChannel() {
    _initialize();
  }

  _initialize() {
    _channelSubscription = _scanChannel.receiveBroadcastStream().listen(
      _onRingScannerStatusReceived,
    );
  }

  final StreamController<String> _zebraRingScannerStatusStreamController = StreamController.broadcast();

  Stream<String> get ringScannerStatus => _zebraRingScannerStatusStreamController.stream;

  void _onRingScannerStatusReceived(event) {
    _zebraRingScannerStatusStreamController.add(event);
  }

  dispose() {
    _zebraRingScannerStatusStreamController.close();
    _channelSubscription?.cancel();
  }

}