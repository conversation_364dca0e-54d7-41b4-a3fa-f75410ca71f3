import 'dart:async';

import 'package:sort_pro_printer_flutter/services/disposable.dart';

enum RingScannerStatus {
  connected,
  disabled,
  disconnected
}

abstract class IRingScannerService implements Disposable {

  Future<RingScannerStatus> getCurrentScannerStatus();

  /// A stream system to be able to listen to scanner status changes
  final StreamController<RingScannerStatus> _ringScannerStatusStreamController = StreamController<RingScannerStatus>.broadcast();
  Stream<RingScannerStatus> get ringScannerStatusStream => _ringScannerStatusStreamController.stream;

  notifyScannerStatusChanged(RingScannerStatus status) => _ringScannerStatusStreamController.add(status);

}
