import 'package:android_id/android_id.dart';
import 'package:flutter/services.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:sort_pro_printer_flutter/services/device_info/i_device_info_service.dart';
import 'package:sort_pro_printer_flutter/services/logs/log_service.dart';

class DeviceInfoService extends IDeviceInfoService {
  String appVersion = "";
  String deviceId = "";
  SharedPreferences? _preferences;
  static const String _serialNumberKey = "SERIAL_NUMBER";
  static const String _getSerialNumberMethod = "getSerialNumber";
  static const MethodChannel _methodChannel = MethodChannel('com.pdl.sortproprinter/command');

  Future<SharedPreferences> get preferences async => _preferences ?? await SharedPreferences.getInstance();

  @override
  Future<String> getAppVersion() async {
    if (appVersion.isEmpty) {
      PackageInfo packageInfo = await PackageInfo.fromPlatform();
      appVersion = packageInfo.version;
    }
    return appVersion;
  }

  @override
  Future<String> getDeviceId() async {
    // We cache this device ID so we don't have to fetch it again
    if (deviceId.isNotEmpty) return deviceId;

    try {
      // try to fetch it from SharedPreferences. We use this approach because we can't access MethodChannel from an isolate
      deviceId = (await preferences).getString(_serialNumberKey) ?? "";
      if (deviceId.isNotEmpty) return deviceId;

      // try to fetch the serial number (Zebra devices only)
      deviceId = await _methodChannel.invokeMethod<String>(_getSerialNumberMethod) ?? "";
      if (deviceId.isNotEmpty) {
        (await preferences).setString(_serialNumberKey, deviceId);
        return deviceId;
      } else {
        throw Exception("Unable to extract serial number");
      }
    } catch (e, s) {
      LogService.instance.error(e, s, false);
      // If we weren't unable to extract the serial number, fallback into getting an unique android ID
      const androidIdPlugin = AndroidId();
      deviceId = await androidIdPlugin.getId() ?? deviceId;
    }
    return deviceId;
  }
}
