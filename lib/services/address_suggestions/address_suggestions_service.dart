import 'dart:async';
import 'dart:convert';
import 'dart:isolate';
import 'dart:math';

import 'package:async/async.dart';
import 'package:flutter_isolate/flutter_isolate.dart';
import 'package:sort_pro_printer_flutter/app/remediate/models/delivery_info.dart';
import 'package:sort_pro_printer_flutter/db/ssws_database_manager.dart';
import 'package:sort_pro_printer_flutter/extensions/string_nullable.dart';
import 'package:sort_pro_printer_flutter/services/address_suggestions/i_address_suggestions_service.dart';
import 'package:sort_pro_printer_flutter/services/logs/i_log_service.dart';
import 'package:sort_pro_printer_flutter/services/logs/log_service.dart';
import 'package:sqlite3/sqlite3.dart';

class AddressSuggestionsService extends IAddressSuggestionsService {
  // These are sent from the Main Isolate to our Data Sync Isolate
  static const commandKey = "command";
  static const dataKey = "data";
  static const getAddressSuggestions = "getAddressSuggestions";

  // These are sent from our Data Sync Isolate to the Main Isolate
  static const responseCodeKey = "responseCode";
  static const responseDataKey = "responseData";
  static const responseDataKeySuggestionAddresses = "suggestionAddresses";
  static const suggestionsFound = "suggestionsFound";

  SendPort? _sendPort;
  StreamQueue? _eventsFromIsolate;
  bool _initialized = false;

  var searchingSuggestions = Completer();
  List<SuggestionAddress> suggestionAddresses = [];

  _init() async {
    if (!_initialized) {
      await _startIsolate();
      _initialized = true;
    }
  }

  /// Function that starts the iot hub Isolate
  /// It will pass a SendPort from a ReceivePort to the isolate's top-level function
  /// so that it sends the SendPort back from the isolate to communicate between each-other
  Future<void> _startIsolate() async {
    final p = ReceivePort();
    await FlutterIsolate.spawn(_dataSyncFunction, p.sendPort);

    // Convert the ReceivePort into a StreamQueue to receive messages from the
    // spawned isolate using a pull-based interface. Events are stored in this
    // queue until they are accessed by `events.next`.
    _eventsFromIsolate = StreamQueue<dynamic>(p);

    // The first message from the spawned isolate is a SendPort. This port is
    // used to communicate with the spawned isolate.
    _sendPort = await _eventsFromIsolate!.next;
    _listenToEventsFromIsolate();
  }

  _listenToEventsFromIsolate() async {
    while (await _eventsFromIsolate!.hasNext) {
      final message = await _eventsFromIsolate!.next;
      if (message is String) {
        final jsonMessage = jsonDecode(message) as Map<String, dynamic>;
        final responseCode = jsonMessage[responseCodeKey];
        if (responseCode == suggestionsFound) {
          final List<Map<String, dynamic>> addressSuggestionsMap =
              List<Map<String, dynamic>>.from(jsonMessage[responseDataKey][responseDataKeySuggestionAddresses]);
          suggestionAddresses = addressSuggestionsMap.map((e) => SuggestionAddress.fromJson(e)).toList();
          searchingSuggestions.complete();
        }
      }
    }
  }

  @pragma('vm:entry-point')
  static Future<void> _dataSyncFunction(SendPort sendPort) async {
    final logService = LogService.instance;
    final dbManager = SswsDatabaseManager();
    logService.trace(LogLevel.verbose, "Address Suggestion Isolate started.");
    // Send a SendPort to the main isolate so that it can send JSON strings to
    // this isolate.
    final commandPort = ReceivePort();
    sendPort.send(commandPort.sendPort);

    StreamQueue messages = StreamQueue<dynamic>(commandPort);

    while (await messages.hasNext) {
      final message = await messages.next;
      if (message is String) {
        final jsonMessage = jsonDecode(message) as Map<String, dynamic>;
        final command = jsonMessage[commandKey];
        final inputData = jsonMessage[dataKey];

        switch (command) {
          case getAddressSuggestions:
            final String terminalId = inputData?["terminalId"];
            final DeliveryAddress inputAddress = DeliveryAddress.fromJson(jsonDecode(inputData?["inputAddress"]));

            final rpmDB = await dbManager.getRpmDatabase(terminalId);
            final combinedAddress = ((inputAddress.addressLine1 ?? '') + (inputAddress.city ?? '') + (inputAddress.postalCode ?? ''))
                .toUpperCase()
                .replaceAll(" ", "");
            var streetNum = inputAddress.streetNumber;
            AddressSuggestionsService.addDamerauLevenshteinDistanceToDb(rpmDB);

            String rpmQuery = """
            SELECT DISTINCT UPPER(REPLACE( COALESCE(StreetName, '') || 
                                           COALESCE(StreetType, '') ||
                                           COALESCE(StreetDirection, '') ||
                                           COALESCE(MunicipalityName, '') || 
                                           COALESCE(PostalCode, '') , ' ', '')) as combinedTarget,
                  StreetName,
                  StreetType,
                  StreetDirection,
                  MunicipalityName,
                  PostalCode,
                  ProvinceCode,
                  FromStreetNumber,
                  ToStreetNumber
            FROM RoutePlan
            WHERE DamLevDiff(combinedTarget, '$combinedAddress', true) >= 80
            AND ${streetNum.isNullOrEmpty() ? "''" : streetNum} BETWEEN FromStreetNumber AND ToStreetNumber
            LIMIT 2;
            """;

            final List<Map<String, dynamic>> rpmQueryResult = rpmDB.select(rpmQuery);
            rpmDB.dispose();

            List<SuggestionAddress> deliveryAddresses = rpmQueryResult
                .map((address) => SuggestionAddress(
                    customerName: inputAddress.customerName,
                    streetNumber: inputAddress.streetNumber,
                    addressLine2: inputAddress.addressLine2,
                    suiteNumber: inputAddress.suiteNumber,
                    fromStreetNumber: int.tryParse(address['FromStreetNumber'].toString()),
                    toStreetNumber: int.tryParse(address['ToStreetNumber'].toString()),
                    streetName: (address['StreetName'] ?? "").toUpperCase(),
                    streetType: (address['StreetType'] ?? "").toUpperCase(),
                    streetDirection: (address['StreetDirection'] ?? "").toUpperCase(),
                    postalCode: (address['PostalCode'] ?? "").toUpperCase(),
                    city: (address['MunicipalityName'] ?? "").toUpperCase(),
                    provinceCode: (address['ProvinceCode'] ?? "").toUpperCase()))
                .toList();
            sendPort.send(jsonEncode({
              responseCodeKey: suggestionsFound,
              responseDataKey: {responseDataKeySuggestionAddresses: deliveryAddresses.map((e) => e.toJson()).toList()},
            }));
            break;
        }
      } else if (message == null) {
        // Exit if the main isolate sends a null message
        break;
      }
    }
    Isolate.exit();
  }

  static void addDamerauLevenshteinDistanceToDb(Database db) {
    db.createFunction(
      functionName: 'DamLevDiff',
      argumentCount: const AllowedArgumentCount(3),
      function: (args) {
        String source = args[0]!.toString();
        String target = args[1]!.toString();
        bool ignoreCase = args[2] == 1;
        int sourceLength = source.length;
        int targetLength = target.length;

        source = ignoreCase ? source.toLowerCase() : source;
        target = ignoreCase ? target.toLowerCase() : target;

        // Create a 2D list to store the edit distances between substrings.
        List<List<int>> dp =
            List.generate(sourceLength + 1, (i) => List.generate(targetLength + 1, (j) => 0, growable: false), growable: false);

        for (int i = 0; i <= sourceLength; i++) {
          for (int j = 0; j <= targetLength; j++) {
            // If one of the strings is empty, the edit distance is the length of the other string.
            if (i == 0) {
              dp[i][j] = j;
            } else if (j == 0) {
              dp[i][j] = i;
            } else {
              // Check if the characters are the same.
              int cost = source[i - 1] == target[j - 1] ? 0 : 1;

              // Calculate the minimum of the insertion, deletion, substitution, and transposition operations.
              dp[i][j] = [dp[i][j - 1] + 1, dp[i - 1][j] + 1, dp[i - 1][j - 1] + cost].reduce((minVal, val) => val < minVal ? val : minVal);

              // Check if transposition is possible and calculate its cost.
              if (i > 1 && j > 1 && source[i - 1] == target[j - 2] && source[i - 2] == target[j - 1]) {
                dp[i][j] = dp[i][j] < dp[i - 2][j - 2] + cost ? dp[i][j] : dp[i - 2][j - 2] + cost;
              }
            }
          }
        }

        // Return a percentage similarity. Reads like "String A" is 80% similar to "String B"
        return (100 - dp[sourceLength][targetLength] / max(sourceLength, targetLength) * 100).toInt();
      },
    );
  }

  @override
  Future<List<SuggestionAddress>> getSuggestionsForAddress(String terminalId, DeliveryAddress inputAddress) async {
    if (inputAddress.addressLine1.isNullOrEmpty() || inputAddress.city.isNullOrEmpty() || inputAddress.postalCode.isNullOrEmpty()) {
      return List.empty();
    }
    if (!searchingSuggestions.isCompleted) searchingSuggestions.complete();
    searchingSuggestions = Completer();
    await _init();
    // Send the command to sync the data immediately
    _sendPort?.send(jsonEncode({
      commandKey: getAddressSuggestions,
      dataKey: {
        "terminalId": terminalId,
        "inputAddress": jsonEncode(inputAddress.toJson()), // default port just in case
      }
    }));
    await searchingSuggestions.future;
    return suggestionAddresses;
  }

  dispose() {
    _sendPort?.send(null); // sending null to stop the isolate
    _initialized = false;
  }
}
