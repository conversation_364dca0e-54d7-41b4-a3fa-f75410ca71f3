import 'package:flutter/services.dart';

class NfcService {
  static const String _enableForegroundNfcMethod = "enableForegroundNfc";
  static const String _disableForegroundNfcMethod = "disableForegroundNfc";

  // MethodChannel to communicate with the Android Platform
  static const MethodChannel _methodChannel = MethodChannel('com.pdl.sortproprinter/nfc');

  // This allows to react to NFC messages in the foreground
  // Specifically used for connecting to the printer
  static Future<void> enableForegroundNfc() async => await _methodChannel.invokeMethod(_enableForegroundNfcMethod);
  static Future<void> disableForegroundNfc() async => await _methodChannel.invokeMethod(_disableForegroundNfcMethod);
}
