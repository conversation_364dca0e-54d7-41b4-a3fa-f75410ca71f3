import 'package:flutter/foundation.dart';
import 'package:sort_pro_printer_flutter/environment/environment_config.dart';
import 'package:sort_pro_printer_flutter/services/logs/microsoft_logging_service.dart';
import 'package:sort_pro_printer_flutter/services/logs/console_log_service.dart';
import 'package:sort_pro_printer_flutter/services/logs/i_log_service.dart';

class LogService {
  // We use kReleaseMode to only enable crash reporting for release builds
  static final ILogService _instance = kReleaseMode
      ? MicrosoftLoggingService(EnvironmentConfig.appInsightsInstrumentationKey, EnvironmentConfig.appCenterKey)
      : ConsoleLogService();

  static ILogService get instance => _instance;

  LogService._();
}
