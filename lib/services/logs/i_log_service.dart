enum LogLevel {
  verbose,
  information,
  warning,
  error,
  critical,
}

abstract class ILogService {
  void setUser(String userId);
  void setDeviceId(String deviceId);
  void setAppVersion(String appVersion);
  void trace(LogLevel logLevel, String message, {Map<String, Object> additionalProperties = const {}});
  void request({
    required Duration duration,
    required int responseCode,
    String? source,
    String? name,
    bool? success,
    String? url,
    Map<String, Object> additionalProperties = const <String, Object>{},
    DateTime? timestamp,
  });
  void event(String event, {Map<String, String> additionalProperties = const {}});
  void error(Object exception, StackTrace stackTrace, bool isFatal, {Map<String, Object> additionalProperties = const {}});
  Future<void> flush();
}