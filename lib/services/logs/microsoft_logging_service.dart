import 'package:azure_application_insights/azure_application_insights.dart';
import 'package:appcenter/app_center.dart';
import 'package:http/http.dart';
import 'package:sort_pro_printer_flutter/environment/environment_config.dart';
import 'package:sort_pro_printer_flutter/services/logs/i_log_service.dart';
import 'package:uuid/uuid.dart';

/// This services leverages both App Center and Application Insights from Microsoft
class MicrosoftLoggingService implements ILogService {
  late final Processor processor;
  late final TelemetryClient telemetryClient;

  MicrosoftLoggingService(String instrumentationKey, String appCenterKey) {
    processor = BufferedProcessor(
      next: TransmissionProcessor(
        instrumentationKey: instrumentationKey,
        httpClient: Client(),
        timeout: const Duration(seconds: 10),
      ),
    );

    telemetryClient = TelemetryClient(
      processor: processor,
    );

    AppCenter.initialize(appCenterKey: appCenterKey);
  }

  @override
  void setUser(String userId) {
    telemetryClient.context.user.id = userId;
    AppCenter.setUser(userId: userId);
  }

  @override
  void error(
    Object exception,
    StackTrace stackTrace,
    bool isFatal, {
    Map<String, Object> additionalProperties = const {},
  }) {
    // Write an error telemetry item.
    telemetryClient.trackError(
      error: exception,
      stackTrace: stackTrace,
      severity: isFatal ? Severity.critical : Severity.error,
      additionalProperties: additionalProperties,
    );
    AppCenter.trackError(
        exception: exception,
        stackTrace: stackTrace,
        additionalProperties: additionalProperties.map((key, value) => MapEntry(key, value.toString())));
  }

  @override
  void event(String event, {Map<String, String> additionalProperties = const {}}) {
    telemetryClient.trackEvent(
      name: event,
      additionalProperties: additionalProperties,
    );
    AppCenter.trackEvent(eventType: event, additionalProperties: additionalProperties);
  }

  @override
  void trace(LogLevel logLevel, String message, {Map<String, Object> additionalProperties = const {}}) {
    late Severity severity;
    final appLogLevel = int.tryParse(EnvironmentConfig.logLevel) ?? LogLevel.information.index; // default
    bool canLog = false;
    switch (logLevel) {
      case LogLevel.verbose:
        severity = Severity.verbose;
        break;
      case LogLevel.information:
        severity = Severity.information;
        break;
      case LogLevel.warning:
        severity = Severity.warning;
        break;
      case LogLevel.error:
        severity = Severity.error;
        break;
      case LogLevel.critical:
        severity = Severity.critical;
        break;
    }
    canLog = logLevel.index >= appLogLevel;

    if (canLog) {
      telemetryClient.trackTrace(
        severity: severity,
        message: message,
        additionalProperties: additionalProperties,
      );
    }
  }

  @override
  Future<void> flush() async => await telemetryClient.flush();

  @override
  void request({
    required Duration duration,
    required int responseCode,
    String? source,
    String? name,
    bool? success,
    String? url,
    Map<String, Object> additionalProperties = const <String, Object>{},
    DateTime? timestamp,
  }) {
    telemetryClient.trackRequest(
      id: const Uuid().v1(),
      duration: duration,
      responseCode: responseCode.toString(),
      source: source,
      name: name,
      success: success,
      url: url,
      additionalProperties: additionalProperties,
      timestamp: timestamp,
    );
  }

  @override
  void setAppVersion(String appVersion) {
    telemetryClient.context.applicationVersion = appVersion;
  }

  @override
  void setDeviceId(String deviceId) {
    telemetryClient.context.device.id = deviceId;
  }
}
