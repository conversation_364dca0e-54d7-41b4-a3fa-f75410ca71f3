import 'package:flutter/foundation.dart';
import 'package:sort_pro_printer_flutter/services/logs/i_log_service.dart';

class ConsoleLogService implements ILogService {
  @override
  void error(Object exception, StackTrace stackTrace, bool isFatal, {Map<String, Object> additionalProperties = const {}}) {
    if (kDebugMode) {
      print("--------------------------------------------------------");
      print("Exception\n${exception.toString()}\n\nFatal: $isFatal\n\nStackTrace: $stackTrace\n\nProps: $additionalProperties");
      print("--------------------------------------------------------");
    }
  }

  @override
  void event(String event, {Map<String, Object> additionalProperties = const {}}) {
    if (kDebugMode) {
      print("--------------------------------------------------------");
      print("Event\n$event\n\nProps: $additionalProperties");
      print("--------------------------------------------------------");
    }
  }

  @override
  Future<void> flush() async {
    if (kDebugMode) {
      print("--------------------------------------------------------");
      print("Flushing...");
      print("--------------------------------------------------------");
    }
  }

  @override
  void setUser(String userId) {
    if (kDebugMode) {
      print("User ID: $userId");
    }
  }

  @override
  void trace(LogLevel logLevel, String message, {Map<String, Object> additionalProperties = const {}}) {
    if (kDebugMode) {
      print("--------------------------------------------------------");
      print("Trace\n${logLevel.name}\n\nMessage: $message\n\nProps: $additionalProperties");
      print("--------------------------------------------------------");
    }
  }

  @override
  void request(
      {required Duration duration,
      required int responseCode,
      String? source,
      String? name,
      bool? success,
      String? url,
      Map<String, Object> additionalProperties = const <String, Object>{},
      DateTime? timestamp}) {
    if(kDebugMode) {
      print("--------------------------------------------------------");
      print("Request\n$name\n\nDuration: ${duration.inMilliseconds} ms\n\nSuccess: $success\n\nAdditional Props: $additionalProperties");
      print("--------------------------------------------------------");
    }
  }

  @override
  void setAppVersion(String appVersion) {
    if(kDebugMode) {
      print("App Version: $appVersion");
    }
  }

  @override
  void setDeviceId(String deviceId) {
    if(kDebugMode) {
      print("Device ID: $deviceId");
    }
  }
}
