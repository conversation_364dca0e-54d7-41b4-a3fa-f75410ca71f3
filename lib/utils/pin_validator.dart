import 'package:sort_pro_printer_flutter/services/logs/i_log_service.dart';
import 'package:sort_pro_printer_flutter/services/logs/log_service.dart';

class PinValidator {
  final ILogService _logService;

  PinValidator({ILogService? logService}) : _logService = logService ?? LogService.instance;

  bool validatePin(String pin) {
    _logService.trace(LogLevel.verbose, "Pin Validation", additionalProperties: {"pin": pin});

    bool isValid = false;

    // Rule - 8
    if (pin.length != 18 && (pin.length < 9 || pin.length > 13)) {
      _logService.trace(LogLevel.error, "Pin Validation Failure",
          additionalProperties: {"pin": pin, "reason": "Pin Length = 18 or Pin Length < 9 or Pin Length > 13"});
      return false; // Reject
    } else {
      isValid = true;
    }

    // Rule - 9
    if (pin.substring(0, 4).contains("OSNR")) {
      _logService.trace(LogLevel.error, "Pin Validation Failure", additionalProperties: {"pin": pin, "reason": "Pin starts with OSNR"});
      return false; // Reject
    } else {
      isValid = true;
    }

    // RULE -1
    if (RegExp(r'[A-Za-z0-9]').allMatches(pin).length == pin.length) {
      _logService.trace(LogLevel.verbose, "Pin Validation Success",
          additionalProperties: {"pin": pin, "reason": "Pin contains only valid alpha numeric chars"});
      isValid = true; // Accept
    } else {
      _logService.trace(LogLevel.error, "Pin Validation Failure",
          additionalProperties: {"pin": pin, "reason": "Pin contains invalid alpha numeric chars"});
      return false; // Reject
    }

    // RULE -2
    if (pin.trim().length == pin.length) {
      isValid = true;
      _logService
          .trace(LogLevel.verbose, "Pin Validation Success", additionalProperties: {"pin": pin, "reason": "Pin has no extra space"});
    } else {
      _logService.trace(LogLevel.error, "Pin Validation Failure", additionalProperties: {"pin": pin, "reason": "Pin has extra space"});
      return false; // Reject
    }

    // RULE - 3 && Rule - 6
    if (pin.substring(0, 2).contains("SD") || pin.substring(0, 2).contains("LT")) {
      if (RegExp(r'[0-9]').allMatches(pin.substring(0)).length >= 5 && RegExp(r'[0-9]').allMatches(pin.substring(0)).length <= 9) {
        // Accept
        _logService.trace(LogLevel.verbose, "Pin Validation Success",
            additionalProperties: {"pin": pin, "reason": "Pin contains 5 to 9 digits only"});
        return true;
      }
    }

    // RULE - 4
    // if (pin.contains("M123"))
    // {
    //     // Accept
    //     isValid = true;
    // }

    // RULE - 5
    if (pin.length == 12 &&
        (RegExp(r'[A-Za-z]').allMatches(pin.substring(0, 2)).length == 2 &&
            RegExp(r'[0-9]').allMatches(pin.substring(2, 10)).length == 8 &&
            RegExp(r'[A-Za-z]').allMatches(pin.substring(10, 12)).length == 2)) {
      // UPU PIN
      // Accept
      _logService.trace(LogLevel.verbose, "Pin Validation Success", additionalProperties: {
        "pin": pin,
        "reason": "Pin length is 12, First and Last two char are alpha, and Pin's 3rd to 8th char are numeric"
      });
      isValid = true;
    }

    // RULE - 7
    if (pin.length == 13 &&
        RegExp(r'[0-9]').allMatches(pin.substring(0, 1)).length == 1 &&
        RegExp(r'[A-Za-z]').allMatches(pin.substring(1, 3)).length == 2 &&
        RegExp(r'[0-9]').allMatches(pin.substring(3, 12)).length == 9 &&
        RegExp(r'[ABC]').allMatches(pin.substring(12, 13)).length == 1) {
      _logService.trace(LogLevel.verbose, "Pin Validation Success", additionalProperties: {
        "pin": pin,
        "reason": "Pin length is 13, 1st char is nummeric, 2nd & 3rd is alpha, 4th to 12th char are numeric, and Last char is caps alpha"
      });
      // Accept no further checks required
      return true;
    }

    // RULE - 10
    if (RegExp(r'[A-Za-z]').allMatches(pin.substring(0, 3)).length == 3 &&
        RegExp(r'[0-9]').allMatches(pin.substring(0, pin.length)).length >= 6 &&
        RegExp(r'[0-9]').allMatches(pin.substring(0, pin.length)).length <= 9) {
      if (pin.substring(0, 3).contains("SNR")) {
        // Reject
        _logService.trace(LogLevel.error, "Pin Validation Failure", additionalProperties: {"pin": pin, "reason": "Pin starts with SNR"});
        return false;
      } else {
        // Accept
        _logService.trace(LogLevel.verbose, "Pin Validation Success",
            additionalProperties: {"pin": pin, "reason": "First three char is alpha, and pin contains 6 to 9 digits only"});
        isValid = true;
      }
    }
    // RULE - 11
    if (pin.length >= 9 &&
        pin.length <= 12 &&
        RegExp(r'[0-9]').allMatches(pin.substring(0, pin.length)).length >= 9 &&
        RegExp(r'[0-9]').allMatches(pin.substring(0, pin.length)).length <= 12) {
      if (pin.startsWith("0")) {
        // Reject
        _logService.trace(LogLevel.error, "Pin Validation Failure", additionalProperties: {"pin": pin, "reason": "Pin starts with 0"});
        return false;
      } else {
        if (!RegExp(r'[A-Za-z]').hasMatch(pin)) {
          if (mod10Check(pin) || mod7Check(pin)) {
            // Accept
            return true;
          } else {
            // Reject
            return false;
          }
        }
      }
    }
    // RULE - 12
    if (pin.length == 13 &&
        RegExp(r'[A-Za-z]').allMatches(pin.substring(0, 2)).length == 2 &&
        RegExp(r'[0-9]').allMatches(pin.substring(2, 10)).length == 8 &&
        RegExp(r'[A-Za-z]').allMatches(pin.substring(10, 13)).length == 3 &&
        pin.substring(11, 13).endsWith("CA")) {
      _logService.trace(LogLevel.verbose, "Pin Validation Success", additionalProperties: {
        "pin": pin,
        "reason":
            "Pin length is 13, 1st char is numeric, 2nd & 3rd are alpha, contains 6 to 9 digits only, pin's 10th to 12th char are alpha and pin's 12th and 13th char equals 'CA'"
      });
      // Accept
      return true;
    }
    // RULE - 13
    if (pin.length == 13 &&
        RegExp(r'[A-Za-z]').allMatches(pin.substring(0, 2)).length == 2 &&
        RegExp(r'[0-9]').allMatches(pin.substring(2, 11)).length == 9 &&
        RegExp(r'[A-Za-z]').allMatches(pin.substring(11, 13)).length == 2 &&
        pin.substring(11, 13).endsWith("CA")) {
      if ((RegExp(r'[A-Za-z]').allMatches(pin.substring(0, 2)).length == 2 &&
          RegExp(r'[0-9]').allMatches(pin.substring(2, 11)).length == 9 &&
          RegExp(r'[A-Za-z]').allMatches(pin.substring(10, 12)).length == 2)) {
        if (pin.endsWith("CA")) {
          if (pin.startsWith("RN") || pin.startsWith("CP") || pin.startsWith("CW") || pin.startsWith("CN")) {
            // Accept
            _logService.trace(LogLevel.verbose, "Pin Validation Success", additionalProperties: {
              "pin": pin,
              "reason":
                  "Pin length is 13, 1st & 2nd char are alpha, 3rd to 10th char are digits, 12th to 13th chars are alpha, 12th and 13th char equals 'CA'"
            });
            return true;
          } else {
            if (mod11Check(pin)) {
              // Accept
              return true;
            } else {
              // Reject
              return false;
            }
          }
        } else {
          // Accept
          _logService.trace(LogLevel.verbose, "Pin Validation Success", additionalProperties: {
            "pin": pin,
            "reason": "Pin length is 13, 1st & 2nd char are alpha, 3rd to 10th char are digits, 12th to 13th chars are alpha"
          });
          return true;
        }
      }
    }

    // Rule - 14
    if (pin.length >= 10 &&
        pin.length <= 13 &&
        RegExp(r'[A-Za-z]').allMatches(pin.substring(0, 1)).length == 1 &&
        (RegExp(r'[0-9]').allMatches(pin.substring(1, pin.length - 1)).length == pin.length - 1)) {
      String digits = pin.substring(1, pin.length - 1);
      if (digits.startsWith("0")) {
        // Reject
        _logService.trace(LogLevel.error, "Pin Validation Failure", additionalProperties: {
          "pin": pin,
          "reason": "Pin length is >= 10, 1st char is alpha, Except first char, all chars are numeric, Pin starts with 0"
        });
        return false;
      } else {
        if (RegExp(r'[B-Z]').hasMatch(pin.substring(0, 1))) {
          if (mod7Check(digits)) {
            return true;
          } else {
            return false;
          }
        } else if (RegExp(r'[A]').hasMatch(pin.substring(0, 1))) {
          if (mod10Check(digits) || mod7Check(digits)) {
            // Accept
            return true;
          } else {
            // Reject
            return false;
          }
        }
      }
    }

    // If Pin doesn't conform with any rule
    // Reject
    if (!isValid) {
      _logService.trace(LogLevel.error, "Pin Validation Failure", additionalProperties: {"pin": pin, "reason": "Pin pattern unknown"});
    }
    return isValid;
  }

  bool mod10Check(String pin) {
    if (pin.isNotEmpty && (pin.length >= 9 && pin.length <= 12)) {
      pin = pin.padLeft(13, '0');
      int sum = 0;
      int checkEven = 0;
      for (int strIndex = 0; strIndex < pin.length; strIndex++) {
        if ((strIndex % 2) == 0) {
          if (strIndex != (pin.length - 1)) {
            sum += int.parse(pin[strIndex]);
          }
        } else {
          checkEven = int.parse(pin[strIndex]) * 2;
          sum += (checkEven > 9) ? (checkEven - 9) : checkEven;
        }
      }
      int Y = sum - ((sum ~/ 10) * 10);
      int Z = (10 - Y) % 10;

      if (pin[pin.length - 1] == Z.toString()) {
        _logService.trace(LogLevel.verbose, "Pin Validation Success", additionalProperties: {"pin": pin, "reason": "MOD 10 pass"});
        return true;
      }
    }
    _logService.trace(LogLevel.error, "Pin Validation Failure", additionalProperties: {"pin": pin, "reason": "MOD 10 fail"});
    return false;
  }

  bool mod7Check(String pin) {
    if (pin.isNotEmpty && (pin.length >= 9 && pin.length <= 12)) {
      int total = 0;
      int strIndex = 0;
      for (; strIndex < (pin.length - 1); strIndex++) {
        total = (total * 10 + int.parse(pin[strIndex])) % 7;
      }
      if (pin[strIndex] == total.toString()) {
        _logService.trace(LogLevel.verbose, "Pin Validation Success", additionalProperties: {"pin": pin, "reason": "MOD 7 pass"});
        return true;
      }
    }
    _logService.trace(LogLevel.error, "Pin Validation Failure", additionalProperties: {"pin": pin, "reason": "MOD 7 fail"});
    return false;
  }

  bool mod11Check(String pin) {
    if (pin.isNotEmpty && pin.length == 13) {
      String pinDigit = pin.substring(2, 11);
      int sum = 0;
      for (int k = 0; k < pinDigit.length - 1; k++) {
        switch (k) {
          case 0:
            sum += int.parse(pinDigit[k]) * 8;
            break;
          case 1:
            sum += int.parse(pinDigit[k]) * 6;
            break;
          case 2:
            sum += int.parse(pinDigit[k]) * 4;
            break;
          case 3:
            sum += int.parse(pinDigit[k]) * 2;
            break;
          case 4:
            sum += int.parse(pinDigit[k]) * 3;
            break;
          case 5:
            sum += int.parse(pinDigit[k]) * 5;
            break;
          case 6:
            sum += int.parse(pinDigit[k]) * 9;
            break;
          case 7:
            sum += int.parse(pinDigit[k]) * 7;
            break;
        }
      }
      int rem = (sum % 11);
      if (pinDigit[pinDigit.length - 1] == (11 - rem).toString()) {
        _logService.trace(LogLevel.information, "Pin Validation Success", additionalProperties: {"pin": pin, "reason": "MOD 11 pass"});
        return true;
      }
    }
    _logService.trace(LogLevel.error, "Pin Validation Failure", additionalProperties: {"pin": pin, "reason": "MOD 11 fail"});
    return false;
  }
}
