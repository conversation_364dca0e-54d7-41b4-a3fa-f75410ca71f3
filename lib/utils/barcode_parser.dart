import 'package:sort_pro_printer_flutter/app/scanner/data/models/barcode.dart';
import 'package:sort_pro_printer_flutter/app/scanner/data/models/barcode_data.dart';
import 'package:sort_pro_printer_flutter/extensions/string_nullable.dart';
import 'package:sort_pro_printer_flutter/services/logs/log_service.dart';
import 'package:sort_pro_printer_flutter/utils/exceptions.dart';

class BarcodeParser {
  static BarcodeData decodeBarcode(Barcode barcode) {
    checkStreetNumber(String address) {
      // grab the first part of the address, and return the numbers from it
      var splitString = address.split(' ');
      var number = splitString[0].replaceAll(RegExp(r'[^0-9]'), '');
      return number;
    }

    try {
      switch (barcode.barcodeType) {
        /// Emp Badge
        case BarcodeType.employeeBadge:
          return BarcodeData(employeeId: barcode.rawData);

        /// Printer Mac
        case BarcodeType.printerMac:
          return BarcodeData(printerMac: barcode.rawData);

        /// Purolator 2D
        case BarcodeType.puro2d:
          final barcodeDetails = {};
          // we split this barcode in sections
          // e.g [..., S03~0, S04~00, ...]
          final strValues = barcode.rawData.split('|');
          // for each section we split in half
          // e.g [S04, 00]
          for (String strValue in strValues) {
            final keyPair = strValue.split('~');
            // There should be two values in here
            if (keyPair.length == 2) {
              barcodeDetails[keyPair[0]] = keyPair[1];
            }
          }

          String? customerName;
          String? suiteNumber;
          String? streetNumber;
          String? addressLine1;
          String? addressLine2;
          String? postalCode;
          String? city;
          String? pinNumber;
          String? deliveryTime;
          String? shipmentType;
          String? deliveryType;
          String? diversionCode;
          String? handlingClassType;
          bool? isDeliveryTimeFromBarcode;
          bool? isDiversionCodeFromBarcode;

          if (barcodeDetails.keys.contains("R01")) {
            customerName = barcodeDetails["R01"];
          }
          if (barcodeDetails.keys.contains("R02")) {
            suiteNumber = barcodeDetails["R02"];
          }

          if (barcodeDetails.keys.contains("R03")) {
            streetNumber = barcodeDetails["R03"];
          }

          if (barcodeDetails.keys.contains("R04")) {
            if (streetNumber.isNullOrEmpty()) {
              streetNumber = checkStreetNumber(barcodeDetails["R04"]);
              addressLine1 = barcodeDetails["R04"].toString().replaceAll(streetNumber, "").trim();
            } else {
              addressLine1 = barcodeDetails["R04"];
            }
          }

          if (barcodeDetails.keys.contains("R05")) {
            addressLine2 = barcodeDetails["R05"];
          }

          if (barcodeDetails.keys.contains("R06")) {
            city = barcodeDetails["R06"];
          }

          if (barcodeDetails.keys.contains("R07")) {
            postalCode = barcodeDetails["R07"];
          }

          if (barcodeDetails.keys.contains("S02")) {
            pinNumber = barcodeDetails["S02"];
          }

          if (barcodeDetails.keys.contains("S04")) {
            deliveryTime = barcodeDetails["S04"];
          }
          if (barcodeDetails.keys.contains("S05")) {
            shipmentType = barcodeDetails["S05"];
          }
          if (barcodeDetails.keys.contains("S06")) {
            deliveryType = barcodeDetails["S06"];
          }
          if (barcodeDetails.keys.contains("S07")) {
            diversionCode = barcodeDetails["S07"];
          }
          if (barcodeDetails.keys.contains("S15")) {
            handlingClassType = barcodeDetails["S15"];
          }

          if (deliveryTime != null && deliveryTime.isNotEmpty) {
            isDeliveryTimeFromBarcode = true;
          }
          if (diversionCode != null && diversionCode.isNotEmpty) {
            isDiversionCodeFromBarcode = true;
          }

          return BarcodeData(
            customerName: customerName,
            suiteNumber: suiteNumber,
            streetNumber: streetNumber,
            addressLine1: addressLine1,
            addressLine2: addressLine2,
            city: city,
            postalCode: postalCode,
            pinNumber: pinNumber,
            shipmentType: shipmentType,
            deliveryType: deliveryType,
            handlingClassType: handlingClassType,
            isDeliveryTimeFromBarcode: isDeliveryTimeFromBarcode,
            isDiversionCodeFromBarcode: isDiversionCodeFromBarcode,
            deliveryTime: deliveryTime,
            diversionCode: diversionCode
          );

        /// Way Bill
        case BarcodeType.manualWayBill:
          return BarcodeData(
            pinNumber: barcode.rawData.substring(1),
          );

        /// Ngb
        case BarcodeType.ngb:
          String strPostalCode = barcode.rawData.substring(0, 9);

          final postalCode = _convertPostalCode(strPostalCode);
          String? pinNumber;
          if (barcode.rawData.substring(9, 11) == "94" && barcode.rawData.substring(23, 24) == "0") {
            pinNumber = barcode.rawData.substring(11, 23);
          } else {
            pinNumber = _convertToAlphabet(barcode.rawData.substring(9, 11)) +
                _convertToAlphabet(barcode.rawData.substring(11, 13)) +
                _convertToAlphabet(barcode.rawData.substring(13, 15)) +
                barcode.rawData.substring(15, 24);
          }

          final deliveryTime = barcode.rawData.substring(25, 27);
          final shipmentType = barcode.rawData.substring(27, 28);
          final deliveryType = barcode.rawData.substring(28, 29);
          final diversionCode = barcode.rawData.substring(29, 30);
          const isDeliveryTimeFromBarcode = true;
          const isDiversionCodeFromBarcode = true;

          return BarcodeData(
            postalCode: postalCode,
            pinNumber: pinNumber,
            deliveryTime: deliveryTime,
            shipmentType: shipmentType,
            deliveryType: deliveryType,
            diversionCode: diversionCode,
            isDiversionCodeFromBarcode: isDiversionCodeFromBarcode,
            isDeliveryTimeFromBarcode: isDeliveryTimeFromBarcode,
          );

        /// Legacy Puro
        case BarcodeType.legacyPurolator:
          String? pinNumber;
          if (barcode.rawData.length == 12 && RegExp(r'[A-Za-z]').allMatches(barcode.rawData.substring(0, 3)).length != 3) {
            pinNumber = barcode.rawData.substring(1);
          } else {
            pinNumber = barcode.rawData;
          }

          return BarcodeData(
            pinNumber: pinNumber,
          );

        /// UPS Manual Waybill
        case BarcodeType.upsManualWaybill:
          return BarcodeData(pinNumber: barcode.rawData);

        /// Maxicode
        case BarcodeType.upsMaxicode:
          final strMaxicode = barcode.rawData.split(String.fromCharCode(29));

          if (strMaxicode.length == 15 &&
              strMaxicode[1].length == 8 &&
              strMaxicode[4].length == 10 &&
              strMaxicode[6].length == 6 &&
              strMaxicode[3].length == 3) {
            final postalCode = strMaxicode[1].substring(2, 6);
            String strServiceIndicator = "";
            //conversion of service indicator based on UPS 1Z Maxicode documentation
            int serviceIndicatorInt = int.parse(strMaxicode[3]);
            if (serviceIndicatorInt < 100) {
              strServiceIndicator = "$serviceIndicatorInt";
            } else {
              //Logic to convert Numeric to Alphanumeric
              int secondCharValue = serviceIndicatorInt % 32;
              int firstCharValue = serviceIndicatorInt - secondCharValue;
              if (BarcodeParser._maxicodeServiceIndicatorFirstChar.keys.contains(firstCharValue)) {
                strServiceIndicator = BarcodeParser._maxicodeServiceIndicatorFirstChar[firstCharValue]!;
                if (BarcodeParser._maxicodeServiceIndicatorSecondChar.keys.contains(secondCharValue)) {
                  strServiceIndicator = strServiceIndicator + BarcodeParser._maxicodeServiceIndicatorSecondChar[secondCharValue]!;
                } else {
                  //BarcodeData.BarCodeType = Session.BarcodeTypes.Unrecognized;
                }
              } else {
                //BarcodeData.BarCodeType = Session.BarcodeTypes.Unrecognized;
              }
            }
            final pinNumber = strMaxicode[4].substring(0, 2) + strMaxicode[6] + strServiceIndicator + strMaxicode[4].substring(2, 8);
            const deliveryTime = "00";
            final addressLine1 = strMaxicode[12];

            return BarcodeData(
              postalCode: postalCode,
              pinNumber: pinNumber,
              deliveryTime: deliveryTime,
              addressLine1: addressLine1,
            );
          }

          return const BarcodeData();

        /// 1Z
        case BarcodeType.ups1z:
          return BarcodeData(pinNumber: barcode.rawData);

        /// Postal Code
        case BarcodeType.upsPostalCode:
          return BarcodeData(postalCode: barcode.rawData.substring(3, 6));

        /// Unknown
        case BarcodeType.unknown:
          return const BarcodeData();
      }
    } catch (e, s) {
      LogService.instance.event("Failed to decode barcode", additionalProperties: {"rawValue": barcode.rawData, "symbology": barcode.barcodeSymbology.name});
      LogService.instance.error(e, s, false);
      throw DecodeBarcodeException();
    }
  }

  /// Used for the decoding logic of the UPS Maxicode barcode
  static final Map<int, String> _maxicodeServiceIndicatorFirstChar = {
    992: 'Z',
    960: 'Y',
    928: 'X',
    896: 'W',
    864: 'V',
    832: 'T',
    800: 'S',
    768: 'R',
    736: 'Q',
    704: 'P',
    672: 'N',
    640: 'M',
    608: 'L',
    576: 'K',
    544: 'J',
    512: 'H',
    480: 'G',
    448: 'F',
    416: 'E',
    384: 'D',
    352: 'C',
    320: 'A',
    288: '9',
    256: '8',
    224: '7',
    192: '6',
    160: '5',
    128: '4',
    96: '3',
    64: '2',
    32: '1',
    0: '0'
  };

  /// Used for the decoding logic of the UPS Maxicode barcode
  static final _maxicodeServiceIndicatorSecondChar = {
    31: 'Z',
    30: 'Y',
    29: 'X',
    28: 'W',
    27: 'V',
    26: 'T',
    25: 'S',
    24: 'R',
    23: 'Q',
    22: 'P',
    21: 'N',
    20: 'M',
    19: 'L',
    18: 'K',
    17: 'J',
    16: 'H',
    15: 'G',
    14: 'F',
    13: 'E',
    12: 'D',
    11: 'C',
    10: 'A',
    9: '9',
    8: '8',
    7: '7',
    6: '6',
    5: '5',
    4: '4',
    3: '3',
    2: '2',
    1: '1',
    0: '0'
  };

  /// Used for the decoding logic of the NGB barcode
  static String _convertPostalCode(String strPostalCode) {
    String finalPostalCode = "";
    String? strConvert;
    String? numberToChar;

    strConvert = strPostalCode.substring(0, 2);
    finalPostalCode += _convertToAlphabet(strConvert);

    numberToChar = strPostalCode.substring(2, 3);
    finalPostalCode += numberToChar;

    strConvert = strPostalCode.substring(3, 5);
    finalPostalCode += _convertToAlphabet(strConvert);

    numberToChar = strPostalCode.substring(5, 6);
    finalPostalCode += numberToChar;

    strConvert = strPostalCode.substring(6, 8);
    finalPostalCode += _convertToAlphabet(strConvert);

    numberToChar = strPostalCode.substring(8, 9);
    finalPostalCode += numberToChar;

    return finalPostalCode;
  }

  /// Used for the decoding logic of the NGB barcode
  static String _convertToAlphabet(String strConvert) {
    int asciival = int.parse(strConvert);
    String result = String.fromCharCode(asciival + 64);
    return result;
  }
}
