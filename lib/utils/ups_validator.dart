class UpsValidator {

  static bool ups1zValidation(String scanData) {
    int odd = 0;
    int even = 0;
    String data = scanData.substring(2).toUpperCase();

    Map<String, String> openWith = {
      'A': '2',
      'B': '3',
      'C': '4',
      'D': '5',
      'E': '6',
      'F': '7',
      'G': '8',
      'H': '9',
      'I': '0',
      'J': '1',
      'K': '2',
      'L': '3',
      'M': '4',
      'N': '5',
      'O': '6',
      'P': '7',
      'Q': '8',
      'R': '9',
      'S': '0',
      'T': '1',
      'U': '2',
      'V': '3',
      'W': '4',
      'X': '5',
      'Y': '6',
      'Z': '7',
    };

    String barcodeOneZ = '';

    for (int i = 0; i < data.length; i++) {
      String value = '';
      if (openWith.containsKey(data[i])) {
        value = openWith[data[i]]!;
      } else {
        value = data[i];
      }
      barcodeOneZ += value;
    }

    for (int i = 0; i < barcodeOneZ.length - 1; i++) {
      if (i % 2 != 0) {
        even += int.parse(barcodeOneZ[i]);
      } else {
        odd += int.parse(barcodeOneZ[i]);
      }
    }

    even = even * 2;
    int res = odd + even;
    int rem = res ~/ 10;
    rem = rem + 1;
    int multiple = rem * 10;
    int checkDigit = multiple - res;
    checkDigit = checkDigit % 10;

    if (RegExp(r'[A-Za-z]').allMatches(scanData.substring(scanData.length - 1)).length == 1) {
      return false;
    }

    if (checkDigit == int.parse(scanData.substring(scanData.length - 1))) {
      return true;
    }

    return false;
  }

}