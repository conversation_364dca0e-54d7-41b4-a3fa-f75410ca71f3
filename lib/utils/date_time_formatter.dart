import 'package:intl/intl.dart';

String formatDateTimeWithTimeZone(DateTime dateTime) {
  String formattedDate = DateFormat('yyyy-MM-ddTHH:mm:ss.SSS').format(dateTime);

  // Calculate the timezone offset in hours and minutes
  Duration offset = dateTime.timeZoneOffset;
  String offsetSign = offset.isNegative ? "-" : "+";
  String offsetHours = offset.inHours.abs().toString().padLeft(2, '0');
  String offsetMinutes = (offset.inMinutes % 60).abs().toString().padLeft(2, '0');

  // Format the offset as ±HH:mm
  String formattedOffset = "$offsetSign$offsetHours:$offsetMinutes";

  return "$formattedDate $formattedOffset";
}
