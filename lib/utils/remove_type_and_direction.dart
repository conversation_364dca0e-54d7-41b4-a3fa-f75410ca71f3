
import '../app/remediate/models/delivery_info.dart';

//Class dealing with Street Types and Directions from the street_postfix List
class RemoveTypeAndDirection {
  //This function takes in a string that is a full address and outputs a StreetAutoCompleteDetails which has the Street Name, Type and Direction separated based on the street_postfix List
  static FullStreetDetails cleanAddress(String inputAddress, {required List<String> listOfTypes, required List<String> listOfDirections}) {
    String streetName = inputAddress.toUpperCase();
    List<String> removedStreetTypes = [];
    List<String> removedStreetDirection = [];

    for (final streetType in listOfTypes) {
      String pattern = RegExp.escape(" ${streetType.toUpperCase()}");
      RegExp regex = RegExp(pattern);

      if (regex.hasMatch(streetName)) {
        removedStreetTypes.add(streetType);
        streetName = streetName.replaceAll(regex, "");
      }
    }

    for (final streetDirection in listOfDirections) {
      String pattern = RegExp.escape(" ${streetDirection.toUpperCase()}");
      RegExp regex = RegExp(pattern);

      if (regex.hasMatch(streetName)) {
        removedStreetDirection.add(streetDirection);
        streetName = streetName.replaceAll(regex, "");
      }
    }

    return FullStreetDetails(streetName, removedStreetTypes.join(), removedStreetDirection.join());
  }

  //This function takes in a deliveryInfo, the incoming fullAddress String and all the addressNames for the Terminal. It returns the same DeliveryInfo with the Types and Directions separated based on matched addressNames
  //If there are no matches in the addressNames the matching is done with the street_postfix List
  static DeliveryInfo matchedAddress(DeliveryInfo deliveryInfo, String fullAddress, List<FullStreetDetails> addressNames,
      {required List<String> listOfTypes, required List<String> listOfDirections}) {
    try {
      var addressMatch = addressNames.firstWhere((street) => street.fullStreetName == fullAddress,
          orElse: () => throw Exception()); //The thrown exception is being caught before and the data is processed with cleanAddress func
      deliveryInfo = deliveryInfo.copyWith(
          deliveryAddress: deliveryInfo.deliveryAddress.copyWith(
              streetName: addressMatch.streetName, streetType: addressMatch.streetType, streetDirection: addressMatch.streetDirection));
    } catch (e) {
      FullStreetDetails address =
          RemoveTypeAndDirection.cleanAddress(fullAddress, listOfTypes: listOfTypes, listOfDirections: listOfDirections);
      deliveryInfo = deliveryInfo.copyWith(
          deliveryAddress: deliveryInfo.deliveryAddress
              .copyWith(streetName: address.streetName, streetType: address.streetType, streetDirection: address.streetDirection));
    }

    return deliveryInfo;
  }
}
