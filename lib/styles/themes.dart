import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import 'colors.dart';

class AppThemes {
  static ThemeData mainTheme = ThemeData(
      useMaterial3: false,
      appBarTheme: const AppBarTheme(
        backgroundColor: Colors.transparent,
        elevation: 0,
        shape: Border(bottom: BorderSide(color: Color(0xFF646464), width: 1.5)),
      ),
      textTheme: const TextTheme(
        displayLarge: TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.normal,
        ),
        displayMedium: TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.normal,
        ),
        displaySmall: TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.normal,
        ),
        headlineLarge: TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.normal,
        ),
        headlineMedium: TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.normal,
        ),
        headlineSmall: TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.normal,
        ),
        titleLarge: TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.normal,
        ),
        titleMedium: TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.bold,
        ),
        titleSmall: TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.normal,
        ),
      ),
      // inputDecorationTheme: const InputDecorationTheme(
      //   filled: true,
      //   fillColor: Color(0xFF3F3F3F),
      //   contentPadding: EdgeInsets.only(left: 8.0),
      //   border: InputBorder.none,
      //   focusedBorder: OutlineInputBorder(
      //     borderSide: BorderSide(color: Color(0xFF969696)),
      //     borderRadius: BorderRadius.all(Radius.circular(11)),
      //   ),
      // ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
            minimumSize: const Size(double.infinity, 48),
            backgroundColor: Colors.white,
            foregroundColor: const Color(0xFF303030),
            textStyle: const TextStyle(fontWeight: FontWeight.w500),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10.0),
            )),
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
            minimumSize: const Size(double.infinity, 48),
            backgroundColor: const Color(0xFF3F3F3F),
            foregroundColor: Colors.white,
            textStyle: const TextStyle(fontWeight: FontWeight.bold),
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8.0), side: const BorderSide(color: Color(0xFF646464)))),
      ),
      textButtonTheme: TextButtonThemeData(style: TextButton.styleFrom(foregroundColor: Colors.white)),
      chipTheme: const ChipThemeData(backgroundColor: Color(0xFF3F3F3F)),
      brightness: Brightness.dark,
      fontFamily: GoogleFonts.openSans().fontFamily,
      scaffoldBackgroundColor: Colors.black,
      primaryColorLight: Colors.white,
      textSelectionTheme: const TextSelectionThemeData(
        cursorColor: Colors.white,
      ));

  /// These themes get added in the [RouteDisplayPage]
  static ThemeData remediationTheme = mainTheme.copyWith(
      scaffoldBackgroundColor: AppColors.remediationColor,
      brightness: Brightness.light,
      textTheme: TextTheme(
        displayLarge: mainTheme.textTheme.displayLarge!.copyWith(color: Colors.black),
        displayMedium: mainTheme.textTheme.displayMedium!.copyWith(color: Colors.black),
        displaySmall: mainTheme.textTheme.displaySmall!.copyWith(color: Colors.black),
        headlineLarge: mainTheme.textTheme.headlineLarge!.copyWith(color: Colors.black),
        headlineMedium: mainTheme.textTheme.headlineMedium!.copyWith(color: Colors.black),
        headlineSmall: mainTheme.textTheme.headlineSmall!.copyWith(color: Colors.black),
        titleLarge: mainTheme.textTheme.titleLarge!.copyWith(color: Colors.black),
        titleMedium: mainTheme.textTheme.titleMedium!.copyWith(color: Colors.black),
        titleSmall: mainTheme.textTheme.titleSmall!.copyWith(color: Colors.black),
      ));
  static ThemeData routeFoundTheme = mainTheme.copyWith(
    scaffoldBackgroundColor: AppColors.routeFoundColor,
  );
  static ThemeData notYoursTheme = mainTheme.copyWith(
    scaffoldBackgroundColor: AppColors.notYoursColor,
  );
}
