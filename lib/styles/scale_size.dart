import 'dart:math';

import 'package:flutter/widgets.dart';

enum DeviceType { phone, tablet }

class ScaleUtil {
  static double textScaleFactor(BuildContext context, {double maxTextScaleFactor = 2}) {
    final width = MediaQuery.of(context).size.width;
    double val = (width / 1400) * maxTextScaleFactor;
    return max(1, min(val, maxTextScaleFactor));
  }

  static DeviceType deviceType(BuildContext context) {
    final width = MediaQuery.of(context).size.shortestSide;
    if(width <= 500) {
      return DeviceType.phone;
    }
    return DeviceType.tablet;
  }
}