import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sort_pro_printer_flutter/app/app_events/cubit/app_event_cubit.dart';
import 'package:sort_pro_printer_flutter/app/app_events/models/app_event.dart';
import 'package:sort_pro_printer_flutter/app/app_events/views/app_event_banner.dart';
import 'package:sort_pro_printer_flutter/app/app_lifecycle/cubit/app_lifecycle_cubit.dart';
import 'package:sort_pro_printer_flutter/app/authenticate_employee/cubit/authentication_cubit.dart';
import 'package:sort_pro_printer_flutter/app/authenticate_employee/views/logout_sheet.dart';
import 'package:sort_pro_printer_flutter/app/utils/timer_mixin.dart';
import 'package:sort_pro_printer_flutter/environment/environment_config.dart';
import 'package:sort_pro_printer_flutter/services/logs/i_log_service.dart';

/// Wraps the entire app in an overlay-style of widget to display AppEvent Banners throughout the application
class AppEventView extends StatefulWidget {
  final Widget child;
  final GlobalKey<NavigatorState> navigator<PERSON>ey;

  const AppEventView({super.key, required this.child, required this.navigatorKey});

  @override
  State<AppEventView> createState() => _AppEventViewState();
}

class _AppEventViewState extends State<AppEventView> with TimerMixin {
  final GlobalKey<AnimatedListState> _listKey = GlobalKey<AnimatedListState>();
  late ListModel<AppEvent> _list;
  ValueNotifier<bool> listEmpty = ValueNotifier(true);

  @override
  void initState() {
    super.initState();
    _list = ListModel<AppEvent>(
      listKey: _listKey,
      initialItems: <AppEvent>[],
      removedItemBuilder: _buildRemovedItem,
    );
  }

  @override
  void dispose() {
    listEmpty.dispose();
    super.dispose();
  }

  // Used to build list items that haven't been removed.
  Widget _buildItem(BuildContext context, int index, Animation<double> animation) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: AppEventBanner(
        appEvent: _list[index],
        animation: animation,
        onClose: () {
          _remove(index);
        },
        autoClose: _list[index].autoClose,
      ),
    );
  }

  /// The builder function used to build items that have been removed.
  ///
  /// Used to build an item after it has been removed from the list. This method
  /// is needed because a removed item remains visible until its animation has
  /// completed (even though it's gone as far as this ListModel is concerned).
  /// The widget will be used by the [AnimatedListState.removeItem] method's
  /// [AnimatedRemovedItemBuilder] parameter.
  Widget _buildRemovedItem(AppEvent item, BuildContext context, Animation<double> animation) {
    return AppEventBanner(
      appEvent: item,
      animation: animation,
      // No gesture detector here: we don't want removed items to be interactive.
      onClose: () {},
      autoClose: item.autoClose,
    );
  }

  // Insert the "next item" into the list model.
  void _insert(AppEvent appEvent) {
    final int index = _list.length;
    _list.insert(index, appEvent);
    checkEmpty();
  }

  // Remove the selected item from the list model.
  void _remove(int index) {
    _list.removeAt(index);
    checkEmpty();
  }

  checkEmpty() {
    listEmpty.value = _list._items.isEmpty;
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        widget.child,
        ValueListenableBuilder<bool>(
          valueListenable: listEmpty,
          builder: (context, empty, _) => IgnorePointer(
            ignoring: empty,
            child: Align(
              alignment: Alignment.bottomCenter,
              child: BlocListener<AppEventCubit, AppEvent>(
                listener: (context, appEvent) {
                  if (appEvent.appEventType != AppEventType.empty &&
                      _list._items.where((element) => element.appEventCode == appEvent.appEventCode).isEmpty) {
                    _insert(appEvent);
                  }

                  if (appEvent.appEventCode == AppEventCode.printerReady) {
                    final List<AppEvent> printerEvents = _list._items
                        .where((element) =>
                            element.appEventCode == AppEventCode.printerHeadOpen ||
                            element.appEventCode == AppEventCode.printerPaperOut ||
                            element.appEventCode == AppEventCode.printerConnectionError ||
                            element.appEventCode == AppEventCode.printerNotFound ||
                            element.appEventCode == AppEventCode.printerDisconnected)
                        .toList();
                    for (AppEvent printerEvent in printerEvents) {
                      _remove(_list._items.indexOf(printerEvent));
                    }
                  }

                  if (appEvent.appEventCode == AppEventCode.dataOutOfSync) {
                    final List<AppEvent> retryEvents =
                        _list._items.where((element) => element.appEventCode == AppEventCode.retrySync).toList();
                    for (AppEvent retryEvent in retryEvents) {
                      _remove(_list._items.indexOf(retryEvent));
                    }
                  }

                  // Remove warnings and errors related to connection issues
                  if (appEvent.appEventCode == AppEventCode.connectionReady) {
                    final List<AppEvent> networkEvents = _list._items
                        .where((element) =>
                            element.appEventCode == AppEventCode.noInternet || element.appEventCode == AppEventCode.dataOutOfSync)
                        .toList();
                    for (AppEvent networkEvent in networkEvents) {
                      _remove(_list._items.indexOf(networkEvent));
                    }
                  }

                  // This event gets triggered when the user taps anywhere in the app or triggers the scanner
                  if (appEvent.appEventCode == AppEventCode.userActive) {
                    final timerDuration = Duration(seconds: EnvironmentConfig.activeSessionTimeoutInSeconds - LogoutSheet.sheetTimeout);
                    cancelTimer();
                    startTimer(timerDuration, () {
                      final context = widget.navigatorKey.currentState!.overlay!.context;
                      final authCubit = context.read<AuthenticationCubit>();
                      final authenticated = authCubit.authenticated;
                      final appState = context.read<AppLifecycleCubit>().state;
                      if (authenticated) {
                        if (appState == AppLifecycleState.resumed) {
                          _showConfirmationSheet(context);
                        } else {
                          context.read<ILogService>().event("User Logged out due to inactivity from the background");
                          authCubit.logout();
                        }
                      }
                    });
                  }
                },
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: AnimatedList(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    key: _listKey,
                    initialItemCount: _list.length,
                    itemBuilder: _buildItem,
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  _showConfirmationSheet(BuildContext context) async {
    showModalBottomSheet(
        context: context,
        builder: (context) {
          return const LogoutSheet();
        });
  }
}

typedef RemovedItemBuilder<T> = Widget Function(T item, BuildContext context, Animation<double> animation);

/// Keeps a Dart [List] in sync with an [AnimatedList].
///
/// The [insert] and [removeAt] methods apply to both the internal list and
/// the animated list that belongs to [listKey].
///
/// This class only exposes as much of the Dart List API as is needed by the
/// sample app. More list methods are easily added, however methods that
/// mutate the list must make the same changes to the animated list in terms
/// of [AnimatedListState.insertItem] and [AnimatedList.removeItem].
class ListModel<E> {
  ListModel({
    required this.listKey,
    required this.removedItemBuilder,
    Iterable<E>? initialItems,
  }) : _items = List<E>.from(initialItems ?? <E>[]);

  final GlobalKey<AnimatedListState> listKey;
  final RemovedItemBuilder<E> removedItemBuilder;
  final List<E> _items;

  AnimatedListState? get _animatedList => listKey.currentState;

  void insert(int index, E item) {
    _items.insert(index, item);
    _animatedList!.insertItem(index);
  }

  E removeAt(int index) {
    final E removedItem = _items.removeAt(index);
    if (removedItem != null) {
      _animatedList!.removeItem(
        index,
        (BuildContext context, Animation<double> animation) {
          return removedItemBuilder(removedItem, context, animation);
        },
      );
    }
    return removedItem;
  }

  int get length => _items.length;

  E operator [](int index) => _items[index];

  int indexOf(E item) => _items.indexOf(item);
}
