import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:sort_pro_printer_flutter/app/app_events/models/app_event.dart';

import '../../../services/data_provider/data_provider_service.dart';

class AppEventBanner extends StatefulWidget {
  final AppEvent appEvent;
  final VoidCallback onClose;
  final Animation<double> animation;
  final bool autoClose;

  const AppEventBanner({Key? key, required this.appEvent, required this.onClose, required this.animation, this.autoClose = false})
      : super(key: key);

  @override
  State<AppEventBanner> createState() => _AppEventBannerState();
}

class _AppEventBannerState extends State<AppEventBanner> with TickerProviderStateMixin {
  late final AnimationController _animationController;
  late final ValueNotifier<bool> _isDismissed;
  Timer? _autoCloseAnimationTimer;

  AppEvent get appEvent => widget.appEvent;

  bool get autoClose => widget.autoClose;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 250), // Duration of the fade-in animation
    );
    _animationController.forward().then((value) {
      if (autoClose) {
        _autoCloseAnimationTimer = Timer(const Duration(seconds: 3), () {
          widget.onClose();
        });
      }
    });
    // Mark the widget as dismissed when removed from the view either by the timer or by clicking dismiss
    _isDismissed = ValueNotifier(false);
    _animationController.addListener(() {
      if (_animationController.isDismissed) {
        widget.onClose();
      }
    });
  }

  @override
  void dispose() {
    _isDismissed.dispose();
    _animationController.dispose();
    _autoCloseAnimationTimer?.cancel();
    super.dispose();
  }

  Color _getColor() {
    switch (appEvent.appEventType) {
      case AppEventType.success:
        return const Color(0xFF1FAC42);
      case AppEventType.warning:
        return const Color(0xFFCFAD00);
      case AppEventType.error:
        return const Color(0xFFFF453A);
      default:
        return const Color(0xFF3F3F3F);
    }
  }

  Widget _getIcon() {
    switch (appEvent.appEventType) {
      case AppEventType.success:
        return const Icon(
          Icons.check_circle,
          color: Colors.white,
          size: 42,
        );
      case AppEventType.warning:
        return const Icon(
          Icons.warning_rounded,
          color: Colors.white,
          size: 42,
        );
      case AppEventType.error:
        return const Icon(
          Icons.error,
          color: Colors.white,
          size: 42,
        );
      default:
        return const SizedBox.shrink();
    }
  }

  String _getAppEventTitle(AppEventType appEventType, AppEventCode appEventCode, {Object? appEventObjects}) {
    switch (appEventCode) {
      case AppEventCode.dataOutOfSync:
        return AppLocalizations.of(context)!.alert_warning_unableToFetch_title;
      case AppEventCode.retrySync:
        if (appEventObjects is int) {
          return AppLocalizations.of(context)!.alert_warning_retry_sync(DataProviderService.maxRetryCount, appEventObjects);
        }
      case AppEventCode.noInternet:
        return AppLocalizations.of(context)!.alert_error_unableToFetch_title;
      case AppEventCode.printerHeadOpen:
        if (appEventType == AppEventType.warning) {
          return AppLocalizations.of(context)!.alert_warning_printerHeadOpen_title;
        } else if (appEventType == AppEventType.error) {
          return AppLocalizations.of(context)!.alert_error_printerUnableToPrint_title;
        }
      case AppEventCode.printerPaperOut:
        if (appEventType == AppEventType.warning) {
          return AppLocalizations.of(context)!.alert_warning_printerOutOfPaper_title;
        } else if (appEventType == AppEventType.error) {
          return AppLocalizations.of(context)!.alert_error_printerUnableToPrint_title;
        }
      case AppEventCode.printerDisconnected:
        if (appEventType == AppEventType.warning) {
          return AppLocalizations.of(context)!.alert_warning_printerDisabled_title;
        } else if (appEventType == AppEventType.error) {
          return AppLocalizations.of(context)!.alert_error_printerDisconnected_title;
        }
      case AppEventCode.printerConnectionError:
        return AppLocalizations.of(context)!.alert_error_printerConnectionError_title;
      case AppEventCode.printerReady:
        return AppLocalizations.of(context)!.alert_success_printerReady_title;
      case AppEventCode.pinInvalid:
        return AppLocalizations.of(context)!.alert_error_pinInvalid;
      case AppEventCode.pinNotFound:
        return AppLocalizations.of(context)!.alert_error_pinNotFound;
      case AppEventCode.labelPrinted:
        return AppLocalizations.of(context)!.remediate_snckbr_labelPrinted;
      case AppEventCode.invalidRemBarcode:
        return AppLocalizations.of(context)!.remediate_invalid_rem_barcode;
      case AppEventCode.printerNotFound:
        if (appEventType == AppEventType.warning) {
          return AppLocalizations.of(context)!.alert_warning_printerDisabled_title;
        } else if (appEventType == AppEventType.error) {
          return AppLocalizations.of(context)!.alert_error_noPrinterPaired_title;
        }
      case AppEventCode.unableToExtractLabelInfoNoConnection:
      case AppEventCode.unableToExtractLabelInfoGeneric:
        return AppLocalizations.of(context)!.remediationPage_lbl_unableToExtractInfoTitle;
      default:
    }
    return "";
  }

  String _getAppEventMessage(AppEventType appEventType, AppEventCode appEventCode, Object? appEventDetails) {
    switch (appEventCode) {
      case AppEventCode.dataOutOfSync:
        if (appEventDetails != null) {
          return AppLocalizations.of(context)!
              .alert_error_unableToFetch_message(AppLocalizations.of(context)!.alert_error_unableToFetch_lastFetchTime(appEventDetails));
        }
      case AppEventCode.noInternet:
        if (appEventDetails != null) {
          return AppLocalizations.of(context)!
              .alert_error_unableToFetch_message(AppLocalizations.of(context)!.alert_error_unableToFetch_lastFetchTime(appEventDetails));
        } else {
          return AppLocalizations.of(context)!.alert_error_unableToFetch_message('');
        }
      case AppEventCode.printerHeadOpen:
        return AppLocalizations.of(context)!.alert_warning_printerHeadOpen_message;
      case AppEventCode.printerPaperOut:
        return AppLocalizations.of(context)!.alert_warning_printerOutOfPaper_message;
      case AppEventCode.printerConnectionError:
        return AppLocalizations.of(context)!.alert_error_printerConnectionError_message;
      case AppEventCode.printerReady:
        return AppLocalizations.of(context)!.alert_success_printerReady_message;
      case AppEventCode.printerNotFound:
        if (appEventType == AppEventType.warning) {
          return AppLocalizations.of(context)!.alert_warning_printerDisabled_message;
        } else if (appEventType == AppEventType.error) {
          return AppLocalizations.of(context)!.alert_error_noPrinterPaired_message;
        }
      case AppEventCode.printerDisconnected:
        if (appEventType == AppEventType.warning) {
          return AppLocalizations.of(context)!.alert_warning_printerDisabled_message;
        }
      case AppEventCode.unableToExtractLabelInfoNoConnection:
        return AppLocalizations.of(context)!.remediationPage_lbl_unableToExtractInfoNoConnection;
      case AppEventCode.unableToExtractLabelInfoGeneric:
        return AppLocalizations.of(context)!.remediationPage_lbl_unableToExtractInfoGeneric;
      // These have no message details
      case AppEventCode.pinInvalid:
      case AppEventCode.pinNotFound:
      default:
    }
    return "";
  }

  @override
  Widget build(BuildContext context) {
    final String title = _getAppEventTitle(appEvent.appEventType, appEvent.appEventCode, appEventObjects: appEvent.appEventDetails);
    final String message = _getAppEventMessage(appEvent.appEventType, appEvent.appEventCode, appEvent.appEventDetails);
    return ValueListenableBuilder<bool>(
        valueListenable: _isDismissed,
        builder: (context, dismissed, value) {
          return dismissed
              ? const SizedBox.shrink()
              : SizeTransition(
                  sizeFactor: widget.animation, // Use the animation as the opacity value
                  child: Material(
                    elevation: 24,
                    color: _getColor(),
                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(100)),
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Row(
                        children: [
                          const SizedBox(
                            width: 4.0,
                          ),
                          _getIcon(),
                          const SizedBox(
                            width: 8.0,
                          ),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  title,
                                  style: Theme.of(context)
                                      .textTheme
                                      .bodyMedium!
                                      .copyWith(fontFamily: GoogleFonts.inter().fontFamily, fontSize: 16),
                                ),
                                if (message.isNotEmpty)
                                  const SizedBox(
                                    height: 4.0,
                                  ),
                                if (message.isNotEmpty)
                                  Text(
                                    message,
                                    style: Theme.of(context)
                                        .textTheme
                                        .bodySmall!
                                        .copyWith(fontFamily: GoogleFonts.inter().fontFamily, fontSize: 14),
                                  ),
                              ],
                            ),
                          ),
                          IconButton(
                            onPressed: () {
                              _autoCloseAnimationTimer?.cancel();
                              widget.onClose();
                            },
                            icon: const Icon(Icons.close),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
        });
  }
}
