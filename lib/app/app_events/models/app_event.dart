import 'package:uuid/uuid.dart';

enum AppEventType {
  success,
  warning,
  error,
  empty,
}

enum AppEventCode {
  empty,
  connectionReady,
  dataOutOfSync,
  retrySync,
  noInternet,
  pinInvalid,
  pinNotFound,
  printerNotFound,
  printerConnectionError,
  printerDisconnected,
  printerReady,
  printerHeadOpen,
  printerPaperOut,
  labelPrinted,
  userActive,
  invalidRemBarcode,
  unableToExtractLabelInfoGeneric,
  unableToExtractLabelInfoNoConnection,
}

/// A class to represent important App-level events
/// that can be raised through the bubble tree
class AppEvent {
  late final String id; // uniquely identify this event for the UI
  final AppEventType appEventType;
  final AppEventCode appEventCode;
  final bool autoClose;
  final Object? appEventDetails;

  AppEvent._(this.appEventType, this.appEventCode, {this.appEventDetails, this.autoClose = false}) {
    id = const Uuid().v1();
  }

  factory AppEvent.empty([AppEventCode? appEventCode]) => AppEvent._(AppEventType.empty, appEventCode ?? AppEventCode.empty);

  factory AppEvent.error(AppEventCode appEventCode, {Object? appEventDetails, autoClose = false}) =>
      AppEvent._(AppEventType.error, appEventCode, appEventDetails: appEventDetails, autoClose: autoClose);

  // all success messages auto close after 3 seconds by default
  factory AppEvent.success(AppEventCode appEventCode, {Object? appEventDetails, autoClose = true}) =>
      AppEvent._(AppEventType.success, appEventCode, appEventDetails: appEventDetails, autoClose: autoClose);

  factory AppEvent.warning(AppEventCode appEventCode, {Object? appEventDetails, autoClose = false}) =>
      AppEvent._(AppEventType.warning, appEventCode, appEventDetails: appEventDetails, autoClose: autoClose);

  @override
  String toString() {
    return 'AppEvent{appEventType: $appEventType, appEventCode: $appEventCode, appEventDetails: $appEventDetails}';
  }
}
