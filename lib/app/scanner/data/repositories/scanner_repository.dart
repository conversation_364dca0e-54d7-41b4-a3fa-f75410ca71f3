import 'package:sort_pro_printer_flutter/app/scanner/data/models/barcode.dart';
import 'package:sort_pro_printer_flutter/services/scanner/i_scanner_service.dart';

class ScannerRepository {
  final IScannerService _scannerService;

  ScannerRepository(this._scannerService);

  Stream<Barcode> get scannedBarcodes => _scannerService.scannedBarcodes;
  Stream<void> get scannerTriggered => _scannerService.scannerTriggered;

  Future<void> playInvalidBarcodeFeedback() async => await _scannerService.playInvalidBarcodeFeedback();

  dispose() {
    _scannerService.dispose();
  }
}
