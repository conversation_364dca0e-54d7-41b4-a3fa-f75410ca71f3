import 'package:sort_pro_printer_flutter/app/scanner/data/models/zebra_scanner.dart';
import 'package:sort_pro_printer_flutter/utils/ups_validator.dart';

/// The Symbology recognized by the app
enum BarcodeSymbology { code128, code39, pdf417, qrCode, unknown }

/// The different Barcode Types that the app supports
/// The IDs are used by the scanlog and are the same as the one's in the legacy app
enum BarcodeType {
  unknown(id: 0),
  ngb(id: 1),
  puro2d(id: 2),
  ups1z(id: 3),
  upsMaxicode(id: 4),
  manualWayBill(id: 5),
  legacyPurolator(id: 7),
  upsManualWaybill(id: 8),
  employeeBadge(id: 0),
  printerMac(id: 0),
  upsPostalCode(id: 0);

  const BarcodeType({
    required this.id,
  });

  final int id;
}

/// Represents a barcode scanned
class Barcode {
  final String rawData;
  final BarcodeSymbology barcodeSymbology;

  const Barcode(this.rawData, this.barcodeSymbology);

  bool get shippingBarcode {
    return barcodeType != BarcodeType.employeeBadge && barcodeType != BarcodeType.printerMac && barcodeType != BarcodeType.unknown;
  }

  bool get printRemLabel {
    return barcodeSymbology == BarcodeSymbology.qrCode && rawData == "PRINT-REM-LABEL";
  }

  static Barcode printRemLabelBarcode = const Barcode("PRINT-REM-LABEL", BarcodeSymbology.qrCode);
  static const Barcode empty = Barcode("", BarcodeSymbology.unknown);

  BarcodeType get barcodeType {
    final alphanumericRegExp = RegExp(r'[A-Za-z0-9]');
    final alphabetRegExp = RegExp(r'[A-Za-z]');
    final numericRegExp = RegExp(r'[0-9]');
    final upsWbRegExp = RegExp(r'[A-Za-z 01345]');

    if (rawData.contains("~") && rawData.contains("|") && barcodeSymbology == BarcodeSymbology.pdf417) {
      return BarcodeType.puro2d;
    } else if (rawData.length == 12 &&
        barcodeSymbology == BarcodeSymbology.code128 &&
        alphanumericRegExp.hasMatch(rawData) &&
        alphabetRegExp.hasMatch(rawData.substring(0, 1)) &&
        numericRegExp.hasMatch(rawData.substring(1))) {
      return BarcodeType.manualWayBill;
    } else if (rawData.length == 34 && alphanumericRegExp.hasMatch(rawData)) {
      return BarcodeType.ngb;
    } else if (rawData.length == 18 &&
        alphanumericRegExp.hasMatch(rawData) &&
        rawData.startsWith("1Z") &&
        UpsValidator.ups1zValidation(rawData)) {
      return BarcodeType.ups1z;
      // todo this one seems to be wrong according to the Legacy Docs.
      // } else if (rawData.length == 9 &&
      //     barcodeSymbology == BarcodeSymbology.code128 &&
      //     alphanumericRegExp.hasMatch(rawData)) {
      //   return BarcodeType.upsPostalCode;
    } else if (rawData.length == 8 &&
        barcodeSymbology == BarcodeSymbology.code39 &&
        alphanumericRegExp.hasMatch(rawData) &&
        rawData.startsWith("60")) {
      return BarcodeType.employeeBadge;
    } else if (rawData.contains(String.fromCharCode(29))) {
      return BarcodeType.upsMaxicode;
    } else if (alphanumericRegExp.hasMatch(rawData) &&
        barcodeSymbology == BarcodeSymbology.code39 &&
        ((rawData.length == 12 && alphabetRegExp.hasMatch(rawData.substring(0, 1)) && numericRegExp.hasMatch(rawData.substring(1, 11)) ||
            (rawData.length <= 12 &&
                rawData.length >= 9 &&
                alphabetRegExp.hasMatch(rawData.substring(0, 3)) &&
                numericRegExp.hasMatch(rawData.substring(3)))))) {
      return BarcodeType.legacyPurolator;
    } else if (alphanumericRegExp.hasMatch(rawData) && rawData.length == 12) {
      return BarcodeType.printerMac;
    } else if (alphanumericRegExp.hasMatch(rawData.substring(0)) &&
        barcodeSymbology == BarcodeSymbology.code128 &&
        rawData.length == 11 &&
        upsWbRegExp.hasMatch(rawData.substring(0, 1)) &&
        numericRegExp.hasMatch(rawData.substring(1)) &&
        int.parse(rawData.substring(1)) % 10 != 0) {
      return BarcodeType.upsManualWaybill;
    }
    return BarcodeType.unknown;
  }

  factory Barcode.fromZebraScan(ZebraScan zebraScan) {
    final rawData = zebraScan.scanData;
    var barcodeSymbology = BarcodeSymbology.unknown;

    // get the symbology
    switch (zebraScan.symbology) {
      case ZebraScan.code128Symbology:
        barcodeSymbology = BarcodeSymbology.code128;
        break;
      case ZebraScan.code39Symbology:
        barcodeSymbology = BarcodeSymbology.code39;
        break;
      case ZebraScan.pdf417Symbology:
        barcodeSymbology = BarcodeSymbology.pdf417;
        break;
      case ZebraScan.qrCodeSymbology:
        barcodeSymbology = BarcodeSymbology.qrCode;
        break;
    }

    return Barcode(rawData, barcodeSymbology);
  }
}
