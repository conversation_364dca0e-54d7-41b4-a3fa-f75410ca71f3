// Represents the Scan Data from a Zebra Scanner's Scan action
class ZebraScan {

  // Symbology given by the Zebra Scanner
  static const code128Symbology = "LABEL-TYPE-CODE128";
  static const code39Symbology = "LABEL-TYPE-CODE39";
  static const pdf417Symbology = "LABEL-TYPE-PDF417";
  static const qrCodeSymbology = "LABEL-TYPE-QRCODE";

  final String scanData;
  final String symbology;
  final String dateTime;

  ZebraScan(
    this.scanData,
    this.symbology,
    this.dateTime,
  );

  static ZebraScan fromJson(Map<String, dynamic> json) => ZebraScan(
        json['scanData'],
        json['symbology'],
        json['dateTime'],
      );

  @override
  String toString() {
    return 'ZebraScan{scanData: $scanData, symbology: $symbology, dateTime: $dateTime}';
  }
}
