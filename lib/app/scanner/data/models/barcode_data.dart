import 'package:equatable/equatable.dart';

/// This class is what we get from parsed barcodes
/// This is also what we get from lookups?
class BarcodeData extends Equatable {
  final bool? isDeliveryTimeFromBarcode;
  final bool? isDiversionCodeFromBarcode;
  final String? pinNumber;
  final String? diversionCode;
  final String? city;
  final String? postalCode;
  final String? streetNumber;
  final String? customerName;
  final String? deliveryTime;
  final String? shipmentType;
  final String? deliveryType;
  final String? handlingClassType;
  final String? suiteNumber;
  final String? addressLine1;
  final String? addressLine2;
  final String? employeeId;
  final String? printerMac;

  const BarcodeData({
    this.isDeliveryTimeFromBarcode,
    this.isDiversionCodeFromBarcode,
    this.pinNumber,
    this.diversionCode,
    this.city,
    this.postalCode,
    this.streetNumber,
    this.addressLine1,
    this.addressLine2,
    this.customerName,
    this.deliveryTime,
    this.shipmentType,
    this.deliveryType,
    this.handlingClassType,
    this.suiteNumber,
    this.employeeId,
    this.printerMac,
  });

  @override
  List<Object?> get props => [
        isDeliveryTimeFromBarcode,
        isDiversionCodeFromBarcode,
        pinNumber,
        diversionCode,
        city,
        postalCode,
        streetNumber,
        customerName,
        deliveryTime,
        shipmentType,
        deliveryType,
        handlingClassType,
        suiteNumber,
        addressLine1,
        addressLine2,
        employeeId,
        printerMac,
      ];
}
