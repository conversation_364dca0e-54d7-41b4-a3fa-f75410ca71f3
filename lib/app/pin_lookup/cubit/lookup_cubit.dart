import 'dart:async';
import 'dart:collection';
import 'dart:convert';

import 'package:equatable/equatable.dart';
import 'package:hydrated_bloc/hydrated_bloc.dart';
import 'package:sort_pro_printer_flutter/app/app_events/cubit/app_event_cubit.dart';
import 'package:sort_pro_printer_flutter/app/app_events/models/app_event.dart';
import 'package:sort_pro_printer_flutter/app/manage/cubit/app_setting_cubit.dart';
import 'package:sort_pro_printer_flutter/app/manage/cubit/user_setting_cubit.dart';
import 'package:sort_pro_printer_flutter/app/manage/data/models/supported_modes.dart';
import 'package:sort_pro_printer_flutter/app/manage/data/models/terminal.dart';
import 'package:sort_pro_printer_flutter/app/manage/data/repositories/setting_repository.dart';
import 'package:sort_pro_printer_flutter/app/peripherals/cubit/peripherals_cubit.dart';
import 'package:sort_pro_printer_flutter/app/pin_lookup/data/models/lookup_result.dart';
import 'package:sort_pro_printer_flutter/app/pin_lookup/data/models/report_data.dart';
import 'package:sort_pro_printer_flutter/app/scanner/data/models/barcode.dart';
import 'package:sort_pro_printer_flutter/app/scanner/data/models/barcode_data.dart';
import 'package:sort_pro_printer_flutter/app/scanner/data/repositories/scanner_repository.dart';
import 'package:sort_pro_printer_flutter/app/sounds/app_sounds.dart';
import 'package:sort_pro_printer_flutter/extensions/string_nullable.dart';
import 'package:sort_pro_printer_flutter/services/data_provider/i_data_provider_service.dart';
import 'package:sort_pro_printer_flutter/services/device_info/i_device_info_service.dart';
import 'package:sort_pro_printer_flutter/services/logs/i_log_service.dart';
import 'package:sort_pro_printer_flutter/services/i_lookup_service.dart';
import 'package:sort_pro_printer_flutter/services/ring_scanner/i_ringer_service.dart';
import 'package:sort_pro_printer_flutter/services/sound/i_sound_service.dart';
import 'package:sort_pro_printer_flutter/services/vibration/i_vibration_service.dart';
import 'package:sort_pro_printer_flutter/utils/barcode_parser.dart';
import 'package:sort_pro_printer_flutter/utils/date_time_formatter.dart';
import 'package:sort_pro_printer_flutter/utils/exceptions.dart';
import 'package:sort_pro_printer_flutter/utils/pin_validator.dart';

part 'lookup_state.dart';

class LookupCubit extends Cubit<LookupState> with HydratedMixin {
  final AppEventCubit appEventCubit;
  final UserSettingCubit userSettingCubit;
  final AppSettingCubit appSettingCubit;
  final PeripheralsCubit peripheralsCubit;
  final ScannerRepository scannerRepository;
  final SettingRepository settingRepository;
  final ILookupService lookupService;
  final IDataProviderService dataProviderService;
  final IDeviceInfoService deviceInfoService;
  final PinValidator pinValidator;
  final ILogService logService;
  final ISoundService soundService;
  final IVibrationService vibrationService;
  final String userId;
  final String mode;
  LookupResult? currentResult;
  LookupResult? pastResult;
  ReportData _reportData = ReportData();

  Terminal? terminal;

  List<String> scannedPins = []; // keep score of all the pins scanned

  LookupCubit({
    required this.scannerRepository,
    required this.settingRepository,
    required this.peripheralsCubit,
    required this.appEventCubit,
    required this.userSettingCubit,
    required this.appSettingCubit,
    required this.lookupService,
    required this.logService,
    required this.soundService,
    required this.vibrationService,
    required this.dataProviderService,
    required this.deviceInfoService,
    required this.pinValidator,
    required this.userId,
    required this.mode,
  }) : super(const WaitingForScan()) {
    hydrate();
    appSettingCubit.stream.listen((event) {
      terminal = event.terminal;
    });
  }

  bool processingBarcode = false;
  Queue<Barcode> barcodesScannedQueue = Queue<Barcode>();

  _processBarcodeScanned(Barcode barcode) async {
    // add the recently scanned barcode to the queue.
    barcodesScannedQueue.add(barcode);

    // if already processing barcodes, don't worry about the rest.
    if (processingBarcode) return;

    processingBarcode = true;

    // while the Queue is not empty, process them one by one
    while (barcodesScannedQueue.isNotEmpty) {
      final barcode = barcodesScannedQueue.first;
      await _lookupPackageInfoFromBarcode(barcode);
      barcodesScannedQueue.removeFirst();
    }

    // done processing
    processingBarcode = false;
  }

  _processManualRemLabel(Barcode barcode) async {
    terminal ??= await settingRepository.getTerminal();
    final dataDescription = await dataProviderService.getDataDescriptionForTerminal(terminal!.id);
    pastResult = currentResult;
    currentResult = LookupResult.emptyRemResult(terminalNumber: terminal!.id, routePlanId: dataDescription?.routePlanId ?? "unavailable");
    _reportData = _reportData.copyWith(parcelsScanned: _reportData.parcelsScanned + 1, manualRemLabels: _reportData.manualRemLabels + 1);
    emit(
      LookupPrintResults(
        currentResult: currentResult,
        pastResult: pastResult,
        reportData: _reportData,
      ),
    );
  }

  _lookupPackageInfoFromBarcode(Barcode barcode) async {
    processingBarcode = true;
    terminal ??= await settingRepository.getTerminal();

    // process a manual rem print
    if (barcode.printRemLabel) {
      _processManualRemLabel(barcode);
      processingBarcode = false;
      return;
    }

    emit(const LookingUpPrintResults());

    late final BarcodeData barcodeData;
    // Get the info from the barcode
    try {
      barcodeData = BarcodeParser.decodeBarcode(barcode);
    } on DecodeBarcodeException catch (_) {
      barcodeData = const BarcodeData();
    }

    // Make sure we got the pin extracted
    if (barcodeData.pinNumber == null) {
      if (peripheralsCubit.state.ringScannerStatus == RingScannerStatus.connected) {
        scannerRepository.playInvalidBarcodeFeedback();
      }
      _reportData = _reportData.copyWith(parcelsScanned: _reportData.parcelsScanned + 1, invalidScans: _reportData.invalidScans + 1);
      soundService.playSoundFromAssets(AppSounds.errorSoundAsset);
      vibrationService.vibrate(intensity: VibrationIntensity.high);
      appEventCubit.emit(AppEvent.error(AppEventCode.pinNotFound));
      if (currentResult != null) {
        emit(
          PinInvalidState(
            currentResult: currentResult,
            pastResult: pastResult,
            reportData: _reportData,
          ),
        );
      } else {
        emit(const WaitingForScan());
      }
      processingBarcode = false;
      return;
    }

    final String pin = barcodeData.pinNumber!;

    // Validate the pin
    final validPin = pinValidator.validatePin(pin);
    if (!validPin) {
      if (peripheralsCubit.state.ringScannerStatus == RingScannerStatus.connected) {
        scannerRepository.playInvalidBarcodeFeedback();
      }
      _reportData = _reportData.copyWith(parcelsScanned: _reportData.parcelsScanned + 1, invalidScans: _reportData.invalidScans + 1);
      soundService.playSoundFromAssets(AppSounds.errorSoundAsset);
      vibrationService.vibrate(intensity: VibrationIntensity.high);
      appEventCubit.emit(AppEvent.error(AppEventCode.pinInvalid));
      if (currentResult != null) {
        emit(
          PinInvalidState(
            currentResult: currentResult,
            pastResult: pastResult,
            reportData: _reportData,
          ),
        );
      } else {
        emit(const WaitingForScan());
      }
      processingBarcode = false;
      return;
    }

    // Look up the info for the PIN
    logService.event("Looking up PIN", additionalProperties: {"pin": pin});

    LookupResult pinInfoResult =
        await lookupService.lookUpSortInstructions(barcodeData, barcode.barcodeType, terminal!.id, userSettingCubit.userId);


    // play the sound and vibration
    if (pinInfoResult.lookupResultType == LookupResultType.misdirect) {
      soundService.playSoundFromAssets(AppSounds.misdirectSoundAsset);
      vibrationService.vibrate(intensity: VibrationIntensity.low);
    } else if (pinInfoResult.lookupResultType == LookupResultType.remediation) {
      soundService.playSoundFromAssets(AppSounds.remSoundAsset);
      vibrationService.vibrate(intensity: VibrationIntensity.medium);
    }

    logService.event("Lookup result", additionalProperties: {"pin": pin, "lookup": jsonEncode(pinInfoResult)});

    final mode = userSettingCubit.state.mode ?? "";
    // These is to populate more data into the ScanLog
    pinInfoResult =
        await _assignExtraFieldsToLookupResult(barcode.barcodeType, barcodeData, SupportedModes.fromName(mode), userId, pinInfoResult);

    /// This will take the user into the rem mode when scanning from both mode
    if (mode == SupportedModes.both.name && pinInfoResult.isRem) {
      emit(
        LookupPrintResults(
          currentResult: currentResult,
          pastResult: pastResult,
          reportData: _reportData,
          printLabel: false,
          shouldRemediate: true
        ),
      );
    } else {
      pastResult = currentResult;
      currentResult = pinInfoResult;

      // update report data
      _updateReportData(pinInfoResult);

      lookupService.notifyLookupResultFound(pinInfoResult);
      emit(
        LookupPrintResults(
          currentResult: currentResult,
          pastResult: pastResult,
          reportData: _reportData,
        ),
      );
    }
    processingBarcode = false;
  }

  lookupPackageInfoFromBarcode(Barcode barcode) async {
    _processBarcodeScanned(barcode);
  }

  confirmPrintForMode3(LookupResult? result) async {
    final mode = userSettingCubit.state.mode ?? "";
    if (result != null) {
      pastResult = currentResult;
      currentResult = await _assignExtraFieldsToLookupResult(null, null, SupportedModes.fromName(mode), userId, result);
      lookupService.notifyLookupResultFound(result);
      // update report data
      _updateReportData(result);
      if(!isClosed) {
        emit(
          LookupPrintResults(
              currentResult: currentResult,
              pastResult: pastResult,
              reportData: _reportData,
              printLabel: false
          ),
        );
      }
    }
  }

  void _updateReportData(LookupResult pinInfoResult) {
    // increase the report data only if the scanned pin is not a duplicate
    if (!scannedPins.contains(pinInfoResult.pin)) {
      switch (pinInfoResult.lookupResultType) {
        case LookupResultType.routing:
          _reportData = _reportData.copyWith(parcelsScanned: _reportData.parcelsScanned + 1, rteLabels: _reportData.rteLabels + 1);
          break;
        case LookupResultType.crossDock:
          _reportData =
              _reportData.copyWith(parcelsScanned: _reportData.parcelsScanned + 1, crossdockLabels: _reportData.crossdockLabels + 1);
          break;
        case LookupResultType.srr:
        case LookupResultType.srrA:
        case LookupResultType.srrR:
        case LookupResultType.srrP:
          _reportData = _reportData.copyWith(parcelsScanned: _reportData.parcelsScanned + 1, srrLabels: _reportData.srrLabels + 1);
          break;
        case LookupResultType.misdirect:
          _reportData =
              _reportData.copyWith(parcelsScanned: _reportData.parcelsScanned + 1, misdirectLabels: _reportData.misdirectLabels + 1);
          break;
        case LookupResultType.remediation:
          _reportData = _reportData.copyWith(parcelsScanned: _reportData.parcelsScanned + 1, remLabels: _reportData.remLabels + 1);
          break;
      }
      scannedPins.add(pinInfoResult.pin);
    }
  }

  // These are fields from the barcode that we need to make sure are in the lookup result for the ScanLog to receive
  // I know, this sucks.
  Future<LookupResult> _assignExtraFieldsToLookupResult(
      BarcodeType? barcodeType, BarcodeData? barcodeData, SupportedModes mode, String userId, LookupResult result) async {
    final packageTypeId = appSettingCubit.state.packageType.id;
    final dataDescription = await dataProviderService.getDataDescriptionForTerminal(result.terminalNumber);
    final deviceId = await deviceInfoService.getDeviceId();
    final appVersion = await deviceInfoService.getAppVersion();

    return result.copyWith(
        ssDeviceId: "${deviceId}_V$appVersion",
        barcodeType: (barcodeType?.id ?? result.barcodeType).toString(),
        fromStreetNumber: result.fromStreetNumber.isNullOrEmpty() ? barcodeData?.streetNumber : result.fromStreetNumber,
        streetName: result.streetName.isNullOrEmpty() ? barcodeData?.addressLine1 : result.streetName,
        unitNumber: result.unitNumber.isNullOrEmpty() ? barcodeData?.suiteNumber : result.unitNumber,
        customerName: result.customerName.isNullOrEmpty() ? barcodeData?.customerName : result.customerName,
        deliveryTime: barcodeData?.deliveryTime,
        shipmentType: barcodeData?.shipmentType,
        deliveryType: barcodeData?.deliveryType,
        diversionCode: barcodeData?.diversionCode,
        handlingClassType: barcodeData?.handlingClassType,
        scanDateTime: formatDateTimeWithTimeZone(DateTime.now()),
        printDateTime: formatDateTimeWithTimeZone(DateTime.now()),
        ssMode: mode.id,
        userLoginId: int.tryParse(userId) ?? 0,
        routePlanVersionId: dataDescription?.routePlanVersionId,
        routePlanId: dataDescription?.routePlanId,
        // this we actually need for the labels and not the scanlog
        parkingPlanMasterVersionId: dataDescription?.parkingPlanVersionId,
        packageType: packageTypeId,
        provinceCode: terminal!.provinceCode);
  }

  @override
  Future<void> close() {
    soundService.dispose();
    barcodesScannedQueue.clear();
    return super.close();
  }

  @override
  LookupState? fromJson(Map<String, dynamic> json) {
    if (json.containsKey("scannedPins")) scannedPins = json["scannedPins"];

    if (json.containsKey("currentResult") && json.containsKey("pastResult") && json.containsKey("reportData")) {
      currentResult = json["currentResult"] != null ? LookupResult.fromJson(json["currentResult"]) : null;
      pastResult = json["pastResult"] != null ? LookupResult.fromJson(json["pastResult"]) : null;
      _reportData = json["reportData"] != null ? ReportData.fromJson(json["reportData"]) : ReportData();

      return LookupPrintResults(
        currentResult: currentResult,
        pastResult: pastResult,
        reportData: _reportData,
      );
    }

    return const WaitingForScan();
  }

  @override
  Map<String, dynamic>? toJson(LookupState state) {
    if (state is LookupPrintResults) {
      return {
        "scannedPins": scannedPins,
        "currentResult": state.currentResult?.toJson(),
        "pastResult": state.pastResult?.toJson(),
        "reportData": state.reportData.toJson(),
      };
    }
    return {};
  }

  @override
  void hydrate() {
    // only hydrate when showing results
    if (state is LookupPrintResults) {
      super.hydrate();
    }
  }

  // https://github.com/felangel/bloc/issues/2402
  // Caching multiple instances of a bloc
  // in this case because we use the same bloc in both Print & Both modes
  @override
  String get id => mode;
}
