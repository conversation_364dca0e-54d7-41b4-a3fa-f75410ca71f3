part of 'lookup_cubit.dart';

abstract class LookupState extends Equatable {
  const LookupState();
}

class LookingUpPrintResults extends LookupState {
  const LookingUpPrintResults() : super();

  @override
  List<Object> get props => [];
}

class WaitingForScan extends LookupState {
  const WaitingForScan() : super();

  @override
  List<Object> get props => [];
}

class ScanError extends LookupState {
  final String errorMessage;

  const ScanError({
    required this.errorMessage,
  }) : super();

  @override
  List<Object> get props => [errorMessage];
}

class LookupPrintResults extends LookupState {
  final LookupResult? currentResult;
  final LookupResult? pastResult;
  final ReportData reportData;
  final bool printLabel;
  final bool shouldRemediate;

  const LookupPrintResults({
    required this.currentResult,
    required this.pastResult,
    required this.reportData,
    this.printLabel = true,
    this.shouldRemediate = false,
  }) : super();

  @override
  List<Object?> get props => [
        currentResult,
        pastResult,
        reportData,
        printLabel,
        shouldRemediate,
      ];
}

class PinInvalidState extends LookupState {
  final LookupResult? currentResult;
  final LookupResult? pastResult;
  final ReportData reportData;

  const PinInvalidState({
    required this.currentResult,
    required this.pastResult,
    required this.reportData,
  }) : super();

  @override
  List<Object?> get props => [
        currentResult,
        pastResult,
        reportData,
      ];
}
