import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:sort_pro_printer_flutter/app/manage/cubit/user_setting_cubit.dart';
import 'package:sort_pro_printer_flutter/app/manage/data/models/supported_languages.dart';
import 'package:sort_pro_printer_flutter/app/peripherals/cubit/peripherals_cubit.dart';
import 'package:sort_pro_printer_flutter/app/pin_lookup/data/models/lookup_result.dart';
import 'package:sort_pro_printer_flutter/app/utils/conveyor_side_translation.dart';
import 'package:sort_pro_printer_flutter/styles/scale_size.dart';

class PrintLabel extends StatelessWidget {

  static const labelHeight = 96.0;

  const PrintLabel({
    super.key,
    required this.lookupResult,
    required this.isPastResult,
    this.canReprint = false,
  });

  final LookupResult lookupResult;
  final bool isPastResult;
  final bool canReprint;

  @override
  Widget build(BuildContext context) {
    final languageCode = context.read<UserSettingCubit>().state.languageCode ?? SupportedLanguages.english;
    final Color textColor = isPastResult ? const Color(0xBFffffff) : Colors.black;
    final Color borderColor = isPastResult ? Colors.white : Colors.black;
    final double labelFontSize = isPastResult ? 13 : 17;
    final double bodyFontSize = isPastResult ? 30 : 36;
    final double sequenceNumberFontSize = isPastResult ? 22 : 26;

    routingLabel() {
      return Container(
        decoration: BoxDecoration(color: isPastResult ? Colors.black : const Color(0xFFFFF799), border: Border.all(color: borderColor)),
        width: isPastResult ? MediaQuery.of(context).size.width * .65 : double.infinity,
        height: isPastResult ? labelHeight - 6 : labelHeight,
        child: Column(
          children: [
            Container(
              decoration: BoxDecoration(border: Border(bottom: BorderSide(color: borderColor))),
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 10.0),
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 10),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        DateFormat('dd/MM/yyyy HH:mm').format(lookupResult.date),
                        style: TextStyle(color: textColor, fontWeight: FontWeight.w600),
                      ),
                      Text(
                        lookupResult.pin.isEmpty ? "" : "PIN:${lookupResult.pin}",
                        style: TextStyle(fontWeight: FontWeight.w600, fontSize: labelFontSize, color: textColor),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            SizedBox(
              height: 45,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Flexible(
                    child: Container(
                        decoration: BoxDecoration(color: Colors.black, border: Border(right: BorderSide(color: borderColor))),
                        child: Center(
                            child: Text(
                          lookupResult.pudroNumber ?? "",
                          style: TextStyle(
                              fontSize: bodyFontSize, fontWeight: FontWeight.bold, color: isPastResult ? textColor : Colors.white),
                        ))),
                  ),
                  Flexible(
                    child: Container(
                        decoration: BoxDecoration(border: Border(right: BorderSide(color: borderColor))),
                        child: Center(
                            child: Text(translateConveyorSide(lookupResult.conveyorSide ?? "", languageCode),
                                style: TextStyle(fontSize: bodyFontSize, color: textColor, fontWeight: FontWeight.bold)))),
                  ),
                  Flexible(
                    flex: 2,
                    child: Container(
                        decoration: BoxDecoration(border: Border(right: BorderSide(color: borderColor))),
                        child: Center(
                            child: Text(lookupResult.routeNumber ?? "",
                                style: TextStyle(fontSize: bodyFontSize, color: textColor, fontWeight: FontWeight.bold)))),
                  ),
                  Flexible(
                    child: Container(
                        decoration: BoxDecoration(border: Border(right: BorderSide(color: borderColor))),
                        child: Center(
                            // display the truckShelfOverride is available
                            child: Text(
                                lookupResult.truckShelfOverride != null && lookupResult.truckShelfOverride != ""
                                    ? lookupResult.truckShelfOverride!
                                    : lookupResult.shelfNumber ?? "",
                                style: TextStyle(fontSize: bodyFontSize, color: textColor, fontWeight: FontWeight.bold)))),
                  ),
                  Flexible(
                    child: Center(
                        child: Column(
                      children: [
                        const Padding(padding: EdgeInsets.all(5)),
                        Text(lookupResult.sequenceNumber,
                            style: TextStyle(fontSize: sequenceNumberFontSize, color: textColor, fontWeight: FontWeight.bold)),
                      ],
                    )),
                  ),
                ],
              ),
            ),
            Container(
              decoration: BoxDecoration(border: Border(top: BorderSide(color: borderColor))),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Padding(
                    padding: const EdgeInsets.only(left: 20.0),
                    child: Text(
                      lookupResult.terminalNumber,
                      style: TextStyle(fontWeight: FontWeight.w600, fontSize: labelFontSize, color: textColor),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.only(right: 20.0),
                    child: Text(
                      lookupResult.routePlanId ?? "",
                      style: TextStyle(fontWeight: FontWeight.w600, fontSize: labelFontSize, color: textColor),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    }

    remediateLabel() {
      Map<LookupResultType, String> labelMap = {
        LookupResultType.srr: "SRR/SRE",
        LookupResultType.srrA: "SRR/SRE - A",
        LookupResultType.srrR: "SRR/SRE - R",
        LookupResultType.srrP: "SRR/SRE - P",
        LookupResultType.misdirect: "MISDIRECT/MALACHEMINE",
        LookupResultType.remediation: "REMEDIATION/REMANIEMENT",
      };

      return Container(
        decoration: BoxDecoration(color: isPastResult ? Colors.black : const Color(0xFFFFF799), border: Border.all(color: borderColor)),
        width: isPastResult ? MediaQuery.of(context).size.width * .65 : double.infinity,
        height: isPastResult ? labelHeight - 6 : labelHeight,
        child: Column(
          children: [
            Container(
              decoration: BoxDecoration(border: Border(bottom: BorderSide(color: borderColor))),
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 10.0),
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 10),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        DateFormat('dd/MM/yyyy HH:mm').format(lookupResult.date),
                        style: TextStyle(color: textColor, fontWeight: FontWeight.w600),
                      ),
                      Text(
                        lookupResult.pin.isEmpty ? "" : "PIN:${lookupResult.pin}",
                        style: TextStyle(fontWeight: FontWeight.w600, fontSize: labelFontSize, color: textColor),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            SizedBox(
              height: 45,
              child: Center(
                child: Text(
                  labelMap[lookupResult.lookupResultType] ?? "",
                  style: Theme.of(context).textTheme.titleLarge!.copyWith(color: textColor, fontWeight: FontWeight.bold),
                  textScaler: TextScaler.linear(ScaleUtil.textScaleFactor(context)),
                ),
              ),
            ),
            Container(
              decoration: BoxDecoration(border: Border(top: BorderSide(color: borderColor))),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Padding(
                    padding: const EdgeInsets.only(left: 20.0),
                    child: Text(
                      lookupResult.terminalNumber,
                      style: TextStyle(fontWeight: FontWeight.w600, fontSize: labelFontSize, color: textColor),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.only(right: 20.0),
                    child: Text(
                      lookupResult.routePlanId ?? "",
                      style: TextStyle(fontWeight: FontWeight.w600, fontSize: labelFontSize, color: textColor),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    }

    return GestureDetector(
      onLongPress: canReprint ? () => context.read<PeripheralsCubit>().printLookupLabel(lookupResult, languageCode) : null,
      child: lookupResult.lookupResultType == LookupResultType.routing || lookupResult.lookupResultType == LookupResultType.crossDock
          ? routingLabel()
          : remediateLabel(),
    );
  }
}
