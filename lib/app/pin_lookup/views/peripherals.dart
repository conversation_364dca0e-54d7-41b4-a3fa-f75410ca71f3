import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sort_pro_printer_flutter/app/peripherals/cubit/peripherals_cubit.dart';
import 'package:sort_pro_printer_flutter/app/peripherals/peripheral_status.dart';
import 'package:sort_pro_printer_flutter/app/peripherals/views/connect_printer_page.dart';
import 'package:sort_pro_printer_flutter/app/peripherals/views/connect_ring_scanner_page.dart';
import 'package:sort_pro_printer_flutter/app/peripherals/views/peripheral_button.dart';
import 'package:sort_pro_printer_flutter/services/printer/printer_info.dart';
import 'package:sort_pro_printer_flutter/services/ring_scanner/i_ringer_service.dart';

class Peripherals extends StatelessWidget {
  const Peripherals({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        Builder(builder: (context) {
          final printerInfo = context.select<PeripheralsCubit, PrinterInfo>((cubit) => cubit.state.printerInfo);
          final printerPaired = context.select<PeripheralsCubit, bool>((cubit) => cubit.state.printerPaired);
          PeripheralStatus peripheralStatus = PeripheralStatus.disconnected;
          // if the printer is paired, show it at least unavailable
          if (printerPaired) {
            peripheralStatus = PeripheralStatus.unavailable;
          }
          if (printerInfo.isConnected) {
            peripheralStatus = PeripheralStatus.connected;
          }
          return PeripheralButton(
            peripheralStatus: peripheralStatus,
            icon: Icons.print,
            // onPressed: () => Navigator.push(context, ConnectPrinterPage.route(context)),
          );
        }),
        const SizedBox(
          width: 14.0,
        ),
        Builder(builder: (context) {
          final ringScannerStatus = context.select<PeripheralsCubit, RingScannerStatus>((cubit) => cubit.state.ringScannerStatus);
          return PeripheralButton(
            peripheralStatus: ringScannerStatus == RingScannerStatus.connected ? PeripheralStatus.connected : PeripheralStatus.disconnected,
            icon: Icons.qr_code_scanner,
            // onPressed: () => Navigator.push(context, ConnectRingerScannerPage.route(context)),
          );
        }),
      ],
    );
  }
}
