/// This is temporarily removed as there's no more space in it on the screen
// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:flutter_gen/gen_l10n/app_localizations.dart';
// import 'package:sort_pro_printer_flutter/app/manage/cubit/user_setting_cubit.dart';
// import 'package:sort_pro_printer_flutter/app/pin_lookup/views/pin_lookup.dart';
// import 'package:sort_pro_printer_flutter/app/remediate/views/remediate_page.dart';
//
// class ModeSelectModal extends StatefulWidget {
//   const ModeSelectModal({super.key});
//
//   @override
//   State<ModeSelectModal> createState() => _ModeSelectModalState();
// }
//
// class _ModeSelectModalState extends State<ModeSelectModal> {
//   @override
//   Widget build(BuildContext context) {
//     var currentMode = context.select<UserSettingCubit, String?>((value) => value.state.mode);
//     return ElevatedButton(
//       style: ElevatedButton.styleFrom(
//         backgroundColor: const Color(0xff646464),
//         foregroundColor: Colors.white,
//         minimumSize: const Size(double.infinity, 45),
//         shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(30.0)),
//       ),
//       child: Row(
//         children: [
//           Text(currentMode == "print"
//               ? AppLocalizations.of(context)!.print
//               : currentMode == "remediate"
//                   ? AppLocalizations.of(context)!.remediate
//                   : AppLocalizations.of(context)!.printRemediate),
//           const Spacer(),
//           const Icon(Icons.expand_more),
//         ],
//       ),
//       onPressed: () {
//         showModalBottomSheet<void>(
//           shape: const RoundedRectangleBorder(borderRadius: BorderRadius.only(topLeft: Radius.circular(20), topRight: Radius.circular(20))),
//           backgroundColor: Colors.black,
//           context: context,
//           builder: (BuildContext context) {
//             return Column(
//               mainAxisSize: MainAxisSize.min,
//               children: <Widget>[
//                 AppBar(
//                   automaticallyImplyLeading: false,
//                   centerTitle: true,
//                   actions: [
//                     TextButton(
//                       onPressed: () {
//                         Navigator.pop(context);
//                       },
//                       child: const Icon(Icons.close, color: Colors.white),
//                     ),
//                   ],
//                   title: Text(AppLocalizations.of(context)!.pinLookup_modal_scanMode),
//                 ),
//                 Padding(
//                   padding: const EdgeInsets.all(10),
//                   child: Column(
//                     children: [
//                       _buildModeOption("print", selected: currentMode == "print"),
//                       const Padding(padding: EdgeInsets.only(bottom: 10)),
//                       _buildModeOption("remediate", selected: currentMode == "remediate"),
//                       const Padding(padding: EdgeInsets.only(bottom: 10)),
//                       _buildModeOption("printRemediate", selected: currentMode == "printRemediate"),
//                       const SizedBox(
//                         height: 32,
//                       ),
//                     ],
//                   ),
//                 ),
//               ],
//             );
//           },
//         );
//       },
//     );
//   }
//
//   _buildModeOption(String mode, {bool selected = false}) {
//     return ElevatedButton(
//       onPressed: () {
//         _onModeSelected(mode);
//       },
//       style: ElevatedButton.styleFrom(
//           backgroundColor: const Color(0xff3F3F3F), foregroundColor: Colors.white, side: const BorderSide(color: Color(0xff646464))),
//       child: Row(
//         children: [Text(_getModeLabel(mode)), const Spacer(), Icon(selected ? Icons.radio_button_checked : Icons.radio_button_unchecked)],
//       ),
//     );
//   }
//
//   String _getModeLabel(String mode) {
//     switch (mode) {
//       case "print":
//         return AppLocalizations.of(context)!.print;
//       case "printRemediate":
//         return AppLocalizations.of(context)!.printRemediate;
//       case "remediate":
//       default:
//         return AppLocalizations.of(context)!.remediate;
//     }
//   }
//
//   _onModeSelected(String mode) async {
//     final currentMode = context.read<UserSettingCubit>().state.mode;
//     if (currentMode == mode) {
//       Navigator.pop(context); // just stay in the same mode
//       return;
//     }
//
//     final confirmed = await showModalBottomSheet<bool>(
//             context: context,
//             builder: (context) {
//               return _ConfirmModeSelect(modeLabel: _getModeLabel(mode));
//             }) ??
//         false;
//
//     if (confirmed) {
//       _changeMode(mode);
//     }
//   }
//
//   _changeMode(String mode) {
//     context.read<UserSettingCubit>().changeMode(mode);
//
//     switch (mode) {
//       case "print":
//       case "printRemediate":
//         Navigator.of(context).popUntil((route) => route.isFirst);
//         Navigator.push(
//           context,
//           PinLookupPage.route(context),
//         );
//         break;
//       case "remediate":
//         Navigator.of(context).popUntil((route) => route.isFirst);
//         Navigator.push(
//           context,
//           RemediatePage.route(context),
//         );
//         break;
//     }
//   }
// }
//
// class _ConfirmModeSelect extends StatelessWidget {
//   final String modeLabel;
//
//   const _ConfirmModeSelect({Key? key, required this.modeLabel}) : super(key: key);
//
//   @override
//   Widget build(BuildContext context) {
//     return Container(
//       height: 200,
//       color: Colors.black,
//       child: Column(
//         children: [
//           Padding(
//             padding: const EdgeInsets.all(16.0),
//             child: Stack(
//               children: [
//                 Row(
//                   children: [
//                     Expanded(
//                       child: Text(
//                         AppLocalizations.of(context)!.confirmModeChange_lbl_title(modeLabel),
//                         textAlign: TextAlign.center,
//                         style: Theme.of(context).textTheme.titleMedium,
//                       ),
//                     ),
//                   ],
//                 ),
//                 Align(
//                     alignment: Alignment.topRight,
//                     child: GestureDetector(onTap: () => Navigator.pop(context), child: const Icon(Icons.close)))
//               ],
//             ),
//           ),
//           const Divider(
//             color: Color(0xFF646464),
//             height: 0,
//           ),
//           Expanded(
//             child: Padding(
//               padding: const EdgeInsets.all(8.0),
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                 mainAxisSize: MainAxisSize.min,
//                 children: <Widget>[
//                   Text(
//                     AppLocalizations.of(context)!.confirmModeChange_lbl_message(modeLabel),
//                     style: Theme.of(context).textTheme.titleMedium,
//                   ),
//                   OutlinedButton(
//                     child: Text(AppLocalizations.of(context)!.remediate_btn_confirm),
//                     onPressed: () => Navigator.pop(context, true),
//                   ),
//                 ],
//               ),
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// }
