import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:sort_pro_printer_flutter/app/peripherals/cubit/peripherals_cubit.dart';
import 'package:sort_pro_printer_flutter/app/pin_lookup/cubit/lookup_cubit.dart';
import 'package:sort_pro_printer_flutter/app/pin_lookup/data/models/lookup_result.dart';
import 'package:sort_pro_printer_flutter/app/scanner/data/models/barcode.dart';
import 'package:sort_pro_printer_flutter/services/printer/printer_info.dart';

/// Manually print a label
/// Currently only accepts REM labels.
class _PrintLabelButton extends StatelessWidget {
  final LookupResultType labelType;

  const _PrintLabelButton({Key? key, required this.labelType})
      : assert(labelType == LookupResultType.remediation),
        super(key: key);

  @override
  Widget build(BuildContext context) {
    final printerInfo = context.select<PeripheralsCubit, PrinterInfo>((cubit) => cubit.state.printerInfo);
    final printerReady = printerInfo.isConnected && printerInfo.isReadyToPrint;
    return ElevatedButton(
      onPressed: printerReady
          ? () {
              switch (labelType) {
                case LookupResultType.routing:
                case LookupResultType.crossDock:
                case LookupResultType.srr:
                case LookupResultType.srrA:
                case LookupResultType.srrR:
                case LookupResultType.srrP:
                case LookupResultType.misdirect:
                  break;
                case LookupResultType.remediation:
                  context.read<LookupCubit>().lookupPackageInfoFromBarcode(Barcode.printRemLabelBarcode);
              }
            }
          : null,
      child: Text(
        AppLocalizations.of(context)!.print_btn_remLabel,
        textAlign: TextAlign.center,
      ),
    );
  }
}

class PrintRemLabelButton extends StatelessWidget {
  const PrintRemLabelButton({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return const _PrintLabelButton(
      labelType: LookupResultType.remediation,
    );
  }
}
