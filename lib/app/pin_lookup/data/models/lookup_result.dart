import 'package:intl/intl.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:sort_pro_printer_flutter/app/manage/data/models/package_type.dart';
import 'package:sort_pro_printer_flutter/app/utils/conveyor_side_translation.dart';
import 'package:sort_pro_printer_flutter/extensions/string_nullable.dart';

part 'lookup_result.g.dart';

enum LookupResultType {
  routing(ssReasonKey: "RTE"),
  crossDock(ssReasonKey: "CD"),
  srr(ssReasonKey: "SRR"),
  srrA(ssReasonKey: "SRR-A"),
  srrR(ssReasonKey: "SRR-R"),
  srrP(ssReasonKey: "SRR-P"),
  misdirect(ssReasonKey: "MSD"),
  remediation(ssReasonKey: "REM");

  const LookupResultType({
    required this.ssReasonKey,
  });

  final String ssReasonKey;
}

enum LookupResolvedBy {
  none(id: 0),
  pinFile(id: 1),
  prePrintFile(id: 2),
  webService(id: 3),
  localRemediation(id: 4),
  barcodeParsing(id: 5);

  const LookupResolvedBy({
    required this.id,
  });

  final int id;
}

@JsonSerializable()
class LookupResult {
  final List<String> _handlingDg = [
    "A1",
    "A2",
    "A3",
    "A4",
    "A5",
    "B1",
    "B2",
    "B3",
    "B4",
    "B5",
    "C1",
    "C2",
    "C3",
    "C4",
    "C5",
    "D1",
    "D2",
    "D3",
    "D4",
    "D5",
    "E1",
    "E2",
    "E3",
    "E4",
    "E5",
  ];

  final LookupResultType lookupResultType;

  /// Info that goes into the label (And the Scan Log)
  final String pin;
  final String terminalNumber;
  final DateTime date;

  final String? _sequenceNumber;
  final String? _truckShelfOverride;

  bool get isRem =>
      lookupResultType == LookupResultType.remediation ||
      lookupResultType == LookupResultType.srr ||
      lookupResultType == LookupResultType.srrA ||
      lookupResultType == LookupResultType.srrR ||
      lookupResultType == LookupResultType.srrP;

  String get sequenceNumber {
    String id = _sequenceNumber.isNullOrEmpty() ? "" : _sequenceNumber!;
    if (id.isNotEmpty) {
      if (id.length == 1) {
        return "00$id";
      } else if (id.length == 2) {
        return "0$id";
      }
    }
    return id;
  }

  String? get truckShelfOverride {
    if (diversionCode == "1" || _handlingDg.contains(handlingClassType)) {
      return "DG";
    } else if (!deliveryTime.isNullOrEmpty() && deliveryTime != "00") {
      return "PR";
    }
    return _truckShelfOverride;
  }

  final String? routeNumber;
  final String? shelfNumber;
  final String? pudroNumber;
  final String? conveyorSide;
  final String? routePlanId;

  // confidence level in remediate results
  final int? weight;

  // number of addresses for the same route/shelf/street (depends on query)
  final int? duplicateCount;

  /// Extra info required by the ScanLog Events
  final LookupResolvedBy lookupResolvedBy;
  final String? ssDeviceId;
  final int? userLoginId;
  final String? parkingPlanMasterVersionId;
  final String? routePlanVersionId;
  final String? prePrintId;
  final String? ssStatusReason;
  final String? ssMode;
  final String? scanDateTime;
  final String? postalCode;
  final String? provinceCode;
  final String? municipalityName;
  final String? fromStreetNumber;
  final String? toStreetNumber;
  final String? streetNumberSuffix;
  final String? streetName;
  final String? streetType;
  final String? streetDir;
  final String? unitNumber;
  final String? customerName;
  final String? printDateTime;
  final String? deliveryTime;
  final String? shipmentType;
  final String? deliveryType;
  final String? diversionCode;
  final int? packageType;
  final String? handlingClassType;
  final String? barcodeType;
  final String? alternateAddressFlag;

  LookupResult({
    required this.lookupResultType,
    required this.pin,
    required this.terminalNumber,
    DateTime? date,
    String? sequenceNumber,
    this.routeNumber,
    this.shelfNumber,
    this.pudroNumber,
    this.conveyorSide,
    this.routePlanId,
    this.weight,
    this.duplicateCount,

    /// Extra info required by the Scan Log Events
    required this.lookupResolvedBy,
    this.ssDeviceId,
    this.userLoginId,
    this.parkingPlanMasterVersionId,
    this.routePlanVersionId,
    this.prePrintId,
    this.ssStatusReason,
    String? truckShelfOverride,
    this.ssMode,
    this.postalCode,
    this.provinceCode,
    this.municipalityName,
    this.fromStreetNumber,
    this.toStreetNumber,
    this.streetNumberSuffix,
    this.streetName,
    this.streetType,
    this.streetDir,
    this.unitNumber,
    this.customerName,
    this.printDateTime,
    this.deliveryTime,
    this.shipmentType,
    this.deliveryType,
    this.diversionCode,
    int? packageType,
    this.handlingClassType,
    this.barcodeType,
    this.alternateAddressFlag,
    this.scanDateTime,
  })  : _sequenceNumber = sequenceNumber,
        _truckShelfOverride = truckShelfOverride,
        date = date ?? DateTime.now(),
        packageType = packageType ?? PackageType.box.id;

  LookupResult copyWith({
    LookupResultType? lookupResultType,
    String? pin,
    String? terminalNumber,
    DateTime? date,
    String? sequenceNumber,
    String? routeNumber,
    String? shelfNumber,
    String? pudroNumber,
    String? conveyorSide,
    String? routePlanId,
    LookupResolvedBy? lookupResolvedBy,
    String? ssDeviceId,
    int? userLoginId,
    String? parkingPlanMasterVersionId,
    String? routePlanVersionId,
    String? prePrintId,
    String? ssStatusReason,
    String? truckShelfOverride,
    String? ssMode,
    String? scanDateTime,
    String? postalCode,
    String? provinceCode,
    String? municipalityName,
    String? fromStreetNumber,
    String? toStreetNumber,
    String? streetNumberSuffix,
    String? streetName,
    String? streetType,
    String? streetDir,
    String? unitNumber,
    String? customerName,
    String? printDateTime,
    String? deliveryTime,
    String? shipmentType,
    String? deliveryType,
    String? diversionCode,
    int? packageType,
    String? handlingClassType,
    String? barcodeType,
    String? resolvedBy,
    String? alternateAddressFlag,
    int? retryCount,
    String? upSyncErrorCode,
    int? weight,
    int? duplicateCount,
  }) {
    return LookupResult(
      date: date ?? this.date,
      lookupResultType: lookupResultType ?? this.lookupResultType,
      pin: pin ?? this.pin,
      terminalNumber: terminalNumber ?? this.terminalNumber,
      sequenceNumber: sequenceNumber ?? _sequenceNumber,
      routeNumber: routeNumber ?? this.routeNumber,
      shelfNumber: shelfNumber ?? this.shelfNumber,
      pudroNumber: pudroNumber ?? this.pudroNumber,
      conveyorSide: conveyorSide ?? this.conveyorSide,
      routePlanId: routePlanId ?? this.routePlanId,
      lookupResolvedBy: lookupResolvedBy ?? this.lookupResolvedBy,
      ssDeviceId: ssDeviceId ?? this.ssDeviceId,
      userLoginId: userLoginId ?? this.userLoginId,
      parkingPlanMasterVersionId: parkingPlanMasterVersionId ?? this.parkingPlanMasterVersionId,
      routePlanVersionId: routePlanVersionId ?? this.routePlanVersionId,
      prePrintId: prePrintId ?? this.prePrintId,
      ssStatusReason: ssStatusReason ?? this.ssStatusReason,
      truckShelfOverride: truckShelfOverride ?? this.truckShelfOverride,
      ssMode: ssMode ?? this.ssMode,
      scanDateTime: scanDateTime ?? this.scanDateTime,
      postalCode: postalCode ?? this.postalCode,
      provinceCode: provinceCode ?? this.provinceCode,
      municipalityName: municipalityName ?? this.municipalityName,
      fromStreetNumber: fromStreetNumber ?? this.fromStreetNumber,
      toStreetNumber: toStreetNumber ?? this.toStreetNumber,
      streetNumberSuffix: streetNumberSuffix ?? this.streetNumberSuffix,
      streetName: streetName ?? this.streetName,
      streetType: streetType ?? this.streetType,
      streetDir: streetDir ?? this.streetDir,
      unitNumber: unitNumber ?? this.unitNumber,
      customerName: customerName ?? this.customerName,
      printDateTime: printDateTime ?? this.printDateTime,
      deliveryTime: deliveryTime ?? this.deliveryTime,
      shipmentType: shipmentType ?? this.shipmentType,
      deliveryType: deliveryType ?? this.deliveryType,
      diversionCode: diversionCode ?? this.diversionCode,
      packageType: packageType ?? this.packageType,
      handlingClassType: handlingClassType ?? this.handlingClassType,
      barcodeType: barcodeType ?? this.barcodeType,
      alternateAddressFlag: alternateAddressFlag ?? this.alternateAddressFlag,
      weight: weight ?? this.weight,
      duplicateCount: duplicateCount ?? this.duplicateCount,
    );
  }

  factory LookupResult.empty() =>
      LookupResult(lookupResultType: LookupResultType.remediation, pin: "", terminalNumber: "", lookupResolvedBy: LookupResolvedBy.none);

  factory LookupResult.emptyRemResult({required String terminalNumber, required String routePlanId}) => LookupResult(
        lookupResultType: LookupResultType.remediation,
        pin: "",
        terminalNumber: terminalNumber,
        date: DateTime.now(),
        lookupResolvedBy: LookupResolvedBy.none,
        routePlanId: routePlanId,
      );

  toLabelInfo(String languageCode) => {
        "lookupResultType": lookupResultType.name,
        "pin": pin,
        "terminalNumber": terminalNumber,
        "date": DateFormat('dd/MM/yyyy HH:mm').format(date),
        "routeNumber": routeNumber ?? "",
        "shelfNumber": truckShelfOverride != null && truckShelfOverride != "" ? truckShelfOverride : shelfNumber ?? "",
        "pudroNumber": pudroNumber ?? "",
        "conveyorSide": translateConveyorSide(conveyorSide ?? "", languageCode),
        "routePlanId": routePlanId ?? "",
        "sequenceNumber": sequenceNumber.isNullOrEmpty() ? "" : sequenceNumber,
      };

  factory LookupResult.fromJson(Map<String, dynamic> json) => _$LookupResultFromJson(json);

  Map<String, dynamic> toJson() => _$LookupResultToJson(this);
}
