// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'lookup_result.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LookupResult _$LookupResultFromJson(Map<String, dynamic> json) => LookupResult(
      lookupResultType:
          $enumDecode(_$LookupResultTypeEnumMap, json['lookupResultType']),
      pin: json['pin'] as String,
      terminalNumber: json['terminalNumber'] as String,
      date:
          json['date'] == null ? null : DateTime.parse(json['date'] as String),
      sequenceNumber: json['sequenceNumber'] as String?,
      routeNumber: json['routeNumber'] as String?,
      shelfNumber: json['shelfNumber'] as String?,
      pudroNumber: json['pudroNumber'] as String?,
      conveyorSide: json['conveyorSide'] as String?,
      routePlanId: json['routePlanId'] as String?,
      weight: json['weight'] as int?,
      duplicateCount: json['duplicateCount'] as int?,
      lookupResolvedBy:
          $enumDecode(_$LookupResolvedByEnumMap, json['lookupResolvedBy']),
      ssDeviceId: json['ssDeviceId'] as String?,
      userLoginId: json['userLoginId'] as int?,
      parkingPlanMasterVersionId: json['parkingPlanMasterVersionId'] as String?,
      routePlanVersionId: json['routePlanVersionId'] as String?,
      prePrintId: json['prePrintId'] as String?,
      ssStatusReason: json['ssStatusReason'] as String?,
      truckShelfOverride: json['truckShelfOverride'] as String?,
      ssMode: json['ssMode'] as String?,
      postalCode: json['postalCode'] as String?,
      provinceCode: json['provinceCode'] as String?,
      municipalityName: json['municipalityName'] as String?,
      fromStreetNumber: json['fromStreetNumber'] as String?,
      toStreetNumber: json['toStreetNumber'] as String?,
      streetNumberSuffix: json['streetNumberSuffix'] as String?,
      streetName: json['streetName'] as String?,
      streetType: json['streetType'] as String?,
      streetDir: json['streetDir'] as String?,
      unitNumber: json['unitNumber'] as String?,
      customerName: json['customerName'] as String?,
      printDateTime: json['printDateTime'] as String?,
      deliveryTime: json['deliveryTime'] as String?,
      shipmentType: json['shipmentType'] as String?,
      deliveryType: json['deliveryType'] as String?,
      diversionCode: json['diversionCode'] as String?,
      packageType: json['packageType'] as int?,
      handlingClassType: json['handlingClassType'] as String?,
      barcodeType: json['barcodeType'] as String?,
      alternateAddressFlag: json['alternateAddressFlag'] as String?,
      scanDateTime: json['scanDateTime'] as String?,
    );

Map<String, dynamic> _$LookupResultToJson(LookupResult instance) =>
    <String, dynamic>{
      'lookupResultType': _$LookupResultTypeEnumMap[instance.lookupResultType]!,
      'pin': instance.pin,
      'terminalNumber': instance.terminalNumber,
      'date': instance.date.toIso8601String(),
      'sequenceNumber': instance.sequenceNumber,
      'truckShelfOverride': instance.truckShelfOverride,
      'routeNumber': instance.routeNumber,
      'shelfNumber': instance.shelfNumber,
      'pudroNumber': instance.pudroNumber,
      'conveyorSide': instance.conveyorSide,
      'routePlanId': instance.routePlanId,
      'weight': instance.weight,
      'duplicateCount': instance.duplicateCount,
      'lookupResolvedBy': _$LookupResolvedByEnumMap[instance.lookupResolvedBy]!,
      'ssDeviceId': instance.ssDeviceId,
      'userLoginId': instance.userLoginId,
      'parkingPlanMasterVersionId': instance.parkingPlanMasterVersionId,
      'routePlanVersionId': instance.routePlanVersionId,
      'prePrintId': instance.prePrintId,
      'ssStatusReason': instance.ssStatusReason,
      'ssMode': instance.ssMode,
      'scanDateTime': instance.scanDateTime,
      'postalCode': instance.postalCode,
      'provinceCode': instance.provinceCode,
      'municipalityName': instance.municipalityName,
      'fromStreetNumber': instance.fromStreetNumber,
      'toStreetNumber': instance.toStreetNumber,
      'streetNumberSuffix': instance.streetNumberSuffix,
      'streetName': instance.streetName,
      'streetType': instance.streetType,
      'streetDir': instance.streetDir,
      'unitNumber': instance.unitNumber,
      'customerName': instance.customerName,
      'printDateTime': instance.printDateTime,
      'deliveryTime': instance.deliveryTime,
      'shipmentType': instance.shipmentType,
      'deliveryType': instance.deliveryType,
      'diversionCode': instance.diversionCode,
      'packageType': instance.packageType,
      'handlingClassType': instance.handlingClassType,
      'barcodeType': instance.barcodeType,
      'alternateAddressFlag': instance.alternateAddressFlag,
    };

const _$LookupResultTypeEnumMap = {
  LookupResultType.routing: 'routing',
  LookupResultType.crossDock: 'crossDock',
  LookupResultType.srr: 'srr',
  LookupResultType.srrA: 'srrA',
  LookupResultType.srrR: 'srrR',
  LookupResultType.srrP: 'srrP',
  LookupResultType.misdirect: 'misdirect',
  LookupResultType.remediation: 'remediation',
};

const _$LookupResolvedByEnumMap = {
  LookupResolvedBy.none: 'none',
  LookupResolvedBy.pinFile: 'pinFile',
  LookupResolvedBy.prePrintFile: 'prePrintFile',
  LookupResolvedBy.webService: 'webService',
  LookupResolvedBy.localRemediation: 'localRemediation',
  LookupResolvedBy.barcodeParsing: 'barcodeParsing',
};
