import 'package:json_annotation/json_annotation.dart';

part 'report_data.g.dart';

@JsonSerializable()
class ReportData {
  final int parcelsScanned;
  final int invalidScans;
  final int manualRemLabels;
  final int rteLabels;
  final int remLabels;
  final int srrLabels;
  final int misdirectLabels;
  final int crossdockLabels;

  int get other => manualRemLabels + misdirectLabels + crossdockLabels + invalidScans;

  ReportData({
    this.parcelsScanned = 0,
    this.invalidScans = 0,
    this.manualRemLabels = 0,
    this.rteLabels = 0,
    this.remLabels = 0,
    this.srrLabels = 0,
    this.misdirectLabels = 0,
    this.crossdockLabels = 0,
  });

  ReportData copyWith({
    int? parcelsScanned,
    int? invalidScans,
    int? manualRemLabels,
    int? rteLabels,
    int? remLabels,
    int? srrLabels,
    int? misdirectLabels,
    int? crossdockLabels,
  }) =>
      ReportData(
        parcelsScanned: parcelsScanned ?? this.parcelsScanned,
        invalidScans: invalidScans ?? this.invalidScans,
        manualRemLabels: manualRemLabels ?? this.manualRemLabels,
        rteLabels: rteLabels ?? this.rteLabels,
        remLabels: remLabels ?? this.remLabels,
        srrLabels: srrLabels ?? this.srrLabels,
        misdirectLabels: misdirectLabels ?? this.misdirectLabels,
        crossdockLabels: crossdockLabels ?? this.crossdockLabels,
      );

  factory ReportData.fromJson(Map<String, dynamic> json) => _$ReportDataFromJson(json);

  Map<String, dynamic> toJson() => _$ReportDataToJson(this);

}
