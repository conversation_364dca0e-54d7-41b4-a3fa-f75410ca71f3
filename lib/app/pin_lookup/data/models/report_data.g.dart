// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'report_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ReportData _$ReportDataFromJson(Map<String, dynamic> json) => ReportData(
      parcelsScanned: json['parcelsScanned'] as int? ?? 0,
      invalidScans: json['invalidScans'] as int? ?? 0,
      manualRemLabels: json['manualRemLabels'] as int? ?? 0,
      rteLabels: json['rteLabels'] as int? ?? 0,
      remLabels: json['remLabels'] as int? ?? 0,
      srrLabels: json['srrLabels'] as int? ?? 0,
      misdirectLabels: json['misdirectLabels'] as int? ?? 0,
      crossdockLabels: json['crossdockLabels'] as int? ?? 0,
    );

Map<String, dynamic> _$ReportDataToJson(ReportData instance) =>
    <String, dynamic>{
      'parcelsScanned': instance.parcelsScanned,
      'invalidScans': instance.invalidScans,
      'manualRemLabels': instance.manualRemLabels,
      'rteLabels': instance.rteLabels,
      'remLabels': instance.remLabels,
      'srrLabels': instance.srrLabels,
      'misdirectLabels': instance.misdirectLabels,
      'crossdockLabels': instance.crossdockLabels,
    };
