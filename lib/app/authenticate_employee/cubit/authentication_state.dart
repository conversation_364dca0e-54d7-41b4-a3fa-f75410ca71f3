part of 'authentication_cubit.dart';

enum AuthenticationStatus { unknown, authenticated, unauthenticated }

class AuthenticationState extends Equatable {
  const AuthenticationState._({
    this.status = AuthenticationStatus.unknown,
    this.user = User.empty,
    this.authenticatedAt,
  });

  const AuthenticationState.unknown() : this._();

  const AuthenticationState.authenticated(User user, DateTime authenticatedAt)
      : this._(status: AuthenticationStatus.authenticated, user: user, authenticatedAt: authenticatedAt);

  const AuthenticationState.unauthenticated() : this._(status: AuthenticationStatus.unauthenticated);

  final AuthenticationStatus status;
  final User user;
  final DateTime? authenticatedAt;

  @override
  List<Object?> get props => [status, user, authenticatedAt];
}
