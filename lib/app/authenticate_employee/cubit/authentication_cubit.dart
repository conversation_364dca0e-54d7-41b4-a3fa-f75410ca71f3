import 'package:equatable/equatable.dart';
import 'package:hydrated_bloc/hydrated_bloc.dart';
import 'package:sort_pro_printer_flutter/app/authenticate_employee/models/user.dart';
import 'package:sort_pro_printer_flutter/environment/environment_config.dart';
import 'package:sort_pro_printer_flutter/services/logs/i_log_service.dart';

part 'authentication_state.dart';

class AuthenticationCubit extends Cubit<AuthenticationState> with HydratedMixin {
  final ILogService _logService;

  AuthenticationCubit(
    this._logService,
  ) : super(const AuthenticationState.unknown()) {
    hydrate();
  }

  bool get authenticated => state.status == AuthenticationStatus.authenticated;

  authenticate(String employeeId) async {
    _logService.setUser(employeeId);
    _logService.event("User Logged In");
    emit(AuthenticationState.authenticated(User(employeeId), DateTime.now()));
  }

  validateUserSession() {
    if (state.status != AuthenticationStatus.authenticated || state.authenticatedAt == null || state.user.id == User.empty.id) {
      emit(const AuthenticationState.unauthenticated());
      return;
    }

    final now = DateTime.now();
    final authenticatedAt = state.authenticatedAt!;

    // log the user out if the session is expired
    if (now.difference(authenticatedAt).inSeconds >= EnvironmentConfig.sessionExpireTimeInSeconds) {
      _logService.event("User Session expired.");
      logout();
    } else {
      // otherwise refresh their session
      refreshUserSession();
    }
  }

  refreshUserSession() {
    if (state.status != AuthenticationStatus.authenticated) return;

    // refresh the user session
    emit(AuthenticationState.authenticated(state.user, DateTime.now()));
    _logService.event("User Session refreshed.");
  }

  logout() async {
    await HydratedBloc.storage.clear();
    _logService.event("User Logged Out");
    emit(const AuthenticationState.unauthenticated());
  }

  @override
  AuthenticationState? fromJson(Map<String, dynamic> json) {
    if (json.containsKey("lastUserLoginId") && json["lastUserLoginId"] != User.empty.id && json.containsKey("authenticatedAtInMillis")) {
      String userId = json["lastUserLoginId"];
      int authenticatedAtInMillis = json["authenticatedAtInMillis"];
      return AuthenticationState.authenticated(User(userId), DateTime.fromMillisecondsSinceEpoch(authenticatedAtInMillis));
    }
    return const AuthenticationState.unauthenticated();
  }

  @override
  Map<String, dynamic>? toJson(AuthenticationState state) {
    return {
      "lastUserLoginId": state.user.id,
      "authenticatedAtInMillis": state.authenticatedAt?.millisecondsSinceEpoch,
    };
  }
}
