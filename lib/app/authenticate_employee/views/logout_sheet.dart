import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sort_pro_printer_flutter/app/authenticate_employee/cubit/authentication_cubit.dart';
import 'package:sort_pro_printer_flutter/app/utils/timer_mixin.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:sort_pro_printer_flutter/services/logs/i_log_service.dart';

class LogoutSheet extends StatefulWidget {
  static const sheetTimeout = 30;

  const LogoutSheet({super.key});

  @override
  State<LogoutSheet> createState() => _LogoutSheetState();
}

class _LogoutSheetState extends State<LogoutSheet> with TimerMixin {
  int countdown = LogoutSheet.sheetTimeout;

  @override
  void initState() {
    // the duration in the periodic timer is how often it ticks.
    startPeriodicTimer(const Duration(seconds: 1), (timer) {
      setState(() {
        countdown--;
      });
      if (countdown == 0) {
        cancelTimer();
        context.read<ILogService>().event("User Logged out due to inactivity");
        context.read<AuthenticationCubit>().logout();
      }
    });
    super.initState();
  }

  @override
  void dispose() {
    cancelTimer();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 200,
      color: Colors.black,
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Stack(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        AppLocalizations.of(context)!.sessionExpirySheet_lbl_title,
                        textAlign: TextAlign.center,
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                    ),
                  ],
                ),
                Align(
                    alignment: Alignment.topRight,
                    child: GestureDetector(onTap: () => Navigator.pop(context), child: const Icon(Icons.close)))
              ],
            ),
          ),
          const Divider(
            color: Color(0xFF646464),
            height: 0,
          ),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  SizedBox(
                    width: MediaQuery.of(context).size.width * .7,
                    child: RichText(
                        textAlign: TextAlign.center,
                        text: TextSpan(
                            style: Theme.of(context).textTheme.bodyMedium,
                            text: "${AppLocalizations.of(context)!.sessionExpirySheet_lbl_message} ",
                            children: [
                              TextSpan(
                                  text: AppLocalizations.of(context)!.sessionExpirySheet_lbl_countdown(countdown),
                                  style: const TextStyle(fontWeight: FontWeight.bold)),
                            ])),
                  ),
                  Row(children: [
                    Flexible(
                      child: ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xff3F3F3F),
                          foregroundColor: Colors.white,
                        ),
                        onPressed: () {
                          Navigator.pop(context);
                          context.read<AuthenticationCubit>().logout();
                        },
                        child: Text(AppLocalizations.of(context)!.sessionExpirySheet_lbl_logoutConfirm),
                      ),
                    ),
                    const SizedBox(
                      width: 16.0,
                    ),
                    Flexible(
                      child: ElevatedButton(
                        child: Text(AppLocalizations.of(context)!.sessionExpirySheet_lbl_logoutCancel),
                        onPressed: () {
                          Navigator.pop(context);
                        },
                      ),
                    ),
                  ]),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
