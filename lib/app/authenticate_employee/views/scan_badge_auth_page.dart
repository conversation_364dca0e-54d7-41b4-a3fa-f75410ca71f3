import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sort_pro_printer_flutter/app/authenticate_employee/cubit/authentication_cubit.dart';
import 'package:sort_pro_printer_flutter/app/authenticate_employee/views/badge_image_widget.dart';
import 'package:sort_pro_printer_flutter/app/authenticate_employee/views/language_toggle.dart';
import 'package:sort_pro_printer_flutter/app/authenticate_employee/views/no_badge_auth_page.dart';
import 'package:sort_pro_printer_flutter/app/custom_app_bar.dart';
import 'package:sort_pro_printer_flutter/app/help/help_document_page.dart';
import 'package:sort_pro_printer_flutter/app/scanner/data/models/barcode.dart';
import 'package:sort_pro_printer_flutter/app/scanner/data/repositories/scanner_repository.dart';
import 'package:sort_pro_printer_flutter/services/logs/i_log_service.dart';
import 'package:sort_pro_printer_flutter/styles/scale_size.dart';
import 'package:sort_pro_printer_flutter/utils/barcode_parser.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

import '../../manage/cubit/language_cubit.dart';
import '../../manage/data/models/supported_languages.dart';

class ScanBadgeAuthPage extends StatefulWidget {
  const ScanBadgeAuthPage({super.key});

  static MaterialPageRoute route(BuildContext context, {bool fromAuth = false}) {
    context.read<ILogService>().event("Navigating to page", additionalProperties: {"page": "ScanBadgeAuthPage"});
    return MaterialPageRoute(
      builder: (_) => const ScanBadgeAuthPage(),
    );
  }

  @override
  State<ScanBadgeAuthPage> createState() => _ScanBadgeAuthPageState();
}

class _ScanBadgeAuthPageState extends State<ScanBadgeAuthPage> {
  late final StreamSubscription streamSubscription;

  @override
  void initState() {
    streamSubscription = context.read<ScannerRepository>().scannedBarcodes.listen((barcode) {
      /// validate the barcode is for the emp badge, and that we are not already authenticated
      if (barcode.barcodeType == BarcodeType.employeeBadge &&
          context.read<AuthenticationCubit>().state.status != AuthenticationStatus.authenticated) {
        final barcodeData = BarcodeParser.decodeBarcode(barcode);
        context.read<AuthenticationCubit>().authenticate(barcodeData.employeeId!);
      }
    });
    super.initState();
  }

  @override
  void dispose() {
    streamSubscription.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthenticationCubit, AuthenticationState>(
      builder: (context, state) {
        final langCode = context.select<LanguageCubit, String>((languageCubit) => languageCubit.state);
        return Scaffold(
          appBar: CustomAppBar(
            title: SizedBox(
              width: 130,
              child: Image.asset("assets/sortpro_app_logo.png"),
            ),
            shape: Border.all(color: Colors.transparent),
            actions: const [
              LanguageToggle(),
            ],
            initialHelpDocumentPage: HelpDocumentPage.signIn,
          ),
          body: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const SizedBox(),
                  const Flexible(child: BadgeImageWidget()),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          AppLocalizations.of(context)!.signIn,
                          style: Theme.of(context).textTheme.headlineMedium!.copyWith(fontWeight: FontWeight.w600),
                          textScaler: TextScaler.linear(ScaleUtil.textScaleFactor(context)),
                        ),
                        const SizedBox(
                          height: 8.0,
                        ),
                        Text(
                          AppLocalizations.of(context)!.scanToSignIn,
                          style: Theme.of(context).textTheme.titleMedium!.copyWith(fontWeight: FontWeight.w500),
                          textAlign: TextAlign.center,
                          textScaler: TextScaler.linear(ScaleUtil.textScaleFactor(context)),
                        ),
                      ],
                    ),
                  ),
                  Flexible(
                    child: InkWell(
                      onTap: () {
                        Navigator.push(
                          context,
                          NoBadgeAuthPage.route(context),
                        );
                      },
                      child: Builder(builder: (context) {
                        final baseTextStyle = Theme.of(context).textTheme.bodyMedium;
                        return Text(
                          AppLocalizations.of(context)!.noIdBadge,
                          style: baseTextStyle?.copyWith(
                            decoration: TextDecoration.underline,
                            color: baseTextStyle.color?.withOpacity(.7),
                          ),
                        );
                      }),
                    ),
                  ),
                  const SizedBox(
                    height: 50,
                  ),
                  SizedBox(
                      width: 130,
                      child: langCode == SupportedLanguages.english
                          ? Image.asset("assets/sortpro_with_love.png")
                          : Image.asset("assets/sortpro_with_love_fr.png")),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
