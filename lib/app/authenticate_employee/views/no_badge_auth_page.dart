import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:sort_pro_printer_flutter/app/authenticate_employee/cubit/authentication_cubit.dart';
import 'package:sort_pro_printer_flutter/app/custom_app_bar.dart';
import 'package:sort_pro_printer_flutter/app/help/help_document_page.dart';
import 'package:sort_pro_printer_flutter/services/logs/i_log_service.dart';

class NoBadgeAuthPage extends StatefulWidget {
  const NoBadgeAuthPage._({Key? key}) : super(key: key);

  static MaterialPageRoute route(BuildContext context, {Key? key}) {
    context.read<ILogService>().event("Navigating to page", additionalProperties: {"page": "NoBadgeAuthPage"});
    return MaterialPageRoute(
      builder: (_) => NoBadgeAuthPage._(
        key: key,
      ),
    );
  }

  @override
  State<NoBadgeAuthPage> createState() => _NoBadgeAuthPageState();
}

class _NoBadgeAuthPageState extends State<NoBadgeAuthPage> {
  late final TextEditingController textEditingController;
  late final ValueNotifier<bool> validEmployeeNumberNotifier;
  late final FocusNode focusNode;

  @override
  void initState() {
    textEditingController = TextEditingController();
    validEmployeeNumberNotifier = ValueNotifier(false);
    focusNode = FocusNode();
    focusNode.requestFocus();
    super.initState();
  }

  @override
  void dispose() {
    textEditingController.dispose();
    validEmployeeNumberNotifier.dispose();
    focusNode.dispose();
    super.dispose();
  }

  validateEmpNumber(String? value) => validEmployeeNumberNotifier.value = value != null && value.isNotEmpty;

  authenticate() => context.read<AuthenticationCubit>().authenticate(textEditingController.text.trim());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: Text(AppLocalizations.of(context)!.signIn),
        initialHelpDocumentPage: HelpDocumentPage.signIn,
      ),
      body: Padding(
        padding: const EdgeInsets.only(left: 8.0, right: 8.0, top: 36.0),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(AppLocalizations.of(context)!.employeeNumber),
              const SizedBox(
                height: 8.0,
              ),
              TextField(
                keyboardType: TextInputType.number,
                focusNode: focusNode,
                onChanged: validateEmpNumber,
                controller: textEditingController,
                maxLength: 10,
                decoration: const InputDecoration(
                  hintText: "e.g 182928392",
                  counter: SizedBox.shrink(),
                ),
                onSubmitted: (_) {
                  if (validEmployeeNumberNotifier.value) authenticate();
                },
              ),
              const SizedBox(
                height: 8.0,
              ),
              ValueListenableBuilder<bool>(
                valueListenable: validEmployeeNumberNotifier,
                builder: (context, valid, _) {
                  return ElevatedButton(
                    onPressed: valid ? authenticate : null,
                    child: Text(
                      AppLocalizations.of(context)!.signIn,
                    ),
                  );
                },
              )
            ],
          ),
        ),
      ),
    );
  }
}
