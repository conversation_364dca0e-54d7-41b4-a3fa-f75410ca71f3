import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sort_pro_printer_flutter/app/manage/cubit/language_cubit.dart';
import 'package:sort_pro_printer_flutter/app/manage/data/models/supported_languages.dart';

// Quick language toggle for the login page
class LanguageToggle extends StatelessWidget {
  const LanguageToggle({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final langCode = context.select<LanguageCubit, String>((languageCubit) => languageCubit.state);
    return IconButton(
      icon: Text(langCode.toUpperCase()),
      onPressed: () => context
          .read<LanguageCubit>()
          .changeLanguage(langCode == SupportedLanguages.english ? SupportedLanguages.french : SupportedLanguages.english),
    );
  }
}
