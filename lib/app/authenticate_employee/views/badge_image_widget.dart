import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

/// This is used as a display when waiting for the employee to scan their badge
class BadgeImageWidget extends StatelessWidget {
  const BadgeImageWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return CircleAvatar(
      maxRadius: 48,
      backgroundColor: Colors.white,
      child: Container(
        padding: const EdgeInsets.all(22),
        child: SvgPicture.asset(
          "assets/signin.svg",
        ),
      ),
    );
  }
}
