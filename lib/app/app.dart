import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:sort_pro_printer_flutter/app/app_events/models/app_event.dart';
import 'package:sort_pro_printer_flutter/app/app_events/views/app_event_view.dart';
import 'package:sort_pro_printer_flutter/app/app_lifecycle/app_lifecycle_observer.dart';
import 'package:sort_pro_printer_flutter/app/app_lifecycle/cubit/app_lifecycle_cubit.dart';
import 'package:sort_pro_printer_flutter/app/peripherals/peripherals_repository.dart';
import 'package:sort_pro_printer_flutter/db/ssws_database_manager.dart';
import 'package:sort_pro_printer_flutter/environment/environment_config.dart';
import 'package:sort_pro_printer_flutter/services/data_provider/i_data_provider_service.dart';
import 'package:sort_pro_printer_flutter/services/device_info/device_info_service.dart';
import 'package:sort_pro_printer_flutter/services/device_info/i_device_info_service.dart';
import 'package:sort_pro_printer_flutter/services/i_lookup_service.dart';
import 'package:sort_pro_printer_flutter/services/lookup_service.dart';
import 'package:sort_pro_printer_flutter/services/network/i_network_service.dart';
import 'package:sort_pro_printer_flutter/services/network/network_service.dart';
import 'package:sort_pro_printer_flutter/services/printer/zebra_printer_service.dart';
import 'package:sort_pro_printer_flutter/app/app_events/cubit/app_event_cubit.dart';
import 'package:sort_pro_printer_flutter/app/data_sync/cubit/data_sync_cubit.dart';

import 'package:sort_pro_printer_flutter/app/manage/cubit/app_setting_cubit.dart';
import 'package:sort_pro_printer_flutter/app/authenticate_employee/cubit/authentication_cubit.dart';
import 'package:sort_pro_printer_flutter/app/authenticate_employee/views/scan_badge_auth_page.dart';
import 'package:sort_pro_printer_flutter/app/manage/cubit/language_cubit.dart';
import 'package:sort_pro_printer_flutter/app/scanner/data/repositories/scanner_repository.dart';
import 'package:sort_pro_printer_flutter/services/data_provider/data_provider_service.dart';
import 'package:sort_pro_printer_flutter/services/logs/i_log_service.dart';
import 'package:sort_pro_printer_flutter/app/manage/views/manage_page.dart';
import 'package:sort_pro_printer_flutter/app/splash/splash_screen.dart';
import 'package:sort_pro_printer_flutter/services/ring_scanner/zebra_ringer_scanner_service.dart';
import 'package:sort_pro_printer_flutter/services/scan_logs/i_scan_log_service.dart';
import 'package:sort_pro_printer_flutter/services/scan_logs/models/scan_log.dart';
import 'package:sort_pro_printer_flutter/services/scan_logs/scan_log_service.dart';
import 'package:sort_pro_printer_flutter/services/scan_logs/scanlog_db_repository.dart';
import 'package:sort_pro_printer_flutter/services/scanner/scanner_service.dart';
import 'package:sort_pro_printer_flutter/services/sslws/sslws_service.dart';
import 'package:sort_pro_printer_flutter/styles/themes.dart';

import 'peripherals/cubit/peripherals_cubit.dart';
import 'pin_lookup/data/models/lookup_result.dart';

class SortProPrinter extends StatefulWidget {
  final ILogService logService;

  const SortProPrinter({super.key, required this.logService});

  @override
  State<SortProPrinter> createState() => _SortProPrinterState();
}

class _SortProPrinterState extends State<SortProPrinter> {
  late final ScannerRepository scannerRepository;
  late final ILookupService lookupService;
  late final SslwsService sslwsService;
  late final IDataProviderService dataProviderService;
  late final INetworkService networkService;
  late final IScanLogService scanLogService;
  late final IDeviceInfoService deviceInfoService;
  StreamSubscription<LookupResult>? lookUpResultStreamSubscription;
  StreamSubscription? scannerTriggeredSubscription;

  late final AuthenticationCubit authenticationCubit;
  late final AppSettingCubit appSettingCubit;
  late final AppEventCubit appEventCubit;
  late final DataSyncCubit dataSyncCubit;
  late final AppLifecycleCubit appLifecycleCubit;
  final GlobalKey<NavigatorState> navKey = GlobalKey<NavigatorState>();
  final GlobalKey<ScaffoldMessengerState> snackbarKey = GlobalKey<ScaffoldMessengerState>();

  @override
  void initState() {
    scannerRepository = ScannerRepository(ScannerService(widget.logService));
    authenticationCubit = AuthenticationCubit(widget.logService);
    dataProviderService = DataProviderService(SswsDatabaseManager());
    deviceInfoService = DeviceInfoService();
    // Set both the device ID and the App Version in the logs
    deviceInfoService.getAppVersion().then((value) => widget.logService.setAppVersion(value));
    deviceInfoService.getDeviceId().then((value) => widget.logService.setDeviceId(value));
    networkService = NetworkService()..initialize();
    appSettingCubit = AppSettingCubit(dataProviderService: dataProviderService, logService: widget.logService);
    appEventCubit = AppEventCubit();
    dataSyncCubit = DataSyncCubit(authenticationCubit, appSettingCubit, appEventCubit, dataProviderService, networkService);
    appLifecycleCubit = AppLifecycleCubit();
    dataSyncCubit.initialize();

    sslwsService = SslwsService(
      EnvironmentConfig.sslwsDomain,
      EnvironmentConfig.sslwsUrl,
      EnvironmentConfig.sslwsUser,
      EnvironmentConfig.sslwsPassword,
    );
    /// Listen to Lookups and add publish ScanLog events
    lookupService = LookupService(
      SswsDatabaseManager(),
      sslwsService,
      widget.logService,
      dataProviderService,
    );
    scanLogService = ScanLogService(ScanLogDbRepository());
    lookUpResultStreamSubscription = lookupService.lookupResultStream.listen((LookupResult lookupResult) {
      scanLogService.publishScanLogEvent(ScanLog.fromLookupResult(lookupResult));
    });
    scannerTriggeredSubscription = scannerRepository.scannerTriggered.listen((_) {
      if (authenticationCubit.authenticated) appEventCubit.addEvent(AppEvent.empty(AppEventCode.userActive));
    });

    super.initState();
  }

  @override
  void dispose() {
    authenticationCubit.close();
    appSettingCubit.close();
    dataSyncCubit.close();
    appEventCubit.close();
    appLifecycleCubit.close();
    scanLogService.dispose();
    networkService.dispose();
    scannerRepository.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final systemLocale = View.of(context).platformDispatcher.locale;

    return MultiRepositoryProvider(
      providers: [
        RepositoryProvider.value(
          value: widget.logService,
        ),
        RepositoryProvider.value(
          value: scannerRepository,
        ),
        RepositoryProvider.value(
          value: lookupService,
        ),
        RepositoryProvider.value(
          value: sslwsService,
        ),
        RepositoryProvider.value(
          value: dataProviderService,
        ),
        RepositoryProvider.value(
          value: networkService,
        ),
        RepositoryProvider.value(
          value: scanLogService,
        ),
        RepositoryProvider.value(
          value: deviceInfoService,
        ),
      ],
      child: MultiBlocProvider(
        providers: [
          BlocProvider.value(value: authenticationCubit),
          BlocProvider.value(value: appSettingCubit),
          BlocProvider.value(value: dataSyncCubit),
          BlocProvider.value(value: appEventCubit),
          BlocProvider.value(value: appLifecycleCubit),
          BlocProvider(create: (context) => LanguageCubit(systemLocale.languageCode)),
          BlocProvider(
            create: (context) => PeripheralsCubit(
              PeripheralsRepository(
                ZebraPrinterService(widget.logService),
                ZebraRingerScannerService(),
              ),
              appEventCubit,
              appSettingCubit,
              dataProviderService,
              widget.logService,
            )..initialize(),
          ),
        ],
        child: Builder(builder: (context) {
          return BlocListener<AuthenticationCubit, AuthenticationState>(
            listener: (context, state) {
              if (state.status == AuthenticationStatus.unauthenticated) {
                // Go back to the device's locale
                // context.read<LanguageCubit>().changeLanguage(View.of(context).platformDispatcher.locale.languageCode);

                /// When the user signs OUT we send them back to the auth page
                navKey.currentState?.popUntil((route) => route.isFirst);
                navKey.currentState?.pushReplacement(
                  ScanBadgeAuthPage.route(context),
                );

                // de-queue the scan logs
                scanLogService.dequeueScanLogUpSyncScanLogs();

                // disconnect printer if connected
                context.read<PeripheralsCubit>().disconnectPrinter();
                context.read<PeripheralsCubit>().removePrinterMacAddress();
              }

              if (state.status == AuthenticationStatus.authenticated) {
                appEventCubit.addEvent(AppEvent.empty(AppEventCode.userActive));

                // Initialize the app settings when logged in.
                appSettingCubit.init();

                // enqueue the scan logs
                scanLogService.enqueueScanLogUpSyncScanLogs();

                /// When the user signs IN we send them to the manage/settings page
                navKey.currentState?.popUntil((route) => route.isFirst);
                navKey.currentState?.pushReplacement(
                  ManagePage.route(context, fromAuth: true),
                );
              }
            },
            child: BlocBuilder<LanguageCubit, String>(
              builder: (context, state) {
                return AppLifecycleObserver(
                  child: Listener(
                    // only listen to pointer down events when user is authenticated
                    onPointerDown:
                        authenticationCubit.authenticated ? (e) => appEventCubit.addEvent(AppEvent.empty(AppEventCode.userActive)) : null,
                    child: MaterialApp(
                      debugShowCheckedModeBanner: false,
                      navigatorKey: navKey,
                      scaffoldMessengerKey: snackbarKey,
                      home: const SplashScreen(),
                      builder: (context, child) => AppEventView(navigatorKey: navKey, child: child!),
                      theme: AppThemes.mainTheme,
                      locale: Locale(state),
                      localizationsDelegates: const [
                        AppLocalizations.delegate,
                        GlobalMaterialLocalizations.delegate,
                        GlobalWidgetsLocalizations.delegate,
                        GlobalCupertinoLocalizations.delegate,
                      ],
                      supportedLocales: AppLocalizations.supportedLocales,
                    ),
                  ),
                );
              },
            ),
          );
        }),
      ),
    );
  }
}
