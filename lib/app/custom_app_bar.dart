import 'package:flutter/material.dart';
import 'package:sort_pro_printer_flutter/app/help/help_button.dart';
import 'package:sort_pro_printer_flutter/app/help/help_document_page.dart';

/// An app bar for every page in the app that encapsulates common behaviour
/// Use this in favor of a regular AppBar
class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  final Widget? title;
  final List<Widget> actions;
  final Widget? leading;
  final bool showHelpButton;
  final ShapeBorder? shape;
  final HelpDocumentPage initialHelpDocumentPage;

  const CustomAppBar({
    Key? key,
    this.title,
    this.actions = const [],
    this.showHelpButton = true,
    this.shape,
    this.leading,
    this.initialHelpDocumentPage = HelpDocumentPage.initial,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AppBar(
      leading: leading,
      title: title,
      titleTextStyle: Theme.of(context).textTheme.titleMedium,
      centerTitle: true,
      actions: showHelpButton ? [...actions, HelpButton(initialPage: initialHelpDocumentPage)] : actions,
      shape: shape,
    );
  }

  @override
  Size get preferredSize => const Size(double.infinity, 60);
}
