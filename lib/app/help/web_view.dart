import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../services/logs/i_log_service.dart';
import '../custom_app_bar.dart';

class WebViewPage extends StatefulWidget {
  final String url;
  final String title;

  static MaterialPageRoute route(BuildContext context, String url, String title) {
    context.read<ILogService>().event("Navigating to page", additionalProperties: {"page": title});

    return MaterialPageRoute(builder: (context) {
      return WebViewPage(url: url, title: title);
    });
  }

  const WebViewPage({Key? key, required this.url, required this.title}) : super(key: key);

  @override
  State<WebViewPage> createState() => _WebViewPageState();
}

class _WebViewPageState extends State<WebViewPage> {
  late final WebViewController controller;
  late final String title;

  @override
  void initState() {
    title = widget.title;
    controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..loadRequest(Uri.parse(widget.url));
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: Text(title),
      ),
      body: WebViewWidget(controller: controller),
    );
  }
}
