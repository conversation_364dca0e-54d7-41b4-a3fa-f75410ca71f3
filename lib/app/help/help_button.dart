import 'dart:async';

import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'package:sort_pro_printer_flutter/app/custom_app_bar.dart';
import 'package:sort_pro_printer_flutter/app/help/help_document_page.dart';
import 'package:sort_pro_printer_flutter/app/manage/cubit/language_cubit.dart';
import 'package:sort_pro_printer_flutter/app/manage/data/models/supported_languages.dart';
import 'package:sort_pro_printer_flutter/services/logs/i_log_service.dart';

class HelpButton extends StatelessWidget {
  const HelpButton({Key? key, this.initialPage = HelpDocumentPage.initial}) : super(key: key);

  final HelpDocumentPage initialPage;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<LanguageCubit, String>(builder: (context, language) {
      return PopupMenuButton(
        shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(10.0))),
        offset: const Offset(10, 50),
        itemBuilder: (BuildContext context) => <PopupMenuEntry>[
          PopupMenuItem(
            child: Text(AppLocalizations.of(context)!.helpPage_lbl_title),
            onTap: () => Navigator.push(context, HelpDocumentView.route(context, initialPage: initialPage))
          )]
      );
    });
  }
}

class HelpDocumentView extends StatefulWidget {
  static MaterialPageRoute route(BuildContext context, {HelpDocumentPage initialPage = HelpDocumentPage.initial}) {
    context.read<ILogService>().event("Navigating to page", additionalProperties: {"page": "HelpDocumentView"});
    return MaterialPageRoute(builder: (context) {
      return HelpDocumentView(
        initialPage: initialPage,
      );
    });
  }

  final HelpDocumentPage initialPage;

  const HelpDocumentView({Key? key, this.initialPage = HelpDocumentPage.initial}) : super(key: key);

  @override
  State<HelpDocumentView> createState() => _HelpDocumentViewState();
}

class _HelpDocumentViewState extends State<HelpDocumentView> {
  static const _userGuideEngAssetPath = "assets/help/SortPro_UserGuide_Eng.pdf";
  static const _userGuideFrAssetPath = "assets/help/SortPro_UserGuide_Fr.pdf";

  Uint8List? bytes;
  PDFViewController? pdfViewController;

  @override
  void initState() {
    loadFile();
    super.initState();
  }

  Future<Uint8List?> fromAsset(String asset, String filename) async {
    try {
      var data = await rootBundle.load(asset);
      bytes = data.buffer.asUint8List();
    } catch (e) {
      throw Exception('Error parsing asset file!');
    }

    return bytes;
  }

  loadFile() async {
    final languageCode = context.read<LanguageCubit>().state;
    final bytes = await fromAsset(languageCode == SupportedLanguages.french ? _userGuideFrAssetPath : _userGuideEngAssetPath, "user-guide-$languageCode.pdf");
    setState(() {
      this.bytes = bytes;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        showHelpButton: false,
        title: Text(AppLocalizations.of(context)!.helpPage_lbl_title),
        shape: Border.all(color: Colors.transparent),
      ),
      body: bytes == null
          ? const Center(
              child: CircularProgressIndicator(),
            )
          : PDFView(
              pdfData: bytes,
              enableSwipe: true,
              onViewCreated: (controller) async {
                pdfViewController = controller;
              },
              onPageChanged: (currentPage, lastPage) {},
              onRender: (pageNumber) {
                pdfViewController?.setPage(widget.initialPage.pageNumber);
              },
            ),
    );
  }
}
