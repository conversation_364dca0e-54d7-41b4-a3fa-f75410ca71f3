part of 'data_sync_cubit.dart';

class DataSyncState extends Equatable {
  /// If there was a first time the app succeded to get data
  final bool firstTimeSyncSuccess;

  /// Whether the last data sync was a success
  final bool? lastSyncSuccess;

  /// Last timestamp when we had a successful sync
  final DateTime? lastSuccessfulSync;

  /// To know whether there are db3 files available or not
  final bool dataExistsLocally;

  /// To know if we can go fetch the data or not
  final bool serverAccessible;

  const DataSyncState({
    this.lastSyncSuccess,
    this.lastSuccessfulSync,
    this.dataExistsLocally = false,
    this.firstTimeSyncSuccess = false,
    this.serverAccessible = true,
  });

  DataSyncState copyWith({
    bool? lastSyncSuccess,
    DateTime? lastSuccessfulSync,
    bool? dataExistsLocally,
    bool? serverAccessible,
  }) =>
      DataSyncState(
        lastSyncSuccess: lastSyncSuccess ?? this.lastSyncSuccess,
        lastSuccessfulSync: lastSuccessfulSync ?? this.lastSuccessfulSync,
        dataExistsLocally: dataExistsLocally ?? this.dataExistsLocally,
        serverAccessible: serverAccessible ?? this.serverAccessible,
      );

  @override
  List<Object?> get props => [
        lastSyncSuccess,
        lastSuccessfulSync,
        dataExistsLocally,
        serverAccessible,
      ];
}
