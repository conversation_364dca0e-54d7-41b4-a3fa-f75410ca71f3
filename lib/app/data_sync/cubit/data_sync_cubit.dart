import 'dart:async';

import 'package:equatable/equatable.dart';
import 'package:sort_pro_printer_flutter/db/ssws_database_manager.dart';
import 'package:sort_pro_printer_flutter/services/network/i_network_service.dart';
import 'package:timeago/timeago.dart' as timeago;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sort_pro_printer_flutter/app/app_events/cubit/app_event_cubit.dart';
import 'package:sort_pro_printer_flutter/app/app_events/models/app_event.dart';
import 'package:sort_pro_printer_flutter/app/authenticate_employee/cubit/authentication_cubit.dart';
import 'package:sort_pro_printer_flutter/app/manage/cubit/app_setting_cubit.dart';
import 'package:sort_pro_printer_flutter/services/data_provider/i_data_provider_service.dart';

part 'data_sync_state.dart';

class DataSyncCubit extends Cubit<DataSyncState> {
  final AuthenticationCubit _authenticationCubit;
  final AppSettingCubit _appSettingCubit;
  final AppEventCubit _appEventCubit;
  final IDataProviderService _dataProviderService;
  final INetworkService _networkService;
  final SswsDatabaseManager dbManager = SswsDatabaseManager();

  StreamSubscription? _authenticationCubitSubscription;
  StreamSubscription? _appSettingsCubitSubscription;
  StreamSubscription<DateTime>? _lastSuccessfulSyncSubscription;
  StreamSubscription<bool>? _lastSyncSuccessSubscription;
  StreamSubscription<int>? _numOfRetriesSubscription;
  StreamSubscription<NetworkStatus>? _networkStatusSubscription;

  String userId = "";

  DataSyncCubit(
    this._authenticationCubit,
    this._appSettingCubit,
    this._appEventCubit,
    this._dataProviderService,
    this._networkService,
  ) : super(const DataSyncState());

  bool authenticated = false;
  String? terminalId;
  String? syncSchedule;

  initialize() async {
    _lastSuccessfulSyncSubscription = _dataProviderService.lastSuccessfulSyncStream.listen((DateTime syncTime) {
      emit(state.copyWith(lastSuccessfulSync: syncTime));
    });

    _lastSyncSuccessSubscription = _dataProviderService.lastSyncSuccessStream.listen((bool isSuccess) {
      _checkLocalDataExists();

      /// If the sync was unsuccessful, send a warning app event
      if (!isSuccess) {
        String? lastUpdatedTimeStr = _getLastUpdateTimeStr();
        _appEventCubit.addEvent(AppEvent.warning(AppEventCode.dataOutOfSync, appEventDetails: lastUpdatedTimeStr));
      } else {
        AppEvent.empty(AppEventCode.connectionReady);
      }
      emit(state.copyWith(lastSyncSuccess: isSuccess));
    });
    _numOfRetriesSubscription = _dataProviderService.numOfRetriesSuccessStream.listen((int numOfRetries) {
      _appEventCubit.addEvent(AppEvent.warning(AppEventCode.retrySync, appEventDetails: numOfRetries, autoClose: true));
    });

    _authenticationCubitSubscription = _authenticationCubit.stream.listen((AuthenticationState state) {
      if (state.status == AuthenticationStatus.unauthenticated) {
        authenticated = false;
        _pauseDataSync();
        _unsubscribeFromNetworkChanges();
      }

      if (state.status == AuthenticationStatus.authenticated) {
        userId = state.user.id;
        authenticated = true;
        _startDataSync();
        _subscribeToNetworkChanges();
      }
    });

    _appSettingsCubitSubscription = _appSettingCubit.stream.listen((AppSettingState appSettingState) async {
      if (appSettingState.terminal?.id != terminalId || appSettingState.syncSchedule != syncSchedule) {
        // Whenever the terminal or syncSchedule changes, we enqueue the data sync
        await dbManager.deleteFilesFromDir(await dbManager.getCurrentDatabasesDirectory());
        terminalId = appSettingState.terminal?.id;
        syncSchedule = appSettingState.syncSchedule;
        _checkNetwork();
        final localDataExists = await _checkLocalDataExists();
        emit(state.copyWith(dataExistsLocally: localDataExists));
      }
    });
  }

  _startDataSync() {
    // If the settings have been set and the user is authenticated, enqueue the data sync flow
    if (terminalId != null && syncSchedule != null && authenticated) {
      final frequency = _getFrequency(syncSchedule!);
      _dataProviderService
          .startTerminalPeriodicDataDownload(terminalId: terminalId!, frequency: frequency, empNumber: userId)
          .then((value) => _checkLocalDataExists());
    }
  }

  _pauseDataSync() {
    _dataProviderService.stopTerminalPeriodicDataDownload();
  }

  _checkNetwork() async {
    final conn = await _networkService.checkConnection();
    _onNetworkStatusChanged(conn);
  }

  _subscribeToNetworkChanges() async {
    _networkStatusSubscription = _networkService.subscribeToNetworkChanges(_onNetworkStatusChanged);
  }

  _unsubscribeFromNetworkChanges() {
    _networkStatusSubscription?.cancel();
    _networkStatusSubscription = null;
  }

  Future<bool> _checkLocalDataExists() async {
    if(terminalId == null) return false;
    final exists = await _dataProviderService.terminalDataExists(terminalId!);
    emit(state.copyWith(dataExistsLocally: exists));
    return exists;
  }

  _onNetworkStatusChanged(NetworkStatus networkStatus) async {
    final connected = networkStatus == NetworkStatus.connected;
    await _checkLocalDataExists();
    if (connected) {
      _startDataSync();
      _appEventCubit.addEvent(AppEvent.empty(AppEventCode.connectionReady));
    } else {
      _pauseDataSync();
      String? lastUpdatedTimeStr = _getLastUpdateTimeStr();
      _appEventCubit.addEvent(AppEvent.error(AppEventCode.noInternet, appEventDetails: lastUpdatedTimeStr));
    }
    emit(state.copyWith(serverAccessible: networkStatus == NetworkStatus.connected));
  }

  String? _getLastUpdateTimeStr() {
    if (state.lastSuccessfulSync != null) {
      final now = DateTime.now();
      final difference = now.difference(state.lastSuccessfulSync!);
      return timeago.format(now.subtract(difference));
    }
    return null;
  }

  @override
  Future<void> close() async {
    _authenticationCubitSubscription?.cancel();
    _appSettingsCubitSubscription?.cancel();
    _lastSyncSuccessSubscription?.cancel();
    _numOfRetriesSubscription?.cancel();
    _lastSuccessfulSyncSubscription?.cancel();
    _unsubscribeFromNetworkChanges();
    _dataProviderService.dispose();
    super.close();
  }

  Duration _getFrequency(String syncSchedule) {
    switch (syncSchedule) {
      case "10":
        return const Duration(minutes: 10);
      case "15":
        return const Duration(minutes: 15);
      case "30":
        return const Duration(minutes: 30);
      case "5":
      default:
        return const Duration(minutes: 5);
    }
  }
}
