import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/widgets.dart';
import 'package:sort_pro_printer_flutter/services/logs/log_service.dart';

class AppLifecycleCubit extends Cubit<AppLifecycleState> {
  AppLifecycleCubit() : super(AppLifecycleState.resumed);

  updateLifecycleEvent(AppLifecycleState state) {
    LogService.instance.event("AppLifecycleState change: ${state.name}");
    emit(state);
  }

}
