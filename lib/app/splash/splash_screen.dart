import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sort_pro_printer_flutter/app/authenticate_employee/cubit/authentication_cubit.dart';
import 'package:sort_pro_printer_flutter/app/authenticate_employee/views/scan_badge_auth_page.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({Key? key}) : super(key: key);

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    init();
    super.initState();
  }

  init() async {
    await Future.delayed(const Duration(seconds: 3));
    if (!mounted) return;
    final authCubit = context.read<AuthenticationCubit>();
    if (authCubit.state.status == AuthenticationStatus.authenticated) {
      authCubit.validateUserSession();
    } else {
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
          builder: (context) => const ScanBadgeAuthPage(),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            Column(
              children: [
                ConstrainedBox(
                  constraints: BoxConstraints(maxWidth: MediaQuery.of(context).size.width * .7),
                  child: Transform.scale(
                    scale: 0.7, // Shrink the logo size by 30%
                    child: Image.asset("assets/app_icon.png"),
                  ),
                ),
              ],
            ),
            const SizedBox(
              width: 4,
            ),
            RichText(
              textAlign: TextAlign.center,
              text: const TextSpan(
                text: "PUROLATOR\n",
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.w400, letterSpacing: 4),
                children: <TextSpan>[
                  TextSpan(text: "\n", style: TextStyle(fontSize: 7)),
                  TextSpan(text: 'SORT ', style: TextStyle(fontSize: 28, fontWeight: FontWeight.w400, letterSpacing: 11)),
                  TextSpan(text: 'PRO', style: TextStyle(fontSize: 28, fontWeight: FontWeight.w600, letterSpacing: 11)),
                ],
              ),
            ),
            const CircularProgressIndicator(),
          ],
        ),
      ),
    );
  }
}
