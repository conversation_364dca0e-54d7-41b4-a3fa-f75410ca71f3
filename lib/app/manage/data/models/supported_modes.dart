enum SupportedModes {
  print(id: "1", name: "print"),
  both(id: "2", name: "printRemediate"),
  remediate(id: "3", name: "remediate");

  const SupportedModes({
    required this.id,
    required this.name,
  });

  final String id;
  final String name;

  static SupportedModes fromName(String name) {
    switch(name) {
      case "printRemediate":
        return SupportedModes.both;
      case "remediate":
        return SupportedModes.remediate;
      default:
        return SupportedModes.print;
    }
  }
}