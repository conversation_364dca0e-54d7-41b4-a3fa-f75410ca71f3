class Terminal {
  final String id;
  final String name;
  final String provinceCode;

  const Terminal({required this.id, required this.name, required this.provinceCode});

  Terminal copyWith({String? id, String? name, String? provinceCode}) => Terminal(
        id: id ?? this.id,
        name: name ?? this.name,
        provinceCode: provinceCode ?? this.provinceCode,
      );

  factory Terminal.fromJson(Map<String, dynamic> json) {
    return Terminal(id: json["id"], name: json["name"], provinceCode: json["provinceCode"] ?? "");
  }

  List<dynamic> toCsvRecord() => [id, name, provinceCode];

  Map<String, dynamic> toJson() => {"id": id, "name": name, "provinceCode": provinceCode};
}
