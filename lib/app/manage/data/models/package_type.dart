import 'package:flutter/widgets.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

enum PackageType {
  unclassified(0),
  mail(1),
  box(2);

  final int id;

  const PackageType(this.id);

  String getLabel(BuildContext context) {
    switch(id){
      case 0:
        return AppLocalizations.of(context)!.packageType_lbl_unclassified;
      case 1:
        return AppLocalizations.of(context)!.packageType_lbl_mail;
      case 2:
        return AppLocalizations.of(context)!.packageType_lbl_box;
    }
    return "";
  }
}