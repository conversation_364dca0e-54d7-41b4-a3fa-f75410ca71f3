import 'dart:io';
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:sort_pro_printer_flutter/app/manage/data/models/package_type.dart';
import 'package:sort_pro_printer_flutter/app/manage/data/models/supported_modes.dart';
import 'package:sort_pro_printer_flutter/app/manage/data/models/terminal.dart';
import 'package:sort_pro_printer_flutter/app/manage/data/models/user_preferences.dart';

class SettingRepository {
  // default terminalId
  final String syncSchedule = "5";
  final bool printerMandatory = false;

  // this comes in the form of en_US or fr_FR, we only care about the first 2 chars
  final String defaultLocale = Platform.localeName.substring(0, 2);

  // --------------APP SETTINGS--------------------
  Future<Terminal?> getTerminal() async {
    final prefs = await SharedPreferences.getInstance();
    final jsonTerminalStr = prefs.getString('terminal');
    if(jsonTerminalStr == null) return null;

    Terminal terminal = Terminal.fromJson(jsonDecode(jsonTerminalStr));
    // Backwards compatibility for currently deployed devices in laval and QA
    if(terminal.provinceCode.isEmpty) {
      terminal = terminal.copyWith(provinceCode: terminal.id == "522" ? "QC" : "ON");
    }
    return terminal;
  }

  void setTerminal(Terminal terminal) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('terminal', jsonEncode(terminal.toJson()));
  }

  Future<String> getSyncSchedule() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('syncSchedule') ?? syncSchedule;
  }

  void setSyncSchedule(String syncSchedule) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('syncSchedule', syncSchedule);
  }

  Future<PackageType> getPackageType() async {
    final prefs = await SharedPreferences.getInstance();
    final int? packageTypeId = prefs.getInt('packageType');
    return PackageType.values.firstWhere((element) => element.id == packageTypeId, orElse: () => PackageType.box);
  }

  void setPackageType(PackageType packageType) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt('packageType', packageType.id);
  }

  Future<bool> getPrinterMandatoryValue() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool('printerMandatory') ?? printerMandatory;
  }

  void setPrinterMandatoryValue(bool printerMandatory) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('printerMandatory', printerMandatory);
  }

  // ---------------USER SETTINGS-------------------
  Future<UserPreferences> getUserPreferences(String userId) async {
    final prefs = await SharedPreferences.getInstance();
    // default user preferences
    UserPreferences defaultPrefs = UserPreferences(mode: SupportedModes.print.name);
    Map<String, dynamic> decodedSettings = jsonDecode(
      prefs.getString(userId) ?? jsonEncode(defaultPrefs.toJson()),
    );

    return UserPreferences.fromJson(decodedSettings);
  }

  Future<String?> getLanguageCode(String userId) async {
    UserPreferences userPreferences = await getUserPreferences(userId);
    return userPreferences.languageCode;
  }

  Future<String> getMode(String userId) async {
    UserPreferences userPreferences = await getUserPreferences(userId);
    return userPreferences.mode;
  }

  void setLanguageCode(String userId, String languageCode) async {
    final prefs = await SharedPreferences.getInstance();
    UserPreferences oldPrefs = await getUserPreferences(userId);
    UserPreferences newPrefs = UserPreferences(languageCode: languageCode, mode: oldPrefs.mode);
    await prefs.setString(userId, jsonEncode(newPrefs.toJson()));
  }

  void setMode(String userId, String mode) async {
    final prefs = await SharedPreferences.getInstance();
    UserPreferences oldPrefs = await getUserPreferences(userId);
    UserPreferences newPrefs = UserPreferences(languageCode: oldPrefs.languageCode, mode: mode);
    await prefs.setString(userId, jsonEncode(newPrefs.toJson()));
  }
}
