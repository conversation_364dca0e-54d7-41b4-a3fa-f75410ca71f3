import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sort_pro_printer_flutter/app/manage/cubit/language_cubit.dart';
import 'package:sort_pro_printer_flutter/app/manage/data/models/supported_modes.dart';
import 'package:sort_pro_printer_flutter/app/manage/data/repositories/setting_repository.dart';

part 'user_setting_state.dart';

class UserSettingCubit extends Cubit<UserSettingState> {
  final LanguageCubit languageCubit;
  final String userId;

  UserSettingCubit(this.userId, this.languageCubit) : super(const UserSettingState()) {
    _init();
  }

  final SettingRepository settingRepository = SettingRepository();

  _init() async {
    String? languageCode = await settingRepository.getLanguageCode(userId) ?? languageCubit.state;
    changeLanguage(languageCode);
    final String mode = await settingRepository.getMode(userId);
    emit(state.copyWith(
      languageCode: languageCode,
      mode: mode,
    ));
  }

  void changeLanguage(String languageCode) {
    settingRepository.setLanguageCode(userId, languageCode);
    languageCubit.changeLanguage(languageCode);
    emit(state.copyWith(languageCode: languageCode));
  }

  void changeMode(SupportedModes mode) {
    settingRepository.setMode(userId, mode.name);
    emit(state.copyWith(mode: mode.name));
  }
}
