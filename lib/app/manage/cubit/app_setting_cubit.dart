import 'dart:async';

import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sort_pro_printer_flutter/app/manage/data/models/package_type.dart';
import 'package:sort_pro_printer_flutter/app/manage/data/models/terminal.dart';
import 'package:sort_pro_printer_flutter/app/manage/data/repositories/setting_repository.dart';
import 'package:sort_pro_printer_flutter/services/data_provider/i_data_provider_service.dart';
import 'package:sort_pro_printer_flutter/services/data_provider/ss_data_description.dart';
import 'package:sort_pro_printer_flutter/services/logs/i_log_service.dart';

part 'app_setting_state.dart';

class AppSettingCubit extends Cubit<AppSettingState> {
  final IDataProviderService dataProviderService;
  final ILogService logService;

  StreamSubscription<SsDataDescription>? _routePlanSubscription;
  SsDataDescription? routePlan;

  AppSettingCubit({required this.dataProviderService, required this.logService}) : super(const AppSettingState());

  final SettingRepository settingRepository = SettingRepository();

  init() async {
    final Terminal? terminal = await settingRepository.getTerminal();
    final String syncSchedule = await settingRepository.getSyncSchedule();
    final bool printerMandatory = await settingRepository.getPrinterMandatoryValue();
    final PackageType packageType = await settingRepository.getPackageType();

    SsDataDescription? routePlan;
    if(terminal != null) {
      routePlan = await dataProviderService.getDataDescriptionForTerminal(terminal.id);
    }

    _routePlanSubscription?.cancel();
    _routePlanSubscription = dataProviderService.routePlanInfoSyncStream.listen((event) {
      if(event.routePlanVersionId != routePlan?.routePlanVersionId || event.routePlanId != routePlan?.routePlanId) {
        changeRoutePlanInfo(event);
      }
    });

    emit(
      state.copyWith(
          terminal: terminal,
          syncSchedule: syncSchedule,
          printerMandatory: printerMandatory,
          packageType: packageType,
          routePlan: routePlan
      ));
  }

  void changeTerminal(Terminal terminal) async {
    settingRepository.setTerminal(terminal);
    SsDataDescription? routePlan = await dataProviderService.getDataDescriptionForTerminal(terminal.id);
    emit(state.copyWith(terminal: terminal, routePlan: routePlan));
  }

  void changeSyncSchedule(String syncSchedule) {
    settingRepository.setSyncSchedule(syncSchedule);
    emit(state.copyWith(syncSchedule: syncSchedule));
  }

  void changePackageType(PackageType packageType) {
    settingRepository.setPackageType(packageType);
    emit(state.copyWith(packageType: packageType));
  }

  void changePrinterMandatoryValue(bool mandatory) {
    settingRepository.setPrinterMandatoryValue(mandatory);
    emit(state.copyWith(printerMandatory: mandatory));
  }

  void changeRoutePlanInfo(SsDataDescription routePlan) {
    logService.trace(LogLevel.information, "Route Plan Downloaded. VersionId: ${routePlan.routePlanVersionId} at ${routePlan.downloadedDateTimeFormatted}");
    emit(state.copyWith(routePlan: routePlan));
  }

  @override
  Future<void> close() async {
    _routePlanSubscription?.cancel();
    super.close();
  }
}
