part of 'app_setting_cubit.dart';

class AppSettingState extends Equatable {
  final Terminal? terminal;
  final String? syncSchedule;
  final bool printerMandatory;
  final PackageType packageType;

  final SsDataDescription? routePlan;

  const AppSettingState({
    this.terminal,
    this.syncSchedule,
    this.printerMandatory = false,
    this.packageType = PackageType.box,
    this.routePlan,
  });

  AppSettingState copyWith({
    Terminal? terminal,
    String? syncSchedule,
    bool? printerMandatory,
    SsDataDescription? routePlan,
    PackageType? packageType,
  }) =>
      AppSettingState(
        terminal: terminal ?? this.terminal,
        syncSchedule: syncSchedule ?? this.syncSchedule,
        printerMandatory: printerMandatory ?? this.printerMandatory,
        routePlan: routePlan ?? this.routePlan,
        packageType: packageType ?? this.packageType,
      );

  @override
  List<Object?> get props => [
        terminal,
        syncSchedule,
        printerMandatory,
        routePlan,
        packageType,
      ];
}
