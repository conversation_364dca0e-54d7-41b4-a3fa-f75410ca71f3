part of 'user_setting_cubit.dart';

class UserSettingState extends Equatable {
  final String? languageCode;
  final String? mode;

  const UserSettingState({
    this.languageCode,
    this.mode,
  });

  UserSettingState copyWith({
    String? languageCode,
    String? mode,
  }) =>
      UserSettingState(
        languageCode: languageCode ?? this.languageCode,
        mode: mode ?? this.mode,
      );

  @override
  List<Object?> get props => [
        languageCode,
        mode,
      ];
}
