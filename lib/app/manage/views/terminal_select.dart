import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:material_symbols_icons/symbols.dart';

import 'package:sort_pro_printer_flutter/app/manage/cubit/app_setting_cubit.dart';
import 'package:sort_pro_printer_flutter/app/manage/data/models/terminal.dart';
import 'package:sort_pro_printer_flutter/app/manage/views/setting_page_item.dart';
import 'package:sort_pro_printer_flutter/app/manage/views/terminal_selection_page.dart';

class TerminalSelect extends StatelessWidget {
  const TerminalSelect({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Terminal? terminal = context.select((AppSettingCubit cubit) => cubit.state.terminal);

    terminalButton() {
      return TextButton(
        style: const ButtonStyle(
          padding: MaterialStatePropertyAll(EdgeInsets.only(right: 0)),
        ),
        onPressed: null,
        child: Row(
          children: [
            Text(
              terminal?.name ?? AppLocalizations.of(context)!.managePage_lbl_selectTerminal,
              style: const TextStyle(
                color: Color(0xffB4B4B4),
              ),
            ),
            const Padding(padding: EdgeInsets.only(left: 10)),
            const Icon(
              Symbols.arrow_forward_ios,
              size: 13,
              weight: 500,
              color: Color(0xffB4B4B4),
            ),
          ],
        ),
      );
    }

    return SettingsPageItem(
      label: AppLocalizations.of(context)!.terminal,
      trailing: terminalButton(),
      onPressed: () async {
        final appSettingCubit = context.read<AppSettingCubit>();
        final newTerminal = await Navigator.push(context, TerminalSelectionPage.route(context, selectedTerminal: terminal));
        if (newTerminal != null) {
          appSettingCubit.changeTerminal(newTerminal);
        }
      },
    );
  }
}
