import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:sort_pro_printer_flutter/app/custom_app_bar.dart';
import 'package:sort_pro_printer_flutter/app/help/help_document_page.dart';
import 'package:sort_pro_printer_flutter/app/manage/cubit/user_setting_cubit.dart';
import 'package:sort_pro_printer_flutter/app/manage/data/models/supported_languages.dart';
import 'package:sort_pro_printer_flutter/app/manage/views/setting_page_item.dart';
import 'package:sort_pro_printer_flutter/services/logs/i_log_service.dart';

class LanguageSelect extends StatelessWidget {
  const LanguageSelect({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Map<String, String> languageMap = {
      SupportedLanguages.english: AppLocalizations.of(context)!.english,
      SupportedLanguages.french: AppLocalizations.of(context)!.french
    };
    String? languageCode = context.select((UserSettingCubit cubit) => cubit.state.languageCode);


    return SettingsPageItem(
      label: AppLocalizations.of(context)!.language,
      trailing: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Text(
            languageMap[languageCode] ?? "",
            style: const TextStyle(
              color: Color(0xffB4B4B4),
            ),
          ),
          const Padding(padding: EdgeInsets.only(left: 10)),
          const Icon(
            Symbols.arrow_forward_ios,
            size: 13,
            weight: 500,
            color: Color(0xffB4B4B4),
          ),
        ],
      ),
      onPressed: () {
        context.read<ILogService>().event("Navigating to page", additionalProperties: {"page": "LanguageSelectorPage"});
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (_) => BlocProvider.value(
              value: context.read<UserSettingCubit>(),
              child: const LanguageSelectorPage(),
            ),
          ),
        );
      },
    );
  }
}

class LanguageSelectorPage extends StatelessWidget {
  const LanguageSelectorPage({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    String? languageCode = context.select((UserSettingCubit cubit) => cubit.state.languageCode);

    return Scaffold(
      appBar: CustomAppBar(
        title: Text(AppLocalizations.of(context)!.language),
        initialHelpDocumentPage: HelpDocumentPage.settings,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            ElevatedButton(
              onPressed: () {
                context.read<UserSettingCubit>().changeLanguage(SupportedLanguages.english);
              },
              style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xff3F3F3F),
                  foregroundColor: Colors.white,
                  side: const BorderSide(color: Color(0xff646464))),
              child: Row(
                children: [
                  Text(AppLocalizations.of(context)!.english),
                  const Spacer(),
                  languageCode == SupportedLanguages.english
                      ? const Icon(Icons.radio_button_checked)
                      : const Icon(Icons.radio_button_unchecked)
                ],
              ),
            ),
            const Padding(padding: EdgeInsets.only(bottom: 10)),
            ElevatedButton(
              onPressed: () {
                context.read<UserSettingCubit>().changeLanguage(SupportedLanguages.french);
              },
              style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xff3F3F3F),
                  foregroundColor: Colors.white,
                  side: const BorderSide(color: Color(0xff646464))),
              child: Row(
                children: [
                  Text(AppLocalizations.of(context)!.french),
                  const Spacer(),
                  languageCode == SupportedLanguages.french
                      ? const Icon(Icons.radio_button_checked)
                      : const Icon(Icons.radio_button_unchecked)
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
