import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:material_symbols_icons/material_symbols_icons.dart';
import 'package:sort_pro_printer_flutter/app/custom_app_bar.dart';
import 'package:sort_pro_printer_flutter/app/help/help_document_page.dart';
import 'package:sort_pro_printer_flutter/app/manage/cubit/app_setting_cubit.dart';
import 'package:sort_pro_printer_flutter/app/manage/data/models/package_type.dart';

import 'package:sort_pro_printer_flutter/app/manage/views/setting_page_item.dart';

import '../../../services/logs/i_log_service.dart';

class PackageTypeSelect extends StatelessWidget {
  const PackageTypeSelect({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final PackageType packageType = context.select((AppSettingCubit cubit) => cubit.state.packageType);

    packageTypeButton() {
      return TextButton(
        style: const ButtonStyle(
          padding: MaterialStatePropertyAll(EdgeInsets.only(right: 0)),
        ),
        onPressed: null,
        child: Row(
          children: [
            Text(
              packageType.getLabel(context),
              style: const TextStyle(
                color: Color(0xffB4B4B4),
              ),
            ),
            const Padding(padding: EdgeInsets.only(left: 10)),
            const Icon(
              Symbols.arrow_forward_ios,
              size: 13,
              weight: 500,
              color: Color(0xffB4B4B4),
            ),
          ],
        ),
      );
    }

    return SettingsPageItem(
      label: AppLocalizations.of(context)!.managePage_lbl_packageType,
      trailing: packageTypeButton(),
      onPressed: () {
        context.read<ILogService>().event("Navigating to page", additionalProperties: {"page": "PackageTypeSelect"});
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (_) => BlocProvider.value(
              value: context.read<AppSettingCubit>(),
              child: const PackageTypeSelectorPage(),
            ),
          ),
        );
      },
    );
  }
}

class PackageTypeSelectorPage extends StatelessWidget {
  const PackageTypeSelectorPage({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final PackageType selectedPackageType = context.select((AppSettingCubit cubit) => cubit.state.packageType);

    return Scaffold(
      appBar: CustomAppBar(
        title: Text(AppLocalizations.of(context)!.managePage_lbl_packageType),
        initialHelpDocumentPage: HelpDocumentPage.settings,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: PackageType.values.map((e) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                SelectorPageItem(
                  label: e.getLabel(context),
                  selected: selectedPackageType.id == e.id,
                  onSelected: () => context.read<AppSettingCubit>().changePackageType(e),
                ),
                const Padding(padding: EdgeInsets.only(bottom: 10)),
              ],
            );
          }).toList(),
        ),
      ),
    );
  }
}

class SelectorPageItem extends StatelessWidget {
  final String label;
  final bool selected;
  final VoidCallback onSelected;

  const SelectorPageItem({super.key, required this.selected, required this.onSelected, required this.label});

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: onSelected,
      style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xff3F3F3F), foregroundColor: Colors.white, side: const BorderSide(color: Color(0xff646464))),
      child: Row(
        children: [
          Text(label),
          const Spacer(),
          selected ? const Icon(Icons.radio_button_checked) : const Icon(Icons.radio_button_unchecked)
        ],
      ),
    );
  }
}
