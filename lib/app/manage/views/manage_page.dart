import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:sort_pro_printer_flutter/app/authenticate_employee/cubit/authentication_cubit.dart';
import 'package:sort_pro_printer_flutter/app/custom_app_bar.dart';
import 'package:sort_pro_printer_flutter/app/help/help_document_page.dart';
import 'package:sort_pro_printer_flutter/app/manage/cubit/app_setting_cubit.dart';
import 'package:sort_pro_printer_flutter/app/manage/cubit/language_cubit.dart';
import 'package:sort_pro_printer_flutter/app/manage/cubit/user_setting_cubit.dart';
import 'package:sort_pro_printer_flutter/app/manage/views/app_version_text.dart';
import 'package:sort_pro_printer_flutter/app/manage/views/mode_select.dart';
import 'package:sort_pro_printer_flutter/app/manage/views/package_type_select.dart';
import 'package:sort_pro_printer_flutter/app/manage/views/printer_mandatory_select.dart';
import 'package:sort_pro_printer_flutter/app/manage/views/ring_scanner_settings_view.dart';
import 'package:sort_pro_printer_flutter/app/manage/views/setting_page_item.dart';
import 'package:sort_pro_printer_flutter/app/manage/views/start_shift_button.dart';
import 'package:sort_pro_printer_flutter/app/manage/views/sync_shcedule_select.dart';
import 'package:sort_pro_printer_flutter/app/manage/views/terminal_select.dart';
import 'package:sort_pro_printer_flutter/app/peripherals/cubit/peripherals_cubit.dart';
import 'package:sort_pro_printer_flutter/app/peripherals/views/printer_settings_view.dart';
import 'package:sort_pro_printer_flutter/app/manage/views/language_select.dart';
import 'package:sort_pro_printer_flutter/services/logs/i_log_service.dart';

import 'confirm_bottom_sheet.dart';

class ManagePage extends StatelessWidget {
  const ManagePage({super.key});

  static MaterialPageRoute route(BuildContext context, {bool fromAuth = false}) {
    final logService = context.read<ILogService>();
    logService.event("Navigating to page", additionalProperties: {"page": "ManagePage"});
    return MaterialPageRoute(
      builder: (_) => BlocProvider(
          create: (context) => UserSettingCubit(
                context.read<AuthenticationCubit>().state.user.id,
                context.read<LanguageCubit>(),
              ),
          child: const ManagePage()),
    );
  }

  @override
  Widget build(BuildContext context) {
    final userId = context.select<AuthenticationCubit, String>((value) => value.state.user.id);
    return PopScope(
      canPop: false,
      onPopInvoked: (bool didPop) {
        context.read<PeripheralsCubit>().disconnectPrinter();
        SystemNavigator.pop();
      },
      child: Scaffold(
        appBar: CustomAppBar(
          title: SizedBox(
            width: 130,
            child: Image.asset("assets/sortpro_app_logo.png"),
          ),
          leading: IconButton(
              onPressed: () async {
                final authCubit = context.read<AuthenticationCubit>();
                final confirmed = await showModalBottomSheet<bool>(
                        context: context,
                        builder: (context) {
                          return ConfirmationBottomSheet(
                            title: AppLocalizations.of(context)!.confirmLogout_lbl_title,
                            message: AppLocalizations.of(context)!.confirmLogout_lbl_message,
                          );
                        }) ??
                    false;
                if (confirmed) authCubit.logout();
              },
              icon: const Icon(Icons.logout)),
          initialHelpDocumentPage: HelpDocumentPage.settings,
        ),
        body: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: RawScrollbar(
                thumbVisibility: true,
                child: Padding(
                  padding: const EdgeInsets.only(left: 16.0, right: 16.0, top: 16.0),
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          AppLocalizations.of(context)!.userSettings,
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        const Padding(padding: EdgeInsets.only(bottom: 10)),
                        SettingsPageItem(
                          label: AppLocalizations.of(context)!.employeeNumber,
                          trailing: Text(
                            userId,
                            style: const TextStyle(
                              color: Color(0xffB4B4B4),
                            ),
                          ),
                        ),
                        const Padding(padding: EdgeInsets.only(bottom: 10)),
                        const LanguageSelect(),
                        const Padding(padding: EdgeInsets.only(bottom: 25)),
                        Text(
                          AppLocalizations.of(context)!.appSettings,
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        const Padding(padding: EdgeInsets.only(bottom: 10)),
                        SettingsPageItem(
                          label: AppLocalizations.of(context)!.appBuild,
                          trailing: const AppVersionText(
                            style: TextStyle(
                              color: Color(0xffB4B4B4),
                            ),
                          ),
                        ),
                        const Padding(padding: EdgeInsets.only(bottom: 10)),
                        SettingsPageItem(
                          label: AppLocalizations.of(context)!.route_plan_version,
                          trailing: BlocBuilder<AppSettingCubit, AppSettingState>(
                            builder: (context, state) {
                              String routePlanId = state.routePlan?.routePlanVersionId.isNotEmpty == true
                                  ? "${state.routePlan!.routePlanId} (${state.routePlan!.routePlanVersionId})"
                                  : "N/A";
                              return Column(
                                crossAxisAlignment: CrossAxisAlignment.end,
                                children: [
                                  Text(
                                    routePlanId,
                                    style: Theme.of(context).textTheme.titleSmall!.copyWith(color: const Color(0xffB4B4B4)),
                                  ),
                                  if (state.routePlan?.downloadedDateTimeFormatted != null)
                                    Text(
                                      state.routePlan!.downloadedDateTimeFormatted!,
                                      style: Theme.of(context).textTheme.labelMedium!.copyWith(color: const Color(0xffB4B4B4)),
                                    ),
                                ],
                              );
                            },
                          ),
                        ),
                        const Padding(padding: EdgeInsets.only(bottom: 10)),
                        const PrinterSettingsView(),
                        const Padding(padding: EdgeInsets.only(bottom: 10)),
                        const RingScannerSettingsView(),
                        const Padding(padding: EdgeInsets.only(bottom: 10)),
                        const PackageTypeSelect(),
                        const Padding(padding: EdgeInsets.only(bottom: 10)),
                        const PrinterMandatorySelect(),
                        const Padding(padding: EdgeInsets.only(bottom: 10)),
                        const SyncScheduleSelect(),
                        const Padding(padding: EdgeInsets.only(bottom: 10)),
                        const TerminalSelect(),
                        const Padding(padding: EdgeInsets.only(bottom: 10)),
                      ],
                    ),
                  ),
                ),
              ),
            ),
            Container(
              color: const Color(0xff282828),
              child: const Padding(
                padding: EdgeInsets.symmetric(horizontal: 17, vertical: 13),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    ModeSelect(),
                    Padding(padding: EdgeInsets.symmetric(vertical: 7)),
                    StartShiftButton(),
                    Padding(padding: EdgeInsets.only(bottom: 10)),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
