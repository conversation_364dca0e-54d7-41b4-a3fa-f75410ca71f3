import 'package:flutter/material.dart';

class SettingsPageItem extends StatelessWidget {
  final String label;
  final Widget trailing;
  final VoidCallback? onLongPressed;
  final VoidCallback? onPressed;

  const SettingsPageItem({
    super.key,
    this.onLongPressed,
    this.onPressed,
    required this.label,
    required this.trailing,
  });

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onLongPress: onLongPressed,
      onPressed: onPressed ?? () => {},
      style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xff3F3F3F),
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(horizontal: 24),
          side: const BorderSide(color: Color(0xff646464))),
      child: Row(
        children: [
          Text(label),
          const Spacer(),
          trailing,
        ],
      ),
    );
  }
}
