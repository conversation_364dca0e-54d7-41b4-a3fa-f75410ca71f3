import 'package:flutter/material.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:sort_pro_printer_flutter/environment/environment_config.dart';

class AppVersionText extends StatefulWidget {
  final TextStyle? style;

  const AppVersionText({Key? key, this.style}) : super(key: key);

  @override
  State<AppVersionText> createState() => _AppVersionTextState();
}

class _AppVersionTextState extends State<AppVersionText> {
  Future<String>? appVersion;

  Future<String> getAppVersion() async {
    final packageInfo = await PackageInfo.fromPlatform();
    final String version = packageInfo.version;
    String env = "";
    switch(EnvironmentConfig.env.toLowerCase()) {
      case "dev":
        env = "Dev";
        break;
      case "qa":
        env = "QA";
        break;
      case "live":
        env = "Prod";
        break;
    }
    return "$env $version";
  }

  @override
  void initState() {
    appVersion = getAppVersion();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<String>(
        future: appVersion,
        builder: (context, snapshot) {
          String version = "";
          if (snapshot.hasData) {
            version = snapshot.data ?? "";
          }

          return Text(
            version,
            style: widget.style,
          );
        });
  }
}
