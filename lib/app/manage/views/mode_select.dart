import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

import 'package:sort_pro_printer_flutter/app/manage/cubit/user_setting_cubit.dart';
import 'package:sort_pro_printer_flutter/app/manage/data/models/supported_modes.dart';

class ModeSelect extends StatelessWidget {
  const ModeSelect({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xff3F3F3F),
        borderRadius: BorderRadius.circular(10),
      ),
      child: Row(
        children: [
          Expanded(
            child: _ModesDropdown(
              mode: SupportedModes.print,
              label: AppLocalizations.of(context)!.print,
              shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.only(topLeft: Radius.circular(8), bottomLeft: Radius.circular(8)),
              ),
            ),
          ),
          Expanded(child: _ModesDropdown(mode: SupportedModes.remediate, label: AppLocalizations.of(context)!.remediate)),
          Expanded(
              child: _ModesDropdown(
            mode: SupportedModes.both,
            label: AppLocalizations.of(context)!.printRemediate,
            shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.only(topRight: Radius.circular(8), bottomRight: Radius.circular(8)),
            ),
          )),
        ],
      ),
    );
  }
}

class _ModesDropdown extends StatelessWidget {
  final SupportedModes mode;
  final String label;
  final OutlinedBorder? shape;

  const _ModesDropdown({Key? key, required this.mode, required this.label, this.shape}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    context.select((UserSettingCubit cubit) => cubit.state.mode);
    context.read<UserSettingCubit>().state.mode;
    return TextButton(
      style: TextButton.styleFrom(
        foregroundColor: Colors.white,
        textStyle: const TextStyle(
          fontWeight: FontWeight.w500,
        ),
        backgroundColor: context.read<UserSettingCubit>().state.mode == mode.name ? const Color(0xff646464) : null,
        minimumSize: const Size(0, 50),
        shape: shape,
      ),
      onPressed: () {
        context.read<UserSettingCubit>().changeMode(mode);
      },
      child: Text(label),
    );
  }
}
