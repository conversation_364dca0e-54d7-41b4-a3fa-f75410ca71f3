import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sort_pro_printer_flutter/app/peripherals/cubit/peripherals_cubit.dart';
import 'package:sort_pro_printer_flutter/app/peripherals/peripheral_status.dart';
import 'package:sort_pro_printer_flutter/app/peripherals/peripheral_type.dart';
import 'package:sort_pro_printer_flutter/app/peripherals/views/connect_ring_scanner_page.dart';
import 'package:sort_pro_printer_flutter/services/ring_scanner/i_ringer_service.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

import 'peripheral_settings_view.dart';

class RingScannerSettingsView extends StatelessWidget {
  const RingScannerSettingsView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<PeripheralsCubit, PeripheralsState>(
      buildWhen: (oldState, newState) => oldState.ringScannerStatus != newState.ringScannerStatus,
      builder: (context, state) {
        return PeripheralSettingsView(
          peripheralStatus:
              state.ringScannerStatus == RingScannerStatus.connected ? PeripheralStatus.connected : PeripheralStatus.disconnected,
          peripheralType: PeripheralType.scanner,
        );
      },
    );
  }
}
