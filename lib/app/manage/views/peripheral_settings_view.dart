import 'package:flutter/material.dart';
import 'package:sort_pro_printer_flutter/app/manage/views/setting_page_item.dart';
import 'package:sort_pro_printer_flutter/app/peripherals/peripheral_status.dart';
import 'package:sort_pro_printer_flutter/app/peripherals/peripheral_type.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class PeripheralSettingsView extends StatefulWidget {
  const PeripheralSettingsView({
    super.key,
    this.title,
    this.subtitle,
    required this.peripheralType,
    this.onPressed,
    required this.peripheralStatus,
  });

  final PeripheralStatus peripheralStatus;
  final String? title;
  final String? subtitle;
  final PeripheralType peripheralType;
  final VoidCallback? onPressed;

  @override
  State<PeripheralSettingsView> createState() => _PeripheralSettingsViewState();
}

class _PeripheralSettingsViewState extends State<PeripheralSettingsView> {
  get peripheralStatus => widget.peripheralStatus;

  @override
  Widget build(BuildContext context) {
    final connectionText = peripheralStatus == PeripheralStatus.disconnected
        ? AppLocalizations.of(context)!.connectionStatus_disconnected
        : peripheralStatus == PeripheralStatus.connected
            ? AppLocalizations.of(context)!.connectionStatus_connected
            : AppLocalizations.of(context)!.connectionStatus_unavailable;
    final connectionColor = peripheralStatus == PeripheralStatus.disconnected
        ? Colors.red
        : peripheralStatus == PeripheralStatus.connected
            ? const Color(0xFF32D74B)
            : Colors.yellow;
    return SettingsPageItem(
      label: _getLabel(),
      trailing: Row(
        children: [
          peripheralStatus == PeripheralStatus.connecting ? const CircularProgressIndicator() : Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                widget.title ?? connectionText,
                style: Theme.of(context).textTheme.titleSmall!.copyWith(color: connectionColor),
              ),
              if(widget.subtitle != null)
              Text(
                widget.subtitle!,
                style: Theme.of(context).textTheme.labelMedium!.copyWith(color: const Color(0xffB4B4B4)),
              ),
            ],
          ),
        ],
      ),
      onPressed: widget.onPressed,
    );
  }

  _getLabel() {
    switch (widget.peripheralType) {
      case PeripheralType.printer:
        return AppLocalizations.of(context)!.printer;
      case PeripheralType.scanner:
        return AppLocalizations.of(context)!.fingerScanner;
    }
  }
}
