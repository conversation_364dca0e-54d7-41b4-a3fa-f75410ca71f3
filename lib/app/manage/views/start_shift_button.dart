import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:sort_pro_printer_flutter/app/app_events/cubit/app_event_cubit.dart';
import 'package:sort_pro_printer_flutter/app/app_events/models/app_event.dart';

import 'package:sort_pro_printer_flutter/app/data_sync/cubit/data_sync_cubit.dart';
import 'package:sort_pro_printer_flutter/app/manage/cubit/app_setting_cubit.dart';
import 'package:sort_pro_printer_flutter/app/manage/cubit/user_setting_cubit.dart';
import 'package:sort_pro_printer_flutter/app/manage/data/models/supported_modes.dart';
import 'package:sort_pro_printer_flutter/app/peripherals/cubit/peripherals_cubit.dart';
import 'package:sort_pro_printer_flutter/app/pin_lookup/views/pin_lookup.dart';
import 'package:sort_pro_printer_flutter/app/remediate/views/remediate_page.dart';
import 'package:sort_pro_printer_flutter/services/sslws/sslws_service.dart';

class StartShiftButton extends StatefulWidget {
  const StartShiftButton({Key? key}) : super(key: key);

  @override
  State<StartShiftButton> createState() => _StartShiftButtonState();
}

class _StartShiftButtonState extends State<StartShiftButton> {
  Timer? timer;
  ValueNotifier<String> fetchingDataDotsNotifier = ValueNotifier("");

  static const int _maxDots = 3;
  static const int _animInterval = 250;
  int _numDots = 0;
  int _numSpaces = _maxDots;

  String get fetchingData => fetchingDataDotsNotifier.value;

  set fetchingData(String value) => fetchingDataDotsNotifier.value = value;

  @override
  void initState() {
    timer = Timer.periodic(const Duration(milliseconds: _animInterval), (timer) {
      if (_numDots >= _maxDots) {
        _numDots = 0;
        _numSpaces = _maxDots;
      } else {
        _numDots++;
        _numSpaces--;
      }

      final dots = List.filled(_numDots, '.').join();
      final spaces = List.filled(_numSpaces, ' ').join();

      fetchingData = "$dots$spaces";
    });
    super.initState();
  }

  @override
  void dispose() {
    timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AppSettingCubit, AppSettingState>(buildWhen: (oldState, newState) {
      return oldState.terminal != newState.terminal;
    }, builder: (context, appSettingCubitState) {
      final terminalSet = appSettingCubitState.terminal != null;
      return BlocBuilder<DataSyncCubit, DataSyncState>(
        builder: (context, state) {
          final dataExists = state.dataExistsLocally;
          final serverReachable = state.serverAccessible;
          return ElevatedButton(
            onPressed: dataExists && terminalSet
                ? () async {
                    // Making this call will establish an initial socket connection from the http client
                    // Thus making the first scan-call faster (if done within the idle timeout time)
                    context.read<SslwsService>().activateIoSocket();
                    final userSettingsCubit = context.read<UserSettingCubit>();
                    final navigator = Navigator.of(context);
                    final pinLookupPageRoute = PinLookupPage.route(context);
                    final remediatePageRoute = RemediatePage.route(context);

                    final printerRequired = context.read<AppSettingCubit>().state.printerMandatory;
                    final printerConnected = context.read<PeripheralsCubit>().state.printerInfo.isConnected;
                    if (printerRequired && !printerConnected) {
                      context.read<AppEventCubit>().addEvent(AppEvent.warning(AppEventCode.printerNotFound));
                      return;
                    }
                    switch (SupportedModes.fromName(userSettingsCubit.state.mode!)) {
                      case SupportedModes.print:
                      case SupportedModes.both:
                        navigator.push(pinLookupPageRoute);
                        break;
                      case SupportedModes.remediate:
                        navigator.push(remediatePageRoute);
                        break;
                    }
                  }
                : null,
            style: ElevatedButton.styleFrom(alignment: Alignment.center),
            child: ValueListenableBuilder<String>(
                valueListenable: fetchingDataDotsNotifier,
                builder: (context, dots, _) {
                  return Text(
                    // we show 'start scanning' if the data exists OR the data doesn't exist but the server is unreachable
                    !terminalSet
                        ? AppLocalizations.of(context)!.managePage_lbl_warningSelectTerminal
                        : dataExists || (!dataExists && !serverReachable)
                            ? AppLocalizations.of(context)!.startScanning
                            : "${AppLocalizations.of(context)!.settings_btn_fetchingData}$dots",
                    style: TextStyle(fontWeight: FontWeight.w600, fontSize: 15, fontFamily: GoogleFonts.openSans().fontFamily),
                  );
                }),
          );
        },
      );
    });
  }
}
