import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:sort_pro_printer_flutter/app/custom_app_bar.dart';
import 'package:sort_pro_printer_flutter/app/help/help_document_page.dart';

import 'package:sort_pro_printer_flutter/app/manage/cubit/app_setting_cubit.dart';
import 'package:sort_pro_printer_flutter/app/manage/views/setting_page_item.dart';
import 'package:sort_pro_printer_flutter/services/logs/i_log_service.dart';

class SyncScheduleSelect extends StatelessWidget {
  const SyncScheduleSelect({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Map<String, String> syncScheduleMap = {
      "5": AppLocalizations.of(context)!.fiveMins,
      "10": AppLocalizations.of(context)!.tenMins,
      "15": AppLocalizations.of(context)!.fifteenMins,
      "30": AppLocalizations.of(context)!.thirtyMins,
    };
    String? syncSchedule = context.select((AppSettingCubit cubit) => cubit.state.syncSchedule);

    syncScheduleButton() {
      return TextButton(
        style: const ButtonStyle(
          padding: MaterialStatePropertyAll(EdgeInsets.only(right: 0)),
        ),
        onPressed: null,
        child: Row(
          children: [
            Text(
              syncScheduleMap[syncSchedule] ?? "",
              style: const TextStyle(
                color: Color(0xffB4B4B4),
              ),
            ),
            const Padding(padding: EdgeInsets.only(left: 10)),
            const Icon(
              Symbols.arrow_forward_ios,
              size: 13,
              weight: 500,
              color: Color(0xffB4B4B4),
            ),
          ],
        ),
      );
    }

    return SettingsPageItem(
      label: AppLocalizations.of(context)!.dataSyncFreq,
      trailing: syncScheduleButton(),
      onPressed: () {
        context.read<ILogService>().event("Navigating to page", additionalProperties: {"page": "SyncSelectorPage"});
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (_) => BlocProvider.value(
              value: context.read<AppSettingCubit>(),
              child: const SyncSelectorPage(),
            ),
          ),
        );
      },
    );
  }
}

class SyncSelectorPage extends StatelessWidget {
  const SyncSelectorPage({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    String? syncSchedule = context.select((AppSettingCubit cubit) => cubit.state.syncSchedule);

    return Scaffold(
      appBar: CustomAppBar(
        title: Text(AppLocalizations.of(context)!.dataSyncFreq),
        initialHelpDocumentPage: HelpDocumentPage.settings,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            ElevatedButton(
              onPressed: () {
                context.read<AppSettingCubit>().changeSyncSchedule("5");
              },
              style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xff3F3F3F),
                  foregroundColor: Colors.white,
                  side: const BorderSide(color: Color(0xff646464))),
              child: Row(
                children: [
                  Text(AppLocalizations.of(context)!.fiveMins),
                  const Spacer(),
                  syncSchedule == "5" ? const Icon(Icons.radio_button_checked) : const Icon(Icons.radio_button_unchecked)
                ],
              ),
            ),
            const Padding(padding: EdgeInsets.only(bottom: 10)),
            ElevatedButton(
              onPressed: () {
                context.read<AppSettingCubit>().changeSyncSchedule("10");
              },
              style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xff3F3F3F),
                  foregroundColor: Colors.white,
                  side: const BorderSide(color: Color(0xff646464))),
              child: Row(
                children: [
                  Text(AppLocalizations.of(context)!.tenMins),
                  const Spacer(),
                  syncSchedule == "10" ? const Icon(Icons.radio_button_checked) : const Icon(Icons.radio_button_unchecked)
                ],
              ),
            ),
            const Padding(padding: EdgeInsets.only(bottom: 10)),
            ElevatedButton(
              onPressed: () {
                context.read<AppSettingCubit>().changeSyncSchedule("15");
              },
              style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xff3F3F3F),
                  foregroundColor: Colors.white,
                  side: const BorderSide(color: Color(0xff646464))),
              child: Row(
                children: [
                  Text(AppLocalizations.of(context)!.fifteenMins),
                  const Spacer(),
                  syncSchedule == "15" ? const Icon(Icons.radio_button_checked) : const Icon(Icons.radio_button_unchecked)
                ],
              ),
            ),
            const Padding(padding: EdgeInsets.only(bottom: 10)),
            ElevatedButton(
              onPressed: () {
                context.read<AppSettingCubit>().changeSyncSchedule("30");
              },
              style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xff3F3F3F),
                  foregroundColor: Colors.white,
                  side: const BorderSide(color: Color(0xff646464))),
              child: Row(
                children: [
                  Text(AppLocalizations.of(context)!.thirtyMins),
                  const Spacer(),
                  syncSchedule == "30" ? const Icon(Icons.radio_button_checked) : const Icon(Icons.radio_button_unchecked)
                ],
              ),
            ),
            const Padding(padding: EdgeInsets.only(bottom: 10)),
          ],
        ),
      ),
    );
  }
}
