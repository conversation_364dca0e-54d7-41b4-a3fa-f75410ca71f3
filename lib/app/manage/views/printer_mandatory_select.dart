import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:sort_pro_printer_flutter/app/custom_app_bar.dart';
import 'package:sort_pro_printer_flutter/app/help/help_document_page.dart';

import 'package:sort_pro_printer_flutter/app/manage/cubit/app_setting_cubit.dart';
import 'package:sort_pro_printer_flutter/app/manage/views/setting_page_item.dart';
import 'package:sort_pro_printer_flutter/services/logs/i_log_service.dart';

class PrinterMandatorySelect extends StatelessWidget {
  const PrinterMandatorySelect({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SettingsPageItem(
      label: AppLocalizations.of(context)!.printerRequired,
      trailing: const SyncScheduleButton(),
      onPressed: () {
        context.read<ILogService>().event("Navigating to page", additionalProperties: {"page": "PrinterMandatorySelectorPage"});
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (_) => BlocProvider.value(
              value: context.read<AppSettingCubit>(),
              child: const PrinterMandatorySelectorPage(),
            ),
          ),
        );
      },
    );
  }
}

class SyncScheduleButton extends StatelessWidget {
  const SyncScheduleButton({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final bool printerMandatory = context.select((AppSettingCubit cubit) => cubit.state.printerMandatory) ?? false;

    return TextButton(
      style: const ButtonStyle(
        padding: MaterialStatePropertyAll(EdgeInsets.only(right: 0)),
      ),
      onPressed: null,
      child: Padding(
        padding: const EdgeInsets.only(left: 30), //Added because printerMandatory text is very short
        child: Row(
          children: [
            Text(
              printerMandatory
                  ? AppLocalizations.of(context)!.printerRequired_option_yes
                  : AppLocalizations.of(context)!.printerRequired_option_no,
              style: const TextStyle(
                color: Color(0xffB4B4B4),
              ),
            ),
            const Padding(padding: EdgeInsets.only(left: 10)),
            const Icon(
              Symbols.arrow_forward_ios,
              size: 13,
              weight: 500,
              color: Color(0xffB4B4B4),
            ),
          ],
        ),
      ),
    );
  }
}

class PrinterMandatorySelectorPage extends StatelessWidget {
  const PrinterMandatorySelectorPage({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final bool mandatory = context.select((AppSettingCubit cubit) => cubit.state.printerMandatory);

    return Scaffold(
      appBar: CustomAppBar(
        title: Text(AppLocalizations.of(context)!.printerRequired),
        initialHelpDocumentPage: HelpDocumentPage.settings,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            ElevatedButton(
              onPressed: () {
                context.read<AppSettingCubit>().changePrinterMandatoryValue(true);
              },
              style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xff3F3F3F),
                  foregroundColor: Colors.white,
                  side: const BorderSide(color: Color(0xff646464))),
              child: Row(
                children: [
                  Text(AppLocalizations.of(context)!.printerRequired_option_yes),
                  const Spacer(),
                  mandatory ? const Icon(Icons.radio_button_checked) : const Icon(Icons.radio_button_unchecked)
                ],
              ),
            ),
            const Padding(padding: EdgeInsets.only(bottom: 10)),
            ElevatedButton(
              onPressed: () {
                context.read<AppSettingCubit>().changePrinterMandatoryValue(false);
              },
              style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xff3F3F3F),
                  foregroundColor: Colors.white,
                  side: const BorderSide(color: Color(0xff646464))),
              child: Row(
                children: [
                  Text(AppLocalizations.of(context)!.printerRequired_option_no),
                  const Spacer(),
                  !mandatory ? const Icon(Icons.radio_button_checked) : const Icon(Icons.radio_button_unchecked)
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
