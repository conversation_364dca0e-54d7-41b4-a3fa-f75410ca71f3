import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sort_pro_printer_flutter/app/custom_app_bar.dart';
import 'package:sort_pro_printer_flutter/app/help/help_document_page.dart';
import 'package:sort_pro_printer_flutter/app/manage/data/models/terminal.dart';
import 'package:sort_pro_printer_flutter/services/logs/i_log_service.dart';
import 'package:sort_pro_printer_flutter/services/terminals/i_terminal_service.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:sort_pro_printer_flutter/services/terminals/terminal_service.dart';

class TerminalSelectionPage extends StatefulWidget {
  final Terminal? selectedTerminal;

  static MaterialPageRoute<Terminal> route(BuildContext context, {Terminal? selectedTerminal}) {
    context.read<ILogService>().event("Navigating to page", additionalProperties: {"page": "TerminalSelectionPage"});
    return MaterialPageRoute(builder: (context) {
      return TerminalSelectionPage(selectedTerminal: selectedTerminal);
    });
  }

  const TerminalSelectionPage({Key? key, this.selectedTerminal}) : super(key: key);

  @override
  State<TerminalSelectionPage> createState() => _TerminalSelectionPageState();
}

class _TerminalSelectionPageState extends State<TerminalSelectionPage> {
  late final TextEditingController searchController;
  late final Future<List<Terminal>> fetchTerminals;
  List<Terminal> terminals = const [];
  ValueNotifier<List<Terminal>> terminalsNotifier = ValueNotifier(const []);

  @override
  void initState() {
    searchController = TextEditingController();
    searchController.addListener(() {
      if (searchController.text.isEmpty) {
        terminalsNotifier.value = terminals;
      } else {
        terminalsNotifier.value = terminals
            .where((element) =>
                element.id.startsWith(searchController.text) || element.name.toLowerCase().startsWith(searchController.text.toLowerCase()))
            .toList();
      }
    });
    final ITerminalService terminalService = TerminalService();
    fetchTerminals = terminalService.getTerminals();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: Text(AppLocalizations.of(context)!.terminal),
        initialHelpDocumentPage: HelpDocumentPage.settings,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            TextField(
              controller: searchController,
              decoration: InputDecoration(
                contentPadding: const EdgeInsets.symmetric(vertical: 0),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(11.0),
                ),
                fillColor: Colors.black,
                prefixIcon: const Icon(Icons.search, color: Color(0xff646464)),
                hintText: AppLocalizations.of(context)!.search,
                // Add a clear button to the search bar
                suffixIcon: IconButton(
                  icon: const Icon(Icons.clear),
                  color: const Color(0xff646464),
                  onPressed: () => searchController.clear(),
                ),
                // Add a search icon or button to the search bar
              ),
            ),
            const SizedBox(
              height: 10,
            ),
            Expanded(
              child: FutureBuilder<List<Terminal>>(
                  future: fetchTerminals,
                  builder: (context, snapshot) {
                    if (snapshot.hasData) {
                      terminals = snapshot.data ?? const [];
                      terminalsNotifier.value = snapshot.data ?? const [];
                    } else {
                      return const Center(child: CircularProgressIndicator());
                    }

                    return ValueListenableBuilder<List<Terminal>>(
                        valueListenable: terminalsNotifier,
                        builder: (context, terminals, _) {
                          final terminalsSorted = List<Terminal>.from(terminals)..sort((a, b) => a.name.compareTo(b.name));
                          return ListView.builder(
                            itemCount: terminalsSorted.length,
                            itemBuilder: (context, index) {
                              final terminal = terminalsSorted[index];
                              return Padding(
                                padding: const EdgeInsets.symmetric(vertical: 5.0),
                                child: ElevatedButton(
                                  style: ElevatedButton.styleFrom(
                                      backgroundColor: const Color(0xff3F3F3F),
                                      foregroundColor: Colors.white,
                                      side: const BorderSide(color: Color(0xff646464))),
                                  child: Row(
                                    children: [
                                      Text(
                                        "${terminal.name} (${terminal.id})",
                                        style: const TextStyle(
                                          color: Colors.white,
                                        ),
                                      ),
                                      const Spacer(),
                                      Icon(widget.selectedTerminal?.id == terminal.id
                                          ? Icons.radio_button_checked
                                          : Icons.radio_button_unchecked),
                                    ],
                                  ),
                                  onPressed: () => Navigator.pop(context, terminal),
                                ),
                              );
                            },
                          );
                        });
                  }),
            ),
          ],
        ),
      ),
    );
  }
}
