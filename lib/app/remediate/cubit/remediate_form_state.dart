part of 'remediate_form_cubit.dart';

class RemediateFormState extends Equatable {
  /// To know if a label was printed when setting this state
  final bool labelPrinted;
  final LookupResult? lookupResult;

  const RemediateFormState({
    this.lookupResult,
    this.labelPrinted = false,
  });

  copyWith({
    bool? labelPrinted,
    LookupResult? lookupResult,
  }) =>
      RemediateFormState(
        lookupResult: lookupResult ?? this.lookupResult,
        labelPrinted: labelPrinted ?? this.labelPrinted,
      );

  @override
  List<Object?> get props => [labelPrinted, lookupResult];
}
