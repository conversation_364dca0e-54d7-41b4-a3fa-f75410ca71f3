import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:sort_pro_printer_flutter/app/app_events/cubit/app_event_cubit.dart';
import 'package:sort_pro_printer_flutter/app/app_events/models/app_event.dart';
import 'package:sort_pro_printer_flutter/app/manage/data/models/supported_modes.dart';
import 'package:sort_pro_printer_flutter/app/pin_lookup/data/models/lookup_result.dart';
import 'package:sort_pro_printer_flutter/app/remediate/models/delivery_info.dart';
import 'package:sort_pro_printer_flutter/app/scanner/data/models/barcode.dart';
import 'package:sort_pro_printer_flutter/services/i_compress_service.dart';
import 'package:sort_pro_printer_flutter/services/logs/i_log_service.dart';
import 'package:sort_pro_printer_flutter/services/ocr/i_ocr_service.dart';
import 'package:google_mlkit_barcode_scanning/google_mlkit_barcode_scanning.dart' as mlkit;

part 'extract_remediate_info_state.dart';

class ExtractRemediateInfoCubit extends Cubit<ExtractRemediateInfoState> {
  final IOcrService _ocrService;
  final ICompressService _compressService;
  final AppEventCubit appEventCubit;
  final ILogService _logService;
  final String terminalId;

  ExtractRemediateInfoCubit(this._ocrService, this._compressService, this._logService, this.terminalId, this.appEventCubit)
      : super(const WaitingForScan());

  openCamera() {
    _logService.event("Opening camera");
    emit(const CameraState());
  }

  closeCamera() {
    _logService.event("Closing camera");
    emit(const WaitingForScan());
  }

  reviewPicture(File file) {
    _logService.event("Reviewing photo");
    emit(ReviewPhotoState(file));
  }

  resetState() => emit(const WaitingForScan());

  openAddressEntryFormEmpty() => emit(const AddressEntryState(DeliveryInfo()));

  lookupRemediationFromPicture(File file) async {
    emit(const LookingUpRemediateResults());
    DeliveryInfo deliveryInfo = const DeliveryInfo();
    try {
      final compressedFile = await _compressService.compressFile(file);
      uploadFileForTraining(compressedFile);
      final List<mlkit.BarcodeFormat> formats = [mlkit.BarcodeFormat.pdf417];
      final barcodeScanner = mlkit.BarcodeScanner(formats: formats);
      final List<mlkit.Barcode> barcodes = await barcodeScanner.processImage(mlkit.InputImage.fromFile(compressedFile));
      if (barcodes.isNotEmpty && barcodes.first.rawValue != null) {
        final barcode = barcodes.first;
        deliveryInfo = DeliveryInfo.fromBarcode(Barcode(barcode.rawValue!, BarcodeSymbology.pdf417));
      } else {
        deliveryInfo = await _getDeliveryInfoFromPicture(file);
      }
      emit(AddressEntryState(deliveryInfo));
    } catch (e, s) {
      _logService.error(e, s, false);
      if (e is DioException) {
        appEventCubit.addEvent(AppEvent.error(AppEventCode.unableToExtractLabelInfoNoConnection, autoClose: true));
      } else {
        appEventCubit.addEvent(AppEvent.error(AppEventCode.unableToExtractLabelInfoGeneric, autoClose: true));
      }
      resetState();
    }
  }

  lookupRemediationFrom2dBarcode(Barcode barcode) {
    emit(AddressEntryState(DeliveryInfo.fromBarcode(barcode)));
  }

  Future<DeliveryInfo> _getDeliveryInfoFromPicture(File file) async {
    final compressedFile = await _compressService.compressFile(file);
    final deliveryLabel = await _ocrService.extractDeliveryInfoFromLabelFile(compressedFile);
    return deliveryLabel.deliveryInfo;
  }

  selectRemediateResult(
    LookupResult lookupResult,
    SupportedModes printMode,
    String languageCode,
  ) async {
    emit(WaitingForScan(
      labelPrinted: true,
      remediateResult: lookupResult,
    ));
  }

  uploadFileForTraining(File file) {
    _ocrService.uploadTrainingImage(file);
  }

  @override
  Future<void> close() async {
    _ocrService.dispose();
    super.close();
  }
}
