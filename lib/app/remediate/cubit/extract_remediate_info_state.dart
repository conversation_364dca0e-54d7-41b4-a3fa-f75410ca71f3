part of 'extract_remediate_info_cubit.dart';

abstract class ExtractRemediateInfoState extends Equatable {
  /// To know if a label was printed when setting this state
  final bool labelPrinted;

  const ExtractRemediateInfoState(this.labelPrinted);
}

class LookingUpRemediateResults extends ExtractRemediateInfoState {
  const LookingUpRemediateResults() : super(false);

  @override
  List<Object> get props => [];
}

class CameraState extends ExtractRemediateInfoState {
  const CameraState() : super(false);

  @override
  List<Object> get props => [];
}

class ReviewPhotoState extends ExtractRemediateInfoState {
  final File file;

  const ReviewPhotoState(this.file) : super(false);

  @override
  List<Object> get props => [file];
}

class WaitingForScan extends ExtractRemediateInfoState {
  const WaitingForScan({
    bool labelPrinted = false,
    this.remediateResult,
  }) : super(labelPrinted);

  final LookupResult? remediateResult;

  @override
  List<Object?> get props => [labelPrinted, remediateResult];
}

class RemediateError extends ExtractRemediateInfoState {
  final String error;

  const RemediateError(this.error) : super(false);

  @override
  List<Object> get props => [error];
}

class AddressEntryState extends ExtractRemediateInfoState {

  final DeliveryInfo deliveryInfo;

  const AddressEntryState(this.deliveryInfo) : super(false);

  @override
  List<Object?> get props => [deliveryInfo];
}
