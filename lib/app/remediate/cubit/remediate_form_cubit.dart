import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sort_pro_printer_flutter/app/manage/cubit/app_setting_cubit.dart';
import 'package:sort_pro_printer_flutter/app/manage/cubit/user_setting_cubit.dart';
import 'package:sort_pro_printer_flutter/app/manage/data/models/supported_languages.dart';
import 'package:sort_pro_printer_flutter/app/manage/data/models/supported_modes.dart';
import 'package:sort_pro_printer_flutter/app/peripherals/cubit/peripherals_cubit.dart';
import 'package:sort_pro_printer_flutter/app/pin_lookup/data/models/lookup_result.dart';
import 'package:sort_pro_printer_flutter/app/remediate/models/delivery_info.dart';
import 'package:sort_pro_printer_flutter/app/scanner/data/models/barcode.dart';
import 'package:sort_pro_printer_flutter/extensions/string_nullable.dart';
import 'package:sort_pro_printer_flutter/services/data_provider/i_data_provider_service.dart';
import 'package:sort_pro_printer_flutter/services/device_info/i_device_info_service.dart';
import 'package:sort_pro_printer_flutter/services/i_lookup_service.dart';
import 'package:sort_pro_printer_flutter/services/logs/i_log_service.dart';
import 'package:sort_pro_printer_flutter/utils/date_time_formatter.dart';

part 'remediate_form_state.dart';

class RemediateFormCubit extends Cubit<RemediateFormState> {
  final ILogService _logService;
  final ILookupService _lookupService;
  final IDataProviderService dataProviderService;
  final IDeviceInfoService deviceInfoService;
  final PeripheralsCubit peripheralsCubit;
  final UserSettingCubit userSettingCubit;
  final AppSettingCubit appSettingCubit;
  final Barcode scannedBarcode;

  RemediateFormCubit(
      this._logService, this._lookupService, this.dataProviderService, this.deviceInfoService, this.peripheralsCubit, this.userSettingCubit, this.scannedBarcode, this.appSettingCubit)
      : super(const RemediateFormState());

  lookupByPinAndPostalCode(DeliveryInfo deliveryInfo, String terminalId, String userId) async {
    final pin = deliveryInfo.pin;
    final postalCode = deliveryInfo.deliveryAddress.postalCode ?? "";
    _logService.trace(
      LogLevel.verbose,
      "Looking up by pin or postal code only",
      additionalProperties: {
        "pin": pin,
        "postalCode": postalCode,
        "userId": userId,
      },
    );
    LookupResult lookupResult = await _lookupService.lookUpSortByPinAndPostalCode(pin, postalCode, terminalId);
    // only try to print the result if the printer is connected
    if(lookupResult.lookupResultType != LookupResultType.remediation && peripheralsCubit.state.printerInfo.isConnected) {
      _logService.trace(
        LogLevel.verbose,
        "Route matched by pin or postal code",
        additionalProperties: {
          "results": lookupResult.toJson(),
          "pin": deliveryInfo.pin,
          "userId": userId,
        },
      );
      sendForPrinting(deliveryInfo, userId, lookupResult);
    }
  }

  Future<void> lookupRouteInformation(DeliveryInfo deliveryInfo, String terminalId, String userId) async {
    _logService.trace(
      LogLevel.verbose,
      "Looking up route",
      additionalProperties: {
        "address": deliveryInfo.deliveryAddress.toString(),
        "pin": deliveryInfo.pin,
        "userId": userId,
      },
    );
    _logService.event("Lookup by Delivery information");
    LookupResult lookupResult = await _lookupService.lookUpSortInstructionsByDeliveryInfo(deliveryInfo, terminalId, userId);
    // Make sure all remediation results are srrR
    if(lookupResult.lookupResultType == LookupResultType.remediation) {
      lookupResult = lookupResult.copyWith(lookupResultType: LookupResultType.srrR);
    }
    sendForPrinting(deliveryInfo, userId, lookupResult);
    _logService.trace(
      LogLevel.verbose,
      "Route matched",
      additionalProperties: {
        "results": lookupResult.toJson(),
        "address": deliveryInfo.deliveryAddress.toString(),
        "pin": deliveryInfo.pin,
        "userId": userId,
      },
    );
  }

  sendForPrinting(
    DeliveryInfo deliveryInfo,
    String userId,
    LookupResult lookupResult, {
    bool fromManualResolve = false,
  }) async {
    lookupResult = await _assignExtraFieldsToLookupResult(deliveryInfo, userId, lookupResult);

    if (peripheralsCubit.state.printerInfo.isConnected) {
      final String languageCode = userSettingCubit.state.languageCode ?? SupportedLanguages.english;
      // Try to print the label
      final printed = await peripheralsCubit.printLookupLabel(lookupResult, languageCode);
      if (printed) {
        _lookupService.notifyLookupResultFound(lookupResult);
        emit(state.copyWith(lookupResult: lookupResult, labelPrinted: true));
        return;
      }
    } else {
      if (fromManualResolve) {
        continueWithoutPrinting(deliveryInfo, userId, lookupResult);
        return;
      }
    }
    if(!(fromManualResolve)) emit(state.copyWith(lookupResult: lookupResult));
  }

  continueWithoutPrinting(
    DeliveryInfo deliveryInfo,
    String userId,
    LookupResult lookupResult,
  ) async {
    lookupResult = await _assignExtraFieldsToLookupResult(deliveryInfo, userId, lookupResult);
    _lookupService.notifyLookupResultFound(lookupResult);
    emit(state.copyWith(lookupResult: lookupResult, labelPrinted: true));
  }

  // These are fields from the barcode that we need to make sure are in the lookup result for the ScanLog to receive
  Future<LookupResult> _assignExtraFieldsToLookupResult(
    DeliveryInfo deliveryInfo,
    String userId,
    LookupResult result,
  ) async {
    final packageTypeId = appSettingCubit.state.packageType.id;
    final SupportedModes mode = SupportedModes.fromName(userSettingCubit.state.mode ?? "");
    final dataDescription = await dataProviderService.getDataDescriptionForTerminal(result.terminalNumber);
    final deviceId = await deviceInfoService.getDeviceId();
    final appVersion = await deviceInfoService.getAppVersion();

    return result.copyWith(
      ssDeviceId: "${deviceId}_V$appVersion",
      barcodeType: scannedBarcode.barcodeType.id.toString(),
      fromStreetNumber: result.fromStreetNumber.isNullOrEmpty() ? deliveryInfo.deliveryAddress.streetNumber : result.fromStreetNumber,
      streetName: result.streetName.isNullOrEmpty() ? deliveryInfo.deliveryAddress.addressLine1 : result.streetName,
      unitNumber: result.unitNumber.isNullOrEmpty() ? deliveryInfo.deliveryAddress.suiteNumber : result.unitNumber,
      customerName: result.customerName.isNullOrEmpty() ? deliveryInfo.deliveryAddress.customerName : result.customerName,
      deliveryTime: result.deliveryTime.isNullOrEmpty() ? deliveryInfo.deliveryDetails.deliveryTime.id : result.deliveryTime,
      shipmentType: result.shipmentType.isNullOrEmpty() ? deliveryInfo.deliveryDetails.shipmentType : result.shipmentType,
      deliveryType: result.deliveryType.isNullOrEmpty() ? deliveryInfo.deliveryDetails.deliveryType.id.toString() : result.deliveryType,
      diversionCode: result.diversionCode.isNullOrEmpty() ? deliveryInfo.deliveryDetails.diversionCode.id.toString() : result.diversionCode,
      packageType: packageTypeId,
      alternateAddressFlag: result.alternateAddressFlag.isNullOrEmpty() ? "N" : result.alternateAddressFlag,
      handlingClassType:
          result.handlingClassType.isNullOrEmpty() ? deliveryInfo.deliveryDetails.handlingClassType : result.handlingClassType,
      scanDateTime: formatDateTimeWithTimeZone(DateTime.now()),
      printDateTime: formatDateTimeWithTimeZone(DateTime.now()),
      ssMode: mode.id,
      userLoginId: int.tryParse(userId) ?? 0,
      routePlanVersionId: dataDescription?.routePlanVersionId,
      routePlanId: dataDescription?.routePlanId,
      // this we actually need for the labels and not the scanlog
      parkingPlanMasterVersionId: dataDescription?.parkingPlanVersionId,
      provinceCode: deliveryInfo.deliveryAddress.provinceCode,
    );
  }
}
