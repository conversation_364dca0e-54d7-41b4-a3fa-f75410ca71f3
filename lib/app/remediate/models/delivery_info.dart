import 'package:equatable/equatable.dart';
import 'package:flutter/cupertino.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:sort_pro_printer_flutter/app/scanner/data/models/barcode.dart';
import 'package:sort_pro_printer_flutter/extensions/string_nullable.dart';
import 'package:sort_pro_printer_flutter/utils/barcode_parser.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

part 'delivery_info.g.dart';

enum DeliveryType {
  regular(id: 0),
  osnr(id: 1),
  hfpu(id: 2),
  asr(id: 3),
  asrHfpu(id: 4);

  const DeliveryType({
    required this.id,
  });

  final int id;
}

enum DeliveryTime {
  none(id: "00"),
  nineAM(id: "25"),
  tenThirtyAM(id: "28"),
  noon(id: "12"),
  evening(id: "29");

  String label(BuildContext context) {
    switch(id) {
      case "29":
        return AppLocalizations.of(context)!.deliveryTime_evening;
      case "28":
        return AppLocalizations.of(context)!.deliveryTime_1030AM;
      case "25":
        return AppLocalizations.of(context)!.deliveryTime_9AM;
      case "12":
        return AppLocalizations.of(context)!.deliveryTime_noon;
      case "00":
      default:
      return AppLocalizations.of(context)!.deliveryTime_none;
    }
  }

  const DeliveryTime({
    required this.id,
  });

  final String id;
}

enum DiversionCode {
  none(id: 0),
  dangerousGoods(id: 1),
  chainOfSignature(id: 2),
  heavyWeight(id: 3),
  futureUse(id: 4),
  specialHandling(id: 5),
  saturday(id: 6);

  String label(BuildContext context) {
    switch(id) {
      case 6:
        return AppLocalizations.of(context)!.diversionCode_saturday;
      case 5:
        return AppLocalizations.of(context)!.diversionCode_specialHandling;
      case 4:
        return AppLocalizations.of(context)!.diversionCode_futureUse;
      case 3:
        return AppLocalizations.of(context)!.diversionCode_heavyWeight;
      case 2:
        return AppLocalizations.of(context)!.diversionCode_chainOfSignature;
      case 1:
        return AppLocalizations.of(context)!.diversionCode_dangerousGoods;
      case 0:
      default:
        return AppLocalizations.of(context)!.diversionCode_none;
    }
  }

  const DiversionCode({
    required this.id,
  });

  final int id;
}

class DeliveryInfo extends Equatable {
  final String pin;
  final DeliveryAddress deliveryAddress;
  final DeliveryDetails deliveryDetails;

  const DeliveryInfo({
    this.pin = "",
    this.deliveryAddress = const DeliveryAddress(),
    this.deliveryDetails = const DeliveryDetails(),
  });

  bool get isEmpty => this == const DeliveryInfo();

  /// Construct a delivery info object from a Barcode
  factory DeliveryInfo.fromBarcode(Barcode barcode) {
    final barcodeData = BarcodeParser.decodeBarcode(barcode);
    return DeliveryInfo(
      pin: barcodeData.pinNumber ?? "",
      deliveryAddress: DeliveryAddress(
          postalCode: barcodeData.postalCode,
          streetNumber: barcodeData.streetNumber,
          streetName: barcodeData.addressLine1,
          city: barcodeData.city,
          customerName: barcodeData.customerName,
          suiteNumber: barcodeData.suiteNumber
      ),
      deliveryDetails: DeliveryDetails(
        deliveryTime: DeliveryTime.values.firstWhere((element) => element.id == barcodeData.deliveryTime, orElse: () => DeliveryTime.none),
        shipmentType: barcodeData.shipmentType,
        deliveryType: DeliveryType.values.firstWhere((element) => element.id.toString() == barcodeData.deliveryType, orElse: () => DeliveryType.regular),
        diversionCode: DiversionCode.values.firstWhere((element) => element.id.toString() == barcodeData.diversionCode, orElse: () => DiversionCode.none),
      ),
    );
  }

  DeliveryInfo copyWith({String? pin, DeliveryAddress? deliveryAddress, DeliveryDetails? deliveryDetails}) {
    return DeliveryInfo(
      pin: pin ?? this.pin,
      deliveryAddress: deliveryAddress ?? this.deliveryAddress,
      deliveryDetails: deliveryDetails ?? this.deliveryDetails,
    );
  }

  bool areAllRequiredElementsPresent() {
    return !(deliveryAddress.postalCode.isNullOrEmpty());
  }

  @override
  List<Object?> get props => [pin, deliveryAddress, deliveryDetails];
}

class DeliveryDetails extends Equatable {
  final DeliveryType deliveryType;
  final DeliveryTime deliveryTime;
  final String shipmentType;
  final DiversionCode diversionCode;
  final String handlingClassType;
  final int packageType;

  const DeliveryDetails({
    this.deliveryType = DeliveryType.regular,
    this.deliveryTime = DeliveryTime.none,
    this.diversionCode = DiversionCode.none,
    String? shipmentType,
    String? handlingClassType,
    int? packageType,
  })  : shipmentType = shipmentType ?? "0",
        handlingClassType = handlingClassType ?? "00",
        packageType = packageType ?? 1;

  DeliveryDetails copyWith({
    DeliveryType? deliveryType,
    String? shipmentType,
    DeliveryTime? deliveryTime,
    DiversionCode? diversionCode,
    String? handlingClassType,
    int? packageType,
  }) {
    return DeliveryDetails(
      deliveryType: deliveryType ?? this.deliveryType,
      shipmentType: shipmentType ?? this.shipmentType,
      diversionCode: diversionCode ?? this.diversionCode,
      deliveryTime: deliveryTime ?? this.deliveryTime,
      handlingClassType: handlingClassType ?? this.handlingClassType,
      packageType: packageType ?? this.packageType,
    );
  }

  @override
  List<Object?> get props => [deliveryTime, shipmentType, deliveryType, diversionCode, handlingClassType, packageType];
}

@JsonSerializable()
class DeliveryAddress extends Equatable {
  final String? customerName;
  final String? streetNumber;
  final String? city;
  final String? provinceCode;
  final String? postalCode;
  final String? streetName;
  final String? streetType;
  final String? streetDirection;
  final String? addressLine2;
  final String? suiteNumber;

  String? get addressLine1 {
    final String addr = [streetName, streetType, streetDirection].where((s) => !s.isNullOrEmpty()).join(' ');
    return addr.isEmpty ? null : addr;
  }

  const DeliveryAddress({
    this.customerName,
    this.streetNumber,
    this.city,
    this.provinceCode,
    this.postalCode,
    this.streetName,
    this.streetType,
    this.streetDirection,
    this.addressLine2,
    this.suiteNumber,
  });

  DeliveryAddress copyWith({
    String? customerName,
    String? streetNumber,
    String? city,
    String? provinceCode,
    String? postalCode,
    String? streetName,
    String? streetType,
    String? streetDirection,
    String? addressLine2,
    String? suiteNumber,
  }) {
    return DeliveryAddress(
      customerName: customerName ?? this.customerName,
      streetNumber: streetNumber ?? this.streetNumber,
      city: city ?? this.city,
      provinceCode: provinceCode ?? this.provinceCode,
      postalCode: postalCode ?? this.postalCode,
      streetName: streetName ?? this.streetName,
      streetType: streetType ?? this.streetType,
      streetDirection: streetDirection ?? this.streetDirection,
      addressLine2: addressLine2 ?? this.addressLine2,
      suiteNumber: suiteNumber ?? this.suiteNumber,
    );
  }

  DeliveryAddress toUppercase() => copyWith(
        customerName: (customerName ?? "").toUpperCase(),
        streetNumber: (streetNumber ?? "").toUpperCase(),
        suiteNumber: (suiteNumber ?? "").toUpperCase(),
        streetName: (streetName ?? "").toUpperCase(),
        streetType: (streetType ?? "").toUpperCase(),
        streetDirection: (streetDirection ?? "").toUpperCase(),
        addressLine2: (addressLine2 ?? "").toUpperCase(),
        city: (city ?? "").toUpperCase(),
        postalCode: (postalCode ?? "").toUpperCase(),
        provinceCode: (provinceCode ?? "").toUpperCase(),
      );

  @override
  String toString() {
    // Add the customer name in its own line if existing
    final customerNameBuffer = StringBuffer();
    if (!customerName.isNullOrEmpty()) {
      customerNameBuffer.write("$customerName\n");
    }
    // Format the suite-streetNumber where applicable
    final streetNumberBuffer = StringBuffer();
    if (!suiteNumber.isNullOrEmpty() && !streetNumber.isNullOrEmpty()) {
      streetNumberBuffer.write("$suiteNumber-$streetNumber");
    } else if (!streetNumber.isNullOrEmpty()) {
      streetNumberBuffer.write("$streetNumber");
    }
    final cityBuffer = StringBuffer();
    if (!city.isNullOrEmpty()) {
      cityBuffer.write("$city, ");
    }
    return '${customerNameBuffer.toString()}${streetNumberBuffer.toString()} ${addressLine1 ?? ''}  ${addressLine2 ?? ''} \n${cityBuffer.toString()}${provinceCode ?? ''} ${postalCode ?? ''}';
  }

  @override
  List<Object?> get props => [customerName, streetNumber, suiteNumber, streetName, streetType, streetDirection, addressLine2, city, postalCode, provinceCode];

  factory DeliveryAddress.fromJson(Map<String, dynamic> json) => _$DeliveryAddressFromJson(json);
  Map<String, dynamic> toJson() => _$DeliveryAddressToJson(this);

}

@JsonSerializable()
class SuggestionAddress extends DeliveryAddress {
  final int? fromStreetNumber;
  final int? toStreetNumber;

  const SuggestionAddress({required this.fromStreetNumber , required this.toStreetNumber, String? customerName, String? streetNumber, String? city, String? provinceCode, String? postalCode, String? streetName, String? streetType, String? streetDirection, String? addressLine2, String? suiteNumber}):
        super(customerName: customerName, streetNumber: streetNumber, city: city, provinceCode: provinceCode, postalCode: postalCode, streetName: streetName, streetType: streetType, streetDirection: streetDirection, addressLine2: addressLine2, suiteNumber: suiteNumber);

  //Returns StreetNumberRange, addressLine1 for the first element. City, Province and Postal Code in second element
  (String, String) separatedAddress() {
    final streetNumberRange = "${fromStreetNumber ?? ""}${toStreetNumber == null || fromStreetNumber == toStreetNumber ? "" : '-$toStreetNumber'}";

    final numRangeAddr1 = '$streetNumberRange ${(addressLine1 ?? '').trim()},';
    final cityProvPostal = '${city ?? ''}, ${provinceCode ?? ''} ${postalCode ?? ''}';

    return (numRangeAddr1, cityProvPostal);
  }

  bool isStreetNumInRange(){
    if(!streetNumber.isNullOrEmpty() && toStreetNumber != null && fromStreetNumber != null){
      final streetNum = int.parse(streetNumber!);

      return streetNum >= fromStreetNumber! &&  streetNum <= toStreetNumber!;
    }

    return false;
  }

  factory SuggestionAddress.fromJson(Map<String, dynamic> json) => _$SuggestionAddressFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$SuggestionAddressToJson(this);
}

class FullStreetDetails {
  final String? streetName;
  final String? streetType;
  final String? streetDirection;

  String get fullStreetName {
    return [streetName, streetType, streetDirection].where((s) => !s.isNullOrEmpty()).join(' ');
  }

  @override
  String toString() {
    return fullStreetName;
  }

  const FullStreetDetails(this.streetName, this.streetType, this.streetDirection);

  FullStreetDetails? findFullStreetDetails(String address, List<FullStreetDetails> addressesList){
    return addressesList.firstWhere((street) => street.fullStreetName == address);
  }
}