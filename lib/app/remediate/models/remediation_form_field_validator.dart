
import 'package:flutter/foundation.dart';

class RemFormFieldController {

  final ValueGetter<bool> _validate;
  final ValueNotifier<bool> _validNotifier = ValueNotifier(false);
  ValueListenable<bool> get validListenable => _validNotifier;

  final ValueGetter<bool> _enable;
  final ValueNotifier<bool> _enabledNotifier = ValueNotifier(false);
  ValueListenable<bool> get enableListenable => _enabledNotifier;

  RemFormFieldController({required ValueGetter<bool> validation, required ValueGetter<bool> enable}) : _validate = validation, _enable = enable;

  bool validate() {
    _validNotifier.value = _validate();
    return _validNotifier.value;
  }

  bool enable() {
    _enabledNotifier.value = _enable();
    return _enabledNotifier.value;
  }

  dispose() {
    _validNotifier.dispose();
    _enabledNotifier.dispose();
  }
}