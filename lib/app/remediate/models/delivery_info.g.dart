// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'delivery_info.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DeliveryAddress _$DeliveryAddressFromJson(Map<String, dynamic> json) =>
    DeliveryAddress(
      customerName: json['customerName'] as String?,
      streetNumber: json['streetNumber'] as String?,
      city: json['city'] as String?,
      provinceCode: json['provinceCode'] as String?,
      postalCode: json['postalCode'] as String?,
      streetName: json['streetName'] as String?,
      streetType: json['streetType'] as String?,
      streetDirection: json['streetDirection'] as String?,
      addressLine2: json['addressLine2'] as String?,
      suiteNumber: json['suiteNumber'] as String?,
    );

Map<String, dynamic> _$DeliveryAddressToJson(DeliveryAddress instance) =>
    <String, dynamic>{
      'customerName': instance.customerName,
      'streetNumber': instance.streetNumber,
      'city': instance.city,
      'provinceCode': instance.provinceCode,
      'postalCode': instance.postalCode,
      'streetName': instance.streetName,
      'streetType': instance.streetType,
      'streetDirection': instance.streetDirection,
      'addressLine2': instance.addressLine2,
      'suiteNumber': instance.suiteNumber,
    };

SuggestionAddress _$SuggestionAddressFromJson(Map<String, dynamic> json) =>
    SuggestionAddress(
      fromStreetNumber: json['fromStreetNumber'] as int?,
      toStreetNumber: json['toStreetNumber'] as int?,
      customerName: json['customerName'] as String?,
      streetNumber: json['streetNumber'] as String?,
      city: json['city'] as String?,
      provinceCode: json['provinceCode'] as String?,
      postalCode: json['postalCode'] as String?,
      streetName: json['streetName'] as String?,
      streetType: json['streetType'] as String?,
      streetDirection: json['streetDirection'] as String?,
      addressLine2: json['addressLine2'] as String?,
      suiteNumber: json['suiteNumber'] as String?,
    );

Map<String, dynamic> _$SuggestionAddressToJson(SuggestionAddress instance) =>
    <String, dynamic>{
      'customerName': instance.customerName,
      'streetNumber': instance.streetNumber,
      'city': instance.city,
      'provinceCode': instance.provinceCode,
      'postalCode': instance.postalCode,
      'streetName': instance.streetName,
      'streetType': instance.streetType,
      'streetDirection': instance.streetDirection,
      'addressLine2': instance.addressLine2,
      'suiteNumber': instance.suiteNumber,
      'fromStreetNumber': instance.fromStreetNumber,
      'toStreetNumber': instance.toStreetNumber,
    };
