import 'dart:io';

import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:sort_pro_printer_flutter/styles/scale_size.dart';

class CameraPreviewView extends StatefulWidget {
  final Builder? overlay;
  final Widget leftAction;
  final String? instructions;
  final ValueSetter<File> onPictureTaken;
  final VoidCallback? onScreenTapped;

  const CameraPreviewView(
      {super.key, this.overlay, required this.onPictureTaken, this.leftAction = const SizedBox(), this.instructions, this.onScreenTapped});

  @override
  State<CameraPreviewView> createState() => _CameraPreviewViewState();
}

class _CameraPreviewViewState extends State<CameraPreviewView> with WidgetsBindingObserver {
  CameraController? controller;
  List<CameraDescription> _cameras = [];
  CameraDescription? description;
  final ValueNotifier<bool> takingPictureNotifier = ValueNotifier(false);

  set takingPicture(bool value) => takingPictureNotifier.value = value;

  static const double _footerHeight = 90.0;

  @override
  void initState() {
    super.initState();
    // Add the WidgetsBindingObserver to the observer list
    WidgetsBinding.instance.addObserver(this);
    _initializeCameraController();
  }

  _initializeCameraController([CameraDescription? description]) async {
    try {
      if (description == null) {
        _cameras = await availableCameras();
        assert(_cameras.isNotEmpty);
        description = _cameras[0];
      }
      controller = CameraController(description, ResolutionPreset.max);
      await controller?.initialize();
      if (!mounted) {
        return;
      }
      setState(() {});
    } catch (e) {
      if (e is CameraException) {
        switch (e.code) {
          case 'CameraAccessDenied':
            // Handle access errors here.
            break;
          default:
            // Handle other errors here.
            break;
        }
      }
    }
  }

  @override
  void dispose() {
    // Remove the WidgetsBindingObserver from the observer list
    WidgetsBinding.instance.removeObserver(this);
    controller?.dispose();
    takingPictureNotifier.dispose();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    final CameraController? cameraController = controller;

    // App state changed before we got the chance to initialize.
    if (cameraController == null || !cameraController.value.isInitialized) {
      return;
    }

    if (state == AppLifecycleState.inactive) {
      cameraController.dispose();
    } else if (state == AppLifecycleState.resumed) {
      _initializeCameraController(cameraController.description);
    }
  }

  @override
  Widget build(BuildContext context) {
    if (controller == null || !controller!.value.isInitialized) {
      return Container();
    }
    return Stack(
      alignment: Alignment.bottomCenter,
      children: [
        Column(
          children: [
            Expanded(
              child: AspectRatio(
                aspectRatio: 1,
                // We then clip the size within the visible boundaries so it doesn't cover the rest of the content
                child: ClipRect(
                  // In order to get the preview to fit the whole width, we need to make its constraints fit the previewSize defined by the controller
                  child: FittedBox(
                    fit: BoxFit.cover,
                    child: SizedBox(
                      width: controller!.value.previewSize?.height,
                      height: controller!.value.previewSize?.width,
                      child: CameraPreview(
                        controller!,
                      ),
                    ),
                  ),
                ),
              ),
            ),
            Container(
              height: _footerHeight,
              decoration: const BoxDecoration(color: Color(0xff212121)),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(12.0),
                    child: Container(
                      padding: const EdgeInsets.all(4),
                      decoration: BoxDecoration(border: Border.all(color: Colors.white), borderRadius: BorderRadius.circular(100)),
                      child: ValueListenableBuilder<bool>(
                          valueListenable: takingPictureNotifier,
                          builder: (context, value, _) {
                            return value
                                ? const CircularProgressIndicator()
                                : FloatingActionButton(
                                    elevation: 0,
                                    onPressed: takePicture,
                                    backgroundColor: Colors.white,
                                  );
                          }),
                    ),
                  ),
                ],
              ),
            )
          ],
        ),
        if (widget.instructions != null)
          Padding(
            padding: const EdgeInsets.only(bottom: 24.0+_footerHeight),
            child: Opacity(
              opacity: .8,
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
                decoration: BoxDecoration(
                    color: const Color(0xFF2E2E2E),
                    border: Border.all(color: Colors.transparent),
                    borderRadius: BorderRadius.circular(100)),
                child: Text(
                  widget.instructions!,
                  style: Theme.of(context).textTheme.bodySmall,
                  textScaler: TextScaler.linear(ScaleUtil.textScaleFactor(context)),
                ),
              ),
            ),
          ),
        if (widget.overlay != null) Padding(
          padding: const EdgeInsets.only(bottom: _footerHeight),
          child: widget.overlay!,
        ),
      ],
    );
  }

  Future<void> takePicture() async {
    takingPicture = true;

    final XFile? xFile = await controller?.takePicture();
    if (xFile != null) {
      final file = File(xFile.path);
      widget.onPictureTaken(file);
    }
    takingPicture = false;
  }
}
