import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:sort_pro_printer_flutter/app/remediate/cubit/extract_remediate_info_cubit.dart';

class PhotoPreview extends StatelessWidget {
  final File file;

  const PhotoPreview({super.key, required this.file});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Expanded(
          child: AspectRatio(
            aspectRatio: 1,
            // We then clip the size within the visible boundaries so it doesn't cover the rest of the content
            child: ClipRect(
              // In order to get the preview to fit the whole width, we need to make its constraints fit the previewSize defined by the controller
              child: FittedBox(
                fit: BoxFit.cover,
                child: SizedBox(
                  width: MediaQuery.of(context).size.width,
                  height: MediaQuery.of(context).size.height,
                  child: Center(child: Image.file(file)),
                ),
              ),
            ),
          ),
        ),
        Container(
          decoration: const BoxDecoration(
            color: Color(0xff212121),
            border: Border(top: BorderSide(color: Color(0xff646464))),
          ),
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 25, horizontal: 17),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Flexible(
                  child: ElevatedButton(
                      onPressed: () => context.read<ExtractRemediateInfoCubit>().openCamera(),
                      style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xff3F3F3F),
                          foregroundColor: Colors.white,
                          side: const BorderSide(color: Color(0xff646464))),
                      child: Text(AppLocalizations.of(context)!.remediatePage_btn_retakePhoto)),
                ),
                const SizedBox(
                  width: 16.0,
                ),
                Flexible(
                  child: ElevatedButton(
                      onPressed: () => context.read<ExtractRemediateInfoCubit>().lookupRemediationFromPicture(file),
                      child: Text(AppLocalizations.of(context)!.remediate_btn_confirm)),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
