import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:sort_pro_printer_flutter/app/remediate/cubit/extract_remediate_info_cubit.dart';

class OpenCameraButton extends StatelessWidget {
  const OpenCameraButton({super.key});

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: TextButton.icon(
        icon: const Icon(Icons.camera_alt_outlined, size: 18,),
        label: Text(AppLocalizations.of(context)!.remediatePage_btn_openCamera),
        onPressed: () {
          context.read<ExtractRemediateInfoCubit>().openCamera();
        },
        style: TextButton.styleFrom(
          iconColor: Colors.white,
          foregroundColor: Colors.white,
          backgroundColor: const Color(0xFF2E2E2E),
          side: const BorderSide(color: Colors.white, width: 1),
          padding: const EdgeInsets.symmetric(vertical: 0, horizontal: 16.0),
          shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(16))),
        ),
      ),
    );
  }
}
