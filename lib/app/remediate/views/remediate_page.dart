import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sort_pro_printer_flutter/app/app_events/cubit/app_event_cubit.dart';
import 'package:sort_pro_printer_flutter/app/app_events/models/app_event.dart';
import 'package:sort_pro_printer_flutter/app/custom_app_bar.dart';
import 'package:sort_pro_printer_flutter/app/help/help_document_page.dart';
import 'package:sort_pro_printer_flutter/app/manage/cubit/app_setting_cubit.dart';
import 'package:sort_pro_printer_flutter/app/manage/cubit/user_setting_cubit.dart';
import 'package:sort_pro_printer_flutter/app/manage/data/models/supported_languages.dart';
import 'package:sort_pro_printer_flutter/app/manage/data/models/supported_modes.dart';
import 'package:sort_pro_printer_flutter/app/pin_lookup/data/models/lookup_result.dart';
import 'package:sort_pro_printer_flutter/app/pin_lookup/views/peripherals.dart';
import 'package:sort_pro_printer_flutter/app/remediate/cubit/extract_remediate_info_cubit.dart';
import 'package:sort_pro_printer_flutter/app/remediate/models/delivery_info.dart';
import 'package:sort_pro_printer_flutter/app/remediate/views/camera/photo_preview.dart';
import 'package:sort_pro_printer_flutter/app/remediate/views/open_remediate_form_button.dart';
import 'package:sort_pro_printer_flutter/app/remediate/views/remediate_form/remediate_form.dart';
import 'package:sort_pro_printer_flutter/app/remediate/views/whack_a_mole_loader.dart';
import 'package:sort_pro_printer_flutter/app/scanner/data/models/barcode.dart';
import 'package:sort_pro_printer_flutter/app/scanner/data/repositories/scanner_repository.dart';
import 'package:sort_pro_printer_flutter/app/utils/timer_mixin.dart';
import 'package:sort_pro_printer_flutter/environment/environment_config.dart';
import 'package:sort_pro_printer_flutter/services/network/i_network_service.dart';
import 'package:sort_pro_printer_flutter/services/ocr/azure_cognitive_services_ocr_service.dart';
import 'package:sort_pro_printer_flutter/services/compress_service.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:sort_pro_printer_flutter/services/logs/i_log_service.dart';

import 'camera/camera_preview.dart';
import 'waiting_for_scan_view.dart';

class RemediatePage extends StatefulWidget {
  final Barcode? scannedBarcode;

  static MaterialPageRoute<LookupResult?> route(BuildContext context, {Barcode? scannedBarcode}) {
    final logService = context.read<ILogService>();
    logService.event("Navigating to page", additionalProperties: {"page": "RemediatePage"});
    return MaterialPageRoute(
      builder: (_) => BlocProvider.value(
        value: context.read<UserSettingCubit>(),
        child: BlocProvider(
          create: (_) => ExtractRemediateInfoCubit(
            AzureCognitiveServicesOcrService(
              logService: logService,
              networkService: context.read<INetworkService>(),
              apiKey: EnvironmentConfig.azureCognitiveServicesApiKey,
              endpoint: EnvironmentConfig.azureCognitiveServicesApiUrl,
              modelId: EnvironmentConfig.azureCognitiveServicesPuroLabelModelId,
              storageAccountConnString: EnvironmentConfig.azureStorageAccountConnectionString,
              storageAccountContainerName: EnvironmentConfig.azureStorageAccountContainerName,
            )..initialize(),
            CompressService(),
            logService,
            context.read<AppSettingCubit>().state.terminal!.id,
            context.read<AppEventCubit>()
          ),
          child: RemediatePage._(scannedBarcode: scannedBarcode),
        ),
      ),
    );
  }

  const RemediatePage._({Key? key, this.scannedBarcode}) : super(key: key);

  @override
  State<RemediatePage> createState() => _RemediatePageState();
}

class _RemediatePageState extends State<RemediatePage> with TimerMixin {
  static const _timerDuration = Duration(seconds: 30);

  late Barcode scannedBarcode;

  StreamSubscription? scannerTriggeredSubscription;
  StreamSubscription<Barcode>? scannedBarcodeSubscription;

  @override
  void initState() {
    scannedBarcode = widget.scannedBarcode ?? Barcode.empty;
    scannerTriggeredSubscription = context.read<ScannerRepository>().scannerTriggered.listen((_) {
      if (context.read<ExtractRemediateInfoCubit>().state is CameraState) {
        startCameraTimer();
      }
    });
    subscribeToBarcodeScanned();
    // only run if the widget has been mounted.
    if (mounted) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        // This means we've come from both mode
        // why bother displaying the camera? let's get down to business https://www.youtube.com/watch?v=c4aZ3ROz4C0
        if (scannedBarcode.shippingBarcode) {
          onAddressExtracted(DeliveryInfo.fromBarcode(scannedBarcode));
        }
      });
    }
    super.initState();
  }

  subscribeToBarcodeScanned() {
    scannedBarcodeSubscription = context.read<ScannerRepository>().scannedBarcodes.listen((barcode) {
      // only run if the widget has been mounted.
      if (mounted) {
        try {
          scannedBarcode = barcode;
          if (scannedBarcode.shippingBarcode) {
            onAddressExtracted(DeliveryInfo.fromBarcode(barcode));
            return;
          }
          throw Exception();
        } catch (_) {
          // otherwise throw an error message to the user
          context.read<AppEventCubit>().addEvent(AppEvent.error(AppEventCode.invalidRemBarcode, autoClose: true));
        }
      }
    });
  }

  unsubscribeToBarcodeScanned() {
    scannedBarcodeSubscription?.cancel();
  }

  @override
  void dispose() {
    scannerTriggeredSubscription?.cancel();
    unsubscribeToBarcodeScanned();
    cancelTimer();
    super.dispose();
  }

  bool canPop(ExtractRemediateInfoState state) {
    if (state is WaitingForScan || state is RemediateError) return true;
    // close the camera
    if (state is CameraState) {
      context.read<ExtractRemediateInfoCubit>().closeCamera();
      cancelTimer();
    }

    // retake picture
    if (state is ReviewPhotoState) {
      context.read<ExtractRemediateInfoCubit>().openCamera();
    }

    return false;
  }

  startCameraTimer() {
    startTimer(_timerDuration, () {
      context.read<ExtractRemediateInfoCubit>().closeCamera();
    });
  }

  onAddressExtracted(DeliveryInfo deliveryInfo) async {
    final navigator = Navigator.of(context);
    final remCubit = context.read<ExtractRemediateInfoCubit>();
    final userSettingCubit = context.read<UserSettingCubit>();
    // transform the delivery address props into uppercase before getting into the remediation form
    // making sure delivery info is not empty, or else this new copy causes the delivery info to not be considered empty in the RemediationForm
    if (!deliveryInfo.isEmpty) {
      // Do not populate the customer name or unit number
      deliveryInfo = deliveryInfo.copyWith(deliveryAddress: deliveryInfo.deliveryAddress.copyWith(customerName: "", suiteNumber: ""));

      deliveryInfo = deliveryInfo.copyWith(deliveryAddress: deliveryInfo.deliveryAddress.toUppercase());
    }
    // populate the province field
    final String provinceCode = context.read<AppSettingCubit>().state.terminal!.provinceCode;
    deliveryInfo = deliveryInfo.copyWith(deliveryAddress: deliveryInfo.deliveryAddress.copyWith(provinceCode: provinceCode));
    // unsubscribe from barcode scanning while in the remediation form
    unsubscribeToBarcodeScanned();
    final LookupResult? lookupResult = await Navigator.push(context, RemediateForm.route(context, deliveryInfo: deliveryInfo, scannedBarcode: scannedBarcode));
    // subscribe back when coming back from the page
    subscribeToBarcodeScanned();
    if (lookupResult != null) {
      final languageCode = userSettingCubit.state.languageCode ?? SupportedLanguages.english;
      final printMode = userSettingCubit.state.mode!;
      remCubit.selectRemediateResult(
          lookupResult.copyWith(barcodeType: scannedBarcode.barcodeType.id.toString()), SupportedModes.fromName(printMode), languageCode);
    } else {
      final mode = userSettingCubit.state.mode;
      if(mode == SupportedModes.both.name) {
        navigator.pop();
      } else {
        remCubit.resetState();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    var mode = context.select<UserSettingCubit, String?>((value) => value.state.mode);
    return BlocConsumer<ExtractRemediateInfoCubit, ExtractRemediateInfoState>(
      listener: (context, state) async {
        final remCubit = context.read<ExtractRemediateInfoCubit>();

        // start the timer when we get into this state
        if (state is CameraState) {
          startCameraTimer();
        }

        if (mode == SupportedModes.both.name) {
          if (state is WaitingForScan && state.labelPrinted) {
            Navigator.of(context).pop(state.remediateResult);
          }
        }

        if (state.labelPrinted && mode == SupportedModes.remediate.name) {
          remCubit.resetState();
        }

        if (state is RemediateError) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                state.error,
                style: const TextStyle(color: Colors.white),
              ),
              backgroundColor: Colors.red,
            ),
          );
        }

        if (state is AddressEntryState) {
          onAddressExtracted(state.deliveryInfo);
        }
      },
      builder: (context, state) {
        return WillPopScope(
          onWillPop: () async => canPop(state),
          child: GestureDetector(
            onTap: () {
              if (state is CameraState) startCameraTimer();
            },
            child: Scaffold(
                appBar: CustomAppBar(
                  leading: mode == SupportedModes.both.name
                      ? null
                      : TextButton(
                          onPressed: () => canPop(state) ? Navigator.pop(context) : null,
                          child: const Icon(Icons.menu, color: Colors.white),
                        ),
                  title: SizedBox(
                    width: 130,
                    child: Image.asset("assets/sortpro_app_logo.png"),
                  ),
                  initialHelpDocumentPage: HelpDocumentPage.remediationMode,
                ),
                bottomNavigationBar:
                    state is LookingUpRemediateResults || state is CameraState || state is AddressEntryState || state is ReviewPhotoState
                        ? const SizedBox.shrink()
                        : Container(
                            decoration: const BoxDecoration(
                              color: Color(0xff212121),
                              border: Border(top: BorderSide(color: Color(0xff646464))),
                            ),
                            child: const Padding(
                              padding: EdgeInsets.symmetric(vertical: 25, horizontal: 17),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.spaceAround,
                                children: [
                                  OpenRemediateFormButton(),
                                  Peripherals(),
                                ],
                              ),
                            ),
                          ),
                body: Builder(
                  builder: (context) {
                    if (state is ReviewPhotoState) {
                      return PhotoPreview(file: state.file);
                    }

                    if (state is CameraState) {
                      return CameraPreviewView(
                        instructions: AppLocalizations.of(context)!.remediatePage_lbl_cameraInstructions,
                        overlay: Builder(builder: (context) {
                          return Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 12),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Image.asset("assets/camera/top-left-corner.png"),
                                    Image.asset("assets/camera/top-right-corner.png"),
                                  ],
                                ),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Image.asset("assets/camera/bottom-left-corner.png"),
                                    Image.asset("assets/camera/bottom-right-corner.png"),
                                  ],
                                ),
                              ],
                            ),
                          );
                        }),
                        onPictureTaken: (file) async {
                          context.read<ExtractRemediateInfoCubit>().reviewPicture(file);
                          cancelTimer();
                        },
                      );
                    }

                    if (state is WaitingForScan || state is RemediateError) {
                      return const WaitingForScanView();
                    }

                    if (state is LookingUpRemediateResults) {
                      return const WhackAMoleLoader();
                    }

                    return const SizedBox.shrink();
                  },
                )),
          ),
        );
      },
    );
  }
}
