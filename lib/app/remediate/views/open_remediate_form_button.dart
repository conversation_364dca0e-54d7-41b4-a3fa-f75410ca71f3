import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:sort_pro_printer_flutter/app/remediate/cubit/extract_remediate_info_cubit.dart';

class OpenRemediateFormButton extends StatelessWidget {
  const OpenRemediateFormButton({super.key});

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: TextButton.icon(
        icon: const Icon(Icons.add, size: 18,),
        label: Text(AppLocalizations.of(context)!.remediatePage_btn_manuallyOpenRemediateFormPage),
        onPressed: () {
          context.read<ExtractRemediateInfoCubit>().openAddressEntryFormEmpty();
        },
        style: TextButton.styleFrom(
          minimumSize: const Size(50, 50),
          iconColor: Colors.white,
          foregroundColor: Colors.white,
          backgroundColor: const Color(0xff464646),
          padding: const EdgeInsets.symmetric(vertical: 0, horizontal: 16.0),
          shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(100))),
        ),
      ),
    );
  }
}
