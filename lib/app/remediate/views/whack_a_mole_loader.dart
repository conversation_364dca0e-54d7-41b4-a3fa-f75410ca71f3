import 'dart:async';
import 'dart:math';

import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter/material.dart';

enum MoleStatus { away, hidden, peeking, showing, hit }

class Mole {
  final MoleStatus moleStatus;

  Mole({this.moleStatus = MoleStatus.away});
}

class WhackAMoleLoader extends StatefulWidget {
  const WhackAMoleLoader({Key? key}) : super(key: key);

  @override
  State<WhackAMoleLoader> createState() => _WhackAMoleLoaderState();
}

class _WhackAMoleLoaderState extends State<WhackAMoleLoader> {
  final List<MoleStatus> _changeableStatuses = [MoleStatus.hidden, MoleStatus.peeking, MoleStatus.showing];
  static const _moleAssetsPath = "assets/whack-a-mole";
  static const _moleAssetMap = {
    MoleStatus.away: "",
    MoleStatus.hidden: "$_moleAssetsPath/mole-hidden.png",
    MoleStatus.peeking: "$_moleAssetsPath/mole-peeking.png",
    MoleStatus.showing: "$_moleAssetsPath/mole-showing.png",
    MoleStatus.hit: "$_moleAssetsPath/mole-hit.png",
  };
  static const int _moleGridCrossAxisCount = 3; // grid size in the x axis
  static const int _moleCount = 9; // number of moles

  final List<Mole> _moles = List<Mole>.generate(_moleCount, (index) => Mole()); // The list of moles
  int _currentMoleIndex = -1; // currently displayed mole
  Timer? _timer; // periodic timer to switch mole
  int _timerInterval = 1000; // Initial interval in milliseconds (1 second)
  static const int _timerDecreaseFrequency = 5; // the frequency at which the timer's interval decreases
  static const Duration _afterHitDelay = Duration(seconds: 1); // delay time after a mole gets hit

  // keep track of the score
  int _score = 0;

  @override
  void initState() {
    _startTimer();
    super.initState();
  }

  @override
  void dispose() {
    _stopTimer();
    super.dispose();
  }

  _startTimer() {
    _onTimerTick();
    _timer = Timer.periodic(Duration(milliseconds: _timerInterval), (timer) {
      _onTimerTick();
    });
  }

  _onTimerTick() {
    setState(() {
      if (_currentMoleIndex >= 0) _moles[_currentMoleIndex] = Mole();
      _currentMoleIndex = Random().nextInt(_moles.length);
      _moles[_currentMoleIndex] = Mole(moleStatus: _changeableStatuses[Random().nextInt(_changeableStatuses.length)]);
    });
  }

  _stopTimer() {
    _timer?.cancel();
    _timer = null;
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Center(child: Container(color: Colors.black, child: Text(AppLocalizations.of(context)!.whackAMole_lbl_message))),
        Column(
          children: [
            Expanded(
              child: LayoutBuilder(builder: (context, constraints) {
                return Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: GridView.count(
                    childAspectRatio: constraints.biggest.aspectRatio * _moleGridCrossAxisCount / (_moles.length / _moleGridCrossAxisCount),
                    crossAxisCount: _moleGridCrossAxisCount,
                    children: _moles.map((e) => _moleWidget(e)).toList(),
                  ),
                );
              }),
            ),
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Center(
                child: Text(AppLocalizations.of(context)!.whackAMole_lbl_score(_score)),
              ),
            )
          ],
        ),
      ],
    );
  }

  Widget _moleWidget(Mole mole) {
    if (mole.moleStatus == MoleStatus.away) return const SizedBox.shrink();
    return GestureDetector(
      onTap: () => _onMolePressed(mole),
      child: Image.asset(
        _moleAssetMap[mole.moleStatus]!,
        width: double.infinity,
      ),
    );
  }

  _onMolePressed(Mole mole) async {
    if (mole.moleStatus != MoleStatus.showing) return;
    _stopTimer();
    final index = _moles.indexOf(mole);
    setState(() {
      _moles[index] = Mole(moleStatus: MoleStatus.hit);
      _score++;
    });
    if (_score % _timerDecreaseFrequency == 0) {
      _timerInterval = (_timerInterval * 0.8).toInt(); // Reduce interval by 20% when score is a multiple of 5
    }
    await Future.delayed(_afterHitDelay);
    _startTimer();
  }
}
