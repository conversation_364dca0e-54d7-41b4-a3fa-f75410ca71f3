import 'package:flutter/material.dart';

class RemediateFormDropdown<T> extends StatefulWidget {
  final String label;
  final TextEditingController controller;
  final DropdownController dropdownController;
  final VoidCallback onCancel;
  final FocusNode focusNode;

  /// onChange is called when the selected option is changed.;
  /// It will pass back the value and the index of the option.
  final void Function(int) onChange;

  /// list of DropdownItems
  final List<DropdownItem<T>> items;
  final DropdownStyle dropdownStyle;

  const RemediateFormDropdown({
    Key? key,
    required this.label,
    required this.controller,
    required this.dropdownController,
    required this.items,
    this.dropdownStyle = const DropdownStyle(),
    required this.onChange,
    required this.focusNode,
    required this.onCancel,
  }) : super(key: key);

  @override
  State<RemediateFormDropdown> createState() => _RemediateFormDropdownState();
}

class _RemediateFormDropdownState<T> extends State<RemediateFormDropdown<T>> with TickerProviderStateMixin {
  final LayerLink _layerLink = LayerLink();
  final ScrollController _scrollController = ScrollController(initialScrollOffset: 0);
  OverlayEntry? _overlayEntry;
  bool _isOpen = false;
  late AnimationController _animationController;
  late Animation<double> _expandAnimation;

  DropdownController get dropdownController => widget.dropdownController;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(vsync: this, duration: const Duration(milliseconds: 200));
    _expandAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );

    dropdownController.drawerOpen.addListener(onDrawerStatusChanged);
  }

  onDrawerStatusChanged() {
    _toggleDropdown(close: !dropdownController.drawerOpen.value);
  }

  @override
  Widget build(BuildContext context) {
    // link the overlay to the button
    return CompositedTransformTarget(
      link: _layerLink,
      child: TextField(
        focusNode: widget.focusNode,
        keyboardType: TextInputType.none,
        readOnly: true,
        showCursor: true,
        controller: widget.controller,
        decoration: InputDecoration(
          suffixIcon: const Icon(Icons.arrow_drop_down),
          suffixIconConstraints: const BoxConstraints(maxWidth: 18.0, maxHeight: 8.0),
          label: Text(
            widget.label,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          contentPadding: const EdgeInsets.only(top: 8.0, bottom: 6.0),
          floatingLabelBehavior: FloatingLabelBehavior.always,
        ),
      ),
    );
  }

  OverlayEntry _createOverlayEntry() {
    // find the size and position of the current widget
    RenderBox renderBox = context.findRenderObject()! as RenderBox;
    var size = renderBox.size;

    var offset = renderBox.localToGlobal(Offset.zero);
    var topOffset = offset.dy + size.height + 5;
    return OverlayEntry(
      // full screen GestureDetector to register when a
      // user has clicked away from the dropdown
      builder: (context) => GestureDetector(
        onTap: () {
          _toggleDropdown(close: true);
          widget.onCancel();
        },
        behavior: HitTestBehavior.translucent,
        // full screen container to register taps anywhere and close drop down
        child: SizedBox(
          height: MediaQuery.of(context).size.height,
          width: MediaQuery.of(context).size.width,
          child: Stack(
            children: [
              Positioned(
                left: offset.dx,
                top: topOffset,
                width: widget.dropdownStyle.width ?? size.width,
                child: CompositedTransformFollower(
                  offset: widget.dropdownStyle.offset ?? Offset(0, size.height + 5),
                  link: _layerLink,
                  showWhenUnlinked: false,
                  child: Material(
                    elevation: widget.dropdownStyle.elevation ?? 0,
                    color: widget.dropdownStyle.color,
                    shape: widget.dropdownStyle.shape,
                    child: SizeTransition(
                      axisAlignment: 1,
                      sizeFactor: _expandAnimation,
                      child: ConstrainedBox(
                        constraints: widget.dropdownStyle.constraints ??
                            BoxConstraints(
                              maxHeight: (MediaQuery.of(context).size.height - topOffset - 15).isNegative
                                  ? 100
                                  : MediaQuery.of(context).size.height - topOffset - 15,
                            ),
                        child: RawScrollbar(
                          thumbVisibility: true,
                          thumbColor: widget.dropdownStyle.scrollbarColor ?? Colors.grey,
                          controller: _scrollController,
                          child: ListView(
                            padding: widget.dropdownStyle.padding ?? EdgeInsets.zero,
                            shrinkWrap: true,
                            controller: _scrollController,
                            children: widget.items.asMap().entries.map((item) {
                              return InkWell(
                                onTap: () {
                                  widget.onChange(item.key);
                                  _toggleDropdown();
                                },
                                child: item.value,
                              );
                            }).toList(),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _toggleDropdown({bool close = false}) async {
    if (_isOpen || close) {
      await _animationController.reverse();
      _overlayEntry?.remove();
      setState(() {
        _isOpen = false;
      });
    } else {
      _overlayEntry = _createOverlayEntry();
      Overlay.of(context).insert(_overlayEntry!);
      setState(() => _isOpen = true);
      _animationController.forward();
    }
  }
}

class DropdownController {

  ValueNotifier<bool> drawerOpen = ValueNotifier(false);

  void openDropdown(){
    drawerOpen.value = true;
  }
  void closeDropdown(){
    drawerOpen.value = false;
  }

  dispose() {
    drawerOpen.dispose();
  }

}

/// DropdownItem is just a wrapper for each child in the dropdown list.\n
/// It holds the value of the item.
class DropdownItem<T> extends StatelessWidget {
  final T? value;
  final Widget child;

  const DropdownItem({Key? key, this.value, required this.child}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return child;
  }
}

class DropdownStyle {
  final double? elevation;
  final Color? color;
  final EdgeInsets? padding;
  final BoxConstraints? constraints;
  final Color? scrollbarColor;

  /// Add shape and border radius of the dropdown from here
  final ShapeBorder? shape;

  /// position of the top left of the dropdown relative to the top left of the button
  final Offset? offset;

  ///button width must be set for this to take effect
  final double? width;

  const DropdownStyle({
    this.constraints,
    this.offset,
    this.width,
    this.elevation,
    this.shape,
    this.color,
    this.padding,
    this.scrollbarColor,
  });
}
