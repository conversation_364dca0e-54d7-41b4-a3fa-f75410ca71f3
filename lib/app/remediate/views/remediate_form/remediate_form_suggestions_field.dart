import 'package:flutter/material.dart';

import '../../models/delivery_info.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class AddressSuggestionsField extends StatefulWidget {
  final List<SuggestionAddress> addressSuggestions;
  final bool showSuggestions;
  final ValueSetter<SuggestionAddress> onSelected;
  final VoidCallback hideSuggestions;

  const AddressSuggestionsField(
      {super.key,
      required this.addressSuggestions,
      required this.showSuggestions,
      required this.onSelected,
      required this.hideSuggestions});

  @override
  State<StatefulWidget> createState() => _AddressSuggestionsFieldState();
}

class _AddressSuggestionsFieldState extends State<AddressSuggestionsField> {
  @override
  Widget build(BuildContext context) {
    if (widget.showSuggestions && widget.addressSuggestions.isNotEmpty) {
      return Padding(
        padding: const EdgeInsets.only(bottom: 8),
        child: Column(children: [
          Padding(
            padding: const EdgeInsets.only(bottom: 8.0),
            child: Container(
              padding: const EdgeInsets.all(8.0),
              decoration: const BoxDecoration(
                color: Color(0xff212121),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                      AppLocalizations.of(context)!
                          .remediateSuggestionSheet_lbl_suggestionsTitle,
                      style: Theme.of(context).textTheme.titleMedium),
                ],
              ),
            ),
          ),
          Column(
              mainAxisSize: MainAxisSize.min,
              children: widget.addressSuggestions.map((address) {
                final separatedAddress = address.separatedAddress();
                return Padding(
                  padding: const EdgeInsets.only(
                      left: 8, right: 8, top: 4, bottom: 4),
                  child: ElevatedButton(
                    onPressed: () {
                      widget.onSelected(address);
                      widget.hideSuggestions();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.black,
                      foregroundColor: Colors.white,
                      side: const BorderSide(color: Colors.white),
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(separatedAddress.$1),
                        Text(separatedAddress.$2)
                      ],
                    ),
                  ),
                );
              }).toList()),
          Padding(
            padding:
                const EdgeInsets.only(left: 8, right: 8, top: 4, bottom: 8),
            child: ElevatedButton(
                onPressed: () {
                  widget.hideSuggestions();
                },
                style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xff212121),
                    foregroundColor: Colors.white,
                    side: const BorderSide(color: Color(0xff212121))),
                child: Text(
                    AppLocalizations.of(context)!
                        .remediateSuggestionSheet_lbl_ignore,
                    style:
                        const TextStyle(decoration: TextDecoration.underline))),
          ),
        ]),
      );
    } else if (widget.addressSuggestions.isNotEmpty) {
      return Padding(
        padding: const EdgeInsets.only(bottom: 24),
        child: Center(
          child: ElevatedButton(
              onPressed: () {
                widget.hideSuggestions();
              },
              style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xff212121),
                  foregroundColor: Colors.white,
                  side: const BorderSide(color: Color(0xff212121))),
              child: Text(AppLocalizations.of(context)!
                  .remediateSuggestionSheet_lbl_openSuggestions)),
        ),
      );
    }

    return const SizedBox.shrink();
  }
}
