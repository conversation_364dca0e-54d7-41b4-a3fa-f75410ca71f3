import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:sort_pro_printer_flutter/app/authenticate_employee/cubit/authentication_cubit.dart';
import 'package:sort_pro_printer_flutter/app/custom_app_bar.dart';
import 'package:sort_pro_printer_flutter/app/manage/cubit/user_setting_cubit.dart';
import 'package:sort_pro_printer_flutter/app/peripherals/cubit/peripherals_cubit.dart';
import 'package:sort_pro_printer_flutter/app/pin_lookup/data/models/lookup_result.dart';
import 'package:sort_pro_printer_flutter/app/pin_lookup/views/print_label.dart';
import 'package:sort_pro_printer_flutter/app/remediate/cubit/remediate_form_cubit.dart';
import 'package:sort_pro_printer_flutter/app/remediate/models/delivery_info.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:sort_pro_printer_flutter/app/remediate/models/remediation_form_field_validator.dart';
import 'package:sort_pro_printer_flutter/app/remediate/views/remediate_form/remediate_form_dropdown.dart';
import 'package:sort_pro_printer_flutter/app/remediate/views/remediate_form/remediate_form_field.dart';
import 'package:sort_pro_printer_flutter/app/remediate/views/remediate_form/remediate_form_row.dart';
import 'package:sort_pro_printer_flutter/app/remediate/views/remediate_form/remediate_form_suggestions_field.dart';
import 'package:sort_pro_printer_flutter/app/scanner/data/models/barcode.dart';
import 'package:sort_pro_printer_flutter/app/scanner/data/models/barcode_data.dart';
import 'package:sort_pro_printer_flutter/app/scanner/data/repositories/scanner_repository.dart';
import 'package:sort_pro_printer_flutter/app/utils/timer_mixin.dart';
import 'package:sort_pro_printer_flutter/db/ssws_database_manager.dart';
import 'package:sort_pro_printer_flutter/extensions/string_nullable.dart';
import 'package:sort_pro_printer_flutter/services/autocomplete/routeplan_autocomplete_service.dart';
import 'package:sort_pro_printer_flutter/services/data_provider/i_data_provider_service.dart';
import 'package:sort_pro_printer_flutter/services/address_suggestions/address_suggestions_service.dart';
import 'package:sort_pro_printer_flutter/services/address_suggestions/i_address_suggestions_service.dart';
import 'package:sort_pro_printer_flutter/services/device_info/i_device_info_service.dart';
import 'package:sort_pro_printer_flutter/services/i_lookup_service.dart';
import 'package:sort_pro_printer_flutter/services/autocomplete/i_autocomplete_service.dart';
import 'package:sort_pro_printer_flutter/services/logs/i_log_service.dart';
import 'package:sort_pro_printer_flutter/utils/barcode_parser.dart';
import 'package:sort_pro_printer_flutter/utils/exceptions.dart';
import 'package:sort_pro_printer_flutter/utils/pin_validator.dart';
import 'package:sort_pro_printer_flutter/utils/postal_code_validator.dart';
import 'package:sort_pro_printer_flutter/utils/remove_type_and_direction.dart';

import '../../../help/help_document_page.dart';
import '../../../manage/cubit/app_setting_cubit.dart';

class RemediateForm extends StatefulWidget {
  static MaterialPageRoute<LookupResult> route(BuildContext context,
      {Key? key, required DeliveryInfo deliveryInfo, required Barcode scannedBarcode}) {
    return MaterialPageRoute(
        fullscreenDialog: true,
        builder: (_) => MultiBlocProvider(
              providers: [
                BlocProvider.value(
                  value: context.read<UserSettingCubit>(),
                ),
                RepositoryProvider<IAutocompleteService>(
                  create: (context) => RoutePlanAutocompleteService(SswsDatabaseManager()),
                ),
                RepositoryProvider<IAddressSuggestionsService>(
                  create: (context) => AddressSuggestionsService(),
                )
              ],
              child: RemediateForm._(
                key: key,
                extractedDeliveryInfo: deliveryInfo,
                scannedBarcode: scannedBarcode,
              ),
            ));
  }

  final DeliveryInfo extractedDeliveryInfo;
  final Barcode scannedBarcode;

  const RemediateForm._({super.key, required this.extractedDeliveryInfo, required this.scannedBarcode});

  @override
  State<RemediateForm> createState() => _RemediateFormState();
}

class _RemediateFormState extends State<RemediateForm> with TimerMixin {
  static const _fieldHorizontalSeparation = 16.0;

  late RemediateFormCubit triageCubit; // handles the Lookup result state of the page
  late String terminalId;
  late String userId;

  ValueNotifier<List<String>> citiesNotifier = ValueNotifier([]);
  List<String> cities = [];

  ValueNotifier<List<FullStreetDetails>> fullStreetDetailsNotifier = ValueNotifier([]);
  List<FullStreetDetails> addressNames = [];

  late Future<List<String>> postalCodesFuture;
  List<String> postalCodes = [];
  GlobalKey postalCodeKey = GlobalKey();

  late Future<List<SuggestionAddress>> addressSuggestions;

  DeliveryInfo deliveryInfo = const DeliveryInfo();

  StreamSubscription? scannedBarcodesSubscription; // subscribe to barcode scans for grabbing the pin number
  StreamSubscription<bool>? keyboardSubscription;

  final GlobalKey firstRowKey = GlobalKey();
  final GlobalKey secondRowKey = GlobalKey();
  final GlobalKey thirdRowKey = GlobalKey();
  final GlobalKey fourthRowKey = GlobalKey();
  final GlobalKey fifthRowKey = GlobalKey();

  late Map<int, GlobalKey> rowMap;

  final GlobalKey cityDropdownKey = GlobalKey();

  /// These focus nodes are for jumping from field to field
  FocusNode? customerNameFocusNode;
  FocusNode? streetNumberFocusNode;
  FocusNode? addressLine1FocusNode;
  FocusNode? unitNumberFocusNode;
  FocusNode? cityFocusNode;
  late FocusNode deliveryTimeFocusNode;
  late FocusNode diversionCodeFocusNode;
  FocusNode? postalCodeFocusNode;
  FocusNode? pinNumberFocusNode;

  TextEditingController? addressLine1TextController;
  TextEditingController? cityTextController;
  TextEditingController? postalCodeTextController;
  TextEditingController? streetNumberTextController;

  late ScrollController scrollController;

  late TextEditingController deliveryTimeController;
  late TextEditingController diversionCodeController;
  TextEditingController? pinNumberController;

  late DropdownController deliveryTimeDropdownController; // control the overlay of the deliveryTime dropdown
  late DropdownController diversionCodeDropdownController; // control the overlay of the diversionCode dropdown

  // validator for knowing when a field is valid
  late RemFormFieldController pinFieldController;
  late RemFormFieldController postalCodeFieldController;
  late RemFormFieldController cityFieldController;
  late RemFormFieldController streetNumberFieldController;
  late RemFormFieldController streetNameFieldController;
  late RemFormFieldController unitNoFieldController;
  late RemFormFieldController customerNameFieldController;
  late List<RemFormFieldController> fieldControllers;

  int focusedRow = 0; // keep track of which row is focused. 0 means no row is focused atm

  bool showSuggestionField =
      true; // keeping track if the suggestionField shown or if it is hidden and the show suggestion button is presented

  final ValueNotifier<bool> lookingUpRoute = ValueNotifier(false);
  final ValueNotifier<bool> isHFPU = ValueNotifier(false);
  final ValueNotifier<bool> enableSearchButton = ValueNotifier(false);
  DeliveryType originalDeliveryType = DeliveryType.regular; // keep track of the extracted DType

  @override
  void initState() {
    /// We need the terminal id and the user id for the lookups
    terminalId = context.read<AppSettingCubit>().state.terminal!.id;
    userId = context.read<AuthenticationCubit>().state.user.id;

    triageCubit = RemediateFormCubit(
      context.read<ILogService>(),
      context.read<ILookupService>(),
      context.read<IDataProviderService>(),
      context.read<IDeviceInfoService>(),
      context.read<PeripheralsCubit>(),
      context.read<UserSettingCubit>(),
      widget.scannedBarcode,
      context.read<AppSettingCubit>(),
    );

    citiesNotifier.addListener(onCitiesLoaded);
    fullStreetDetailsNotifier.addListener(onAddressesLoaded);
    fetchCities();
    fetchAddressesForCity(widget.extractedDeliveryInfo.deliveryAddress.city ?? '');
    postalCodesFuture = context.read<IAutocompleteService>().getAllPostalCodesForTerminal(terminalId);
    addressSuggestions =
        context.read<IAddressSuggestionsService>().getSuggestionsForAddress(terminalId, widget.extractedDeliveryInfo.deliveryAddress);

    rowMap = {
      1: firstRowKey,
      2: secondRowKey,
      3: thirdRowKey,
      4: fourthRowKey,
      5: fifthRowKey,
    };

    scrollController = ScrollController();

    deliveryTimeFocusNode = FocusNode();
    deliveryTimeFocusNode.addListener(onDeliveryTimeFocusNodeStatusChanged);
    deliveryTimeController = TextEditingController();
    WidgetsBinding.instance.addPostFrameCallback(
        (_) => deliveryTimeController.text = widget.extractedDeliveryInfo.deliveryDetails.deliveryTime.label(context));
    deliveryTimeDropdownController = DropdownController();

    diversionCodeFocusNode = FocusNode();
    diversionCodeFocusNode.addListener(onDiversionCodeFocusNodeStatusChanged);
    diversionCodeController = TextEditingController();
    WidgetsBinding.instance.addPostFrameCallback(
        (_) => diversionCodeController.text = widget.extractedDeliveryInfo.deliveryDetails.diversionCode.label(context));
    diversionCodeDropdownController = DropdownController();

    /// Listen to barcode scans for inserting the pin into the pin field.
    scannedBarcodesSubscription = context.read<ScannerRepository>().scannedBarcodes.listen((barcode) async {
      // ignore barcodes that are not shipping labels
      if (barcode.shippingBarcode) {
        // try to get the pin from the barcode
        late final BarcodeData barcodeData;
        try {
          barcodeData = BarcodeParser.decodeBarcode(barcode);
        } on DecodeBarcodeException catch (_) {
          barcodeData = const BarcodeData();
        }
        final pin = barcodeData.pinNumber ?? "";
        final pinValid = PinValidator().validatePin(pin);
        final pinFieldFocused = pinNumberFocusNode?.hasPrimaryFocus ?? false;
        if (pinValid && pinFieldFocused) {
          pinNumberController?.text = pin;
          deliveryInfo = deliveryInfo.copyWith(pin: pin);
          lookupByPinAndPostalCode();
          checkFields();
          // small delay to allow the postalcode field to become enable so that we can change the focus to it
          await Future.delayed(const Duration(milliseconds: 100));
          changeFocus(postalCodeFocusNode);
          setFocusedRow(1);
        }
      }
    });

    /// When user comes to this form without extracting label data, focus into the first row and field.
    if (widget.extractedDeliveryInfo.isEmpty) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        pinNumberFocusNode?.requestFocus();
        setFocusedRow(1);
      });
    } else {
      deliveryInfo = widget.extractedDeliveryInfo;
      // Do a quick lookup based on the pin
      lookupByPinAndPostalCode();
      // initialize HFPU stuff
      originalDeliveryType = deliveryInfo.deliveryDetails.deliveryType;
      isHFPU.value = deliveryInfo.deliveryDetails.deliveryType == DeliveryType.hfpu;
      _initHelperDeliveryInfo();
    }

    pinFieldController = RemFormFieldController(
      validation: () => deliveryInfo.pin.isNotEmpty,
      enable: () => true,
    );
    postalCodeFieldController = RemFormFieldController(
      validation: () {
        String postalCode = (deliveryInfo.deliveryAddress.postalCode ?? "").replaceAll(" ", "");
        return PostalCodeValidator.validatePostalCode(postalCode);
      },
      enable: () => pinFieldController.validate(),
    );
    cityFieldController = RemFormFieldController(
      validation: () {
        String city = deliveryInfo.deliveryAddress.city ?? "";
        return cities.contains(city);
      },
      enable: () => pinFieldController.validate() && postalCodeFieldController.validate(),
    );
    streetNumberFieldController = RemFormFieldController(
      validation: () => !deliveryInfo.deliveryAddress.streetNumber.isNullOrEmpty(),
      enable: () => pinFieldController.validate() && postalCodeFieldController.validate() && cityFieldController.validate(),
    );
    streetNameFieldController = RemFormFieldController(
      validation: () {
        String fullStreetName = deliveryInfo.deliveryAddress.streetName ?? "";
        return addressNames.where((element) => element.streetName == fullStreetName).isNotEmpty;
      },
      enable: () =>
          pinFieldController.validate() &&
          postalCodeFieldController.validate() &&
          cityFieldController.validate() &&
          streetNumberFieldController.validate(),
    );
    unitNoFieldController = RemFormFieldController(
      validation: () => true,
      enable: () {
        return pinFieldController.validate() &&
            postalCodeFieldController.validate() &&
            cityFieldController.validate() &&
            streetNumberFieldController.validate() &&
            streetNameFieldController.validate();
      },
    );
    customerNameFieldController = RemFormFieldController(
      validation: () => true,
      enable: () {
        return pinFieldController.validate() &&
            postalCodeFieldController.validate() &&
            cityFieldController.validate() &&
            streetNumberFieldController.validate() &&
            streetNameFieldController.validate();
      },
    );
    fieldControllers = [
      pinFieldController,
      postalCodeFieldController,
      cityFieldController,
      streetNumberFieldController,
      streetNameFieldController,
      customerNameFieldController,
      unitNoFieldController
    ];

    // When the user presses the back button while focused on a field, reset the state of this page
    keyboardSubscription = KeyboardVisibilityController().onChange.listen((bool visible) {
      if (!visible && focusedRow != 0) {
        resetFocusRow();
      }
    });

    super.initState();
  }

  Future<void> fetchCities() async {
    cities = await context.read<IAutocompleteService>().getAllCitiesForTerminal(terminalId);
    citiesNotifier.value = cities;
  }

  Future<void> _initHelperDeliveryInfo() async {
    await onStreetAddressChange(deliveryInfo.deliveryAddress.streetName ?? "");
    checkFields();
  }

  onStreetAddressChange(String fullAddress) async {
    final rpmData = await context.read<IDataProviderService>().getDataDescriptionForTerminal(terminalId);
    final listOfDirections = rpmData?.streetDirections ?? const [];
    final listOfTypes = rpmData?.streetTypes ?? const [];
    deliveryInfo = RemoveTypeAndDirection.matchedAddress(
      deliveryInfo,
      fullAddress,
      addressNames,
      listOfTypes: listOfTypes,
      listOfDirections: listOfDirections,
    );
  }

  @override
  void dispose() {
    triageCubit.close();

    scrollController.dispose();

    deliveryTimeFocusNode.dispose();
    deliveryTimeFocusNode.removeListener(onDeliveryTimeFocusNodeStatusChanged);
    deliveryTimeController.dispose();
    deliveryTimeDropdownController.dispose();

    diversionCodeFocusNode.dispose();
    diversionCodeFocusNode.removeListener(onDiversionCodeFocusNodeStatusChanged);
    diversionCodeController.dispose();
    diversionCodeDropdownController.dispose();

    scannedBarcodesSubscription?.cancel();
    keyboardSubscription?.cancel();

    lookingUpRoute.dispose();
    isHFPU.dispose();
    enableSearchButton.dispose();

    for (var element in fieldControllers) {
      element.dispose();
    }

    citiesNotifier.removeListener(onCitiesLoaded);
    citiesNotifier.dispose();
    fullStreetDetailsNotifier.removeListener(onAddressesLoaded);
    fullStreetDetailsNotifier.dispose();
    super.dispose();
  }

  void checkFields() {
    // we assume the button will be enabled
    bool enable = true;
    for (var element in fieldControllers) {
      // if at least one of this comes as false, the button will be disabled
      enable = element.enable();
    }
    enableSearchButton.value = enable;
  }

  void setFocusedRow(int row) {
    setState(() => focusedRow = row);
    final targetContext = rowMap[row]?.currentContext;
    if (targetContext != null) {
      Scrollable.ensureVisible(
        targetContext,
        duration: const Duration(milliseconds: 400),
        curve: Curves.easeInOut,
      );
      showSuggestionField = false;
    }
  }

  /// Change the field that is currently focused
  void changeFocus(FocusNode? nextNode) => nextNode?.requestFocus();

  /// Remove the focus from any row
  void resetFocusRow() {
    scrollToTop();
    setState(() => focusedRow = 0);
  }

  scrollToTop() {
    scrollController.animateTo(
      0, //scroll offset to go
      duration: const Duration(milliseconds: 500), //duration of scroll
      curve: Curves.fastOutSlowIn, //scroll type
    );
  }

  /// User has confirmed the resulting label
  confirmResult(LookupResult result, {bool printLabel = true}) async {
    if (printLabel) {
      triageCubit.sendForPrinting(deliveryInfo, userId, result);
    } else {
      triageCubit.continueWithoutPrinting(deliveryInfo, userId, result);
    }
  }

  /// User has manually printed SRR or MSD
  printManualResolve(LookupResult result) async {
    triageCubit.sendForPrinting(deliveryInfo, userId, result, fromManualResolve: true);
  }

  void lookupByPinAndPostalCode() {
    triageCubit.lookupByPinAndPostalCode(deliveryInfo, terminalId, userId);
  }

  void lookupRoute() async {
    lookingUpRoute.value = true;
    await triageCubit.lookupRouteInformation(deliveryInfo, terminalId, userId);
    lookingUpRoute.value = false;
  }

  void onCitiesLoaded() async {
    checkFields();
  }

  void onAddressesLoaded() async {
    _initHelperDeliveryInfo();
  }

  void onDeliveryTimeFocusNodeStatusChanged() {
    if (deliveryTimeFocusNode.hasPrimaryFocus) {
      setFocusedRow(4);
      deliveryTimeDropdownController.openDropdown();
    } else {
      deliveryTimeDropdownController.closeDropdown();
    }
  }

  void onDiversionCodeFocusNodeStatusChanged() {
    if (diversionCodeFocusNode.hasPrimaryFocus) {
      setFocusedRow(5);
      diversionCodeDropdownController.openDropdown();
    } else {
      diversionCodeDropdownController.closeDropdown();
    }
  }

  Future<void> fetchAddressesForCity(String city) async {
    addressNames = await context.read<IAutocompleteService>().getAllAddressNamesForCity(terminalId, city);
    fullStreetDetailsNotifier.value = addressNames;
  }

  Future<void> selectSuggestion(SuggestionAddress addressSelected) async {
    String newStreetNum;

    //If street# is in range we keep it, if not we remove it. If there is no range we leave it as is
    if (addressSelected.fromStreetNumber != null && addressSelected.fromStreetNumber == addressSelected.toStreetNumber) {
      newStreetNum = addressSelected.fromStreetNumber!.toString();
    } else if (!addressSelected.isStreetNumInRange()) {
      newStreetNum = "";
    } else {
      newStreetNum = deliveryInfo.deliveryAddress.streetNumber ?? "";
    }

    deliveryInfo = deliveryInfo.copyWith(deliveryAddress: addressSelected.copyWith(streetNumber: newStreetNum));

    streetNumberTextController!.text = newStreetNum;
    addressLine1TextController!.text = addressSelected.addressLine1 ?? "";
    cityTextController!.text = addressSelected.city ?? "";
    postalCodeTextController!.text = addressSelected.postalCode ?? "";

    await fetchAddressesForCity(deliveryInfo.deliveryAddress.city!);

    final rpmData = await context.read<IDataProviderService>().getDataDescriptionForTerminal(terminalId);
    final listOfDirections = rpmData?.streetDirections ?? const [];
    final listOfTypes = rpmData?.streetTypes ?? const [];
    deliveryInfo = RemoveTypeAndDirection.matchedAddress(
      deliveryInfo,
      addressLine1TextController!.text,
      addressNames,
      listOfTypes: listOfTypes,
      listOfDirections: listOfDirections,
    );
    checkFields();
  }

  void onHideSuggestions() {
    setState(() {
      showSuggestionField = !showSuggestionField;
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<RemediateFormCubit, RemediateFormState>(
      bloc: triageCubit,
      listener: (context, state) {
        if (state.labelPrinted) {
          Navigator.of(context).pop(state.lookupResult);
        }
      },
      builder: (context, state) {
        final resultFound = state.lookupResult != null;
        return GestureDetector(
          onTap: () {},
          behavior: HitTestBehavior.translucent,
          child: Scaffold(
            appBar: CustomAppBar(
              title: Text(AppLocalizations.of(context)!.remediation_title),
              initialHelpDocumentPage: HelpDocumentPage.remEditPage,
            ),
            body: IgnorePointer(
              ignoring: resultFound,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8.0),
                child: SingleChildScrollView(
                  controller: scrollController,
                  physics: const NeverScrollableScrollPhysics(),
                  child: SizedBox(
                    height: MediaQuery.of(context).size.height,
                    child: Column(
                      children: [
                        RemediateFormRow(
                          key: firstRowKey,
                          opaque: (focusedRow != 1 && focusedRow != 0) || resultFound,
                          children: [
                            Flexible(
                              child: RemediateAddressFormField(
                                emptyErrorLength: 12,
                                fieldController: pinFieldController,
                                label: AppLocalizations.of(context)!.editAddressPage_lbl_pin,
                                initialValue: deliveryInfo.pin,
                                onRemediationFormFieldBuild: (node, controller) {
                                  pinNumberFocusNode = node;
                                  pinNumberController = controller;
                                },
                                onTap: () => setFocusedRow(1),
                                onTapOutside: () {
                                  resetFocusRow();
                                  // Do a quick lookup based on the pin and postalcode
                                  lookupByPinAndPostalCode();
                                },
                                onSubmitted: (_) {
                                  // Do a quick lookup based on the pin and postalcode
                                  lookupByPinAndPostalCode();

                                  // move focus to the next field
                                  changeFocus(postalCodeFocusNode);
                                  setFocusedRow(1);
                                },
                                onChanged: (String? value) {
                                  deliveryInfo = deliveryInfo.copyWith(pin: value);

                                  checkFields();
                                },
                              ),
                            ),
                            const SizedBox(
                              width: _fieldHorizontalSeparation,
                            ),
                            Flexible(
                              child: FutureBuilder(
                                  future: postalCodesFuture,
                                  builder: (context, snapshot) {
                                    // Explained in the cities field
                                    if (snapshot.data != postalCodes) {
                                      postalCodeKey = GlobalKey();
                                      postalCodes = snapshot.data ?? [];
                                    }
                                    return RemediateAddressFormField(
                                      key: postalCodeKey,
                                      emptyErrorLength: 6,
                                      label: AppLocalizations.of(context)!.editAddressPage_lbl_postalCode,
                                      initialValue: deliveryInfo.deliveryAddress.postalCode,
                                      onRemediationFormFieldBuild: (node, textEditingController) {
                                        postalCodeFocusNode = node;
                                        postalCodeTextController = textEditingController;
                                      },
                                      onTap: () => setFocusedRow(1),
                                      onTapOutside: () {
                                        resetFocusRow();
                                        // Do a quick lookup based on the pin and postalcode
                                        lookupByPinAndPostalCode();
                                      },
                                      onSubmitted: (_) {
                                        // Do a quick lookup based on the pin and postalcode
                                        lookupByPinAndPostalCode();

                                        // move focus to the next field
                                        changeFocus(cityFocusNode);
                                        setFocusedRow(2);
                                      },
                                      onChanged: (String? value) {
                                        deliveryInfo = deliveryInfo.copyWith(
                                            deliveryAddress: deliveryInfo.deliveryAddress.copyWith(postalCode: value));
                                        checkFields();
                                      },
                                      fieldController: postalCodeFieldController,
                                      options: postalCodes,
                                    );
                                  }),
                            ),
                          ],
                        ),
                        RemediateFormRow(
                          key: secondRowKey,
                          opaque: (focusedRow != 2 && focusedRow != 0) || resultFound,
                          children: [
                            Flexible(
                                flex: 5,
                                child: ValueListenableBuilder<List<String>>(
                                    valueListenable: citiesNotifier,
                                    builder: (context, cities, _) {
                                      return RemediateAddressFormField(
                                        emptyErrorLength: 12,
                                        label: AppLocalizations.of(context)!.editAddressPage_lbl_city,
                                        initialValue: deliveryInfo.deliveryAddress.city,
                                        onRemediationFormFieldBuild: (node, textEditingController) {
                                          cityFocusNode = node;
                                          cityTextController = textEditingController;
                                        },
                                        options: cities,
                                        onTap: () => setFocusedRow(2),
                                        onTapOutside: resetFocusRow,
                                        onSubmitted: (_) {
                                          // move focus to the next field
                                          changeFocus(streetNumberFocusNode);
                                          // set the focused row to the next one
                                          setFocusedRow(2);
                                        },
                                        onChanged: (String? value) {
                                          deliveryInfo =
                                              deliveryInfo.copyWith(deliveryAddress: deliveryInfo.deliveryAddress.copyWith(city: value));
                                          // Pull addressName suggestions for city
                                          fetchAddressesForCity(deliveryInfo.deliveryAddress.city!);

                                          checkFields();
                                        },
                                        fieldController: cityFieldController,
                                      );
                                    })),
                            const SizedBox(
                              width: _fieldHorizontalSeparation,
                            ),
                            Flexible(
                                child: RemediateAddressFormField(
                              emptyErrorLength: 3,
                              label: AppLocalizations.of(context)!.editAddressPage_lbl_streetNumber,
                              initialValue: deliveryInfo.deliveryAddress.streetNumber,
                              onRemediationFormFieldBuild: (node, textEditingController) {
                                streetNumberFocusNode = node;
                                streetNumberTextController = textEditingController;
                              },
                              onTap: () => setFocusedRow(2),
                              onTapOutside: resetFocusRow,
                              onSubmitted: (_) {
                                // move focus to the next field
                                changeFocus(addressLine1FocusNode);
                                setFocusedRow(3);
                              },
                              onChanged: (String? value) {
                                deliveryInfo =
                                    deliveryInfo.copyWith(deliveryAddress: deliveryInfo.deliveryAddress.copyWith(streetNumber: value));
                                checkFields();
                              },
                              fieldController: streetNumberFieldController,
                            )),
                          ],
                        ),
                        RemediateFormRow(
                          key: thirdRowKey,
                          opaque: (focusedRow != 3 && focusedRow != 0) || resultFound,
                          children: [
                            Flexible(
                                flex: 5,
                                child: ValueListenableBuilder<List<FullStreetDetails>>(
                                    valueListenable: fullStreetDetailsNotifier,
                                    builder: (context, addressNames, _) {
                                      return RemediateAddressFormField(
                                        emptyErrorLength: 12,
                                        label: AppLocalizations.of(context)!.editAddressPage_lbl_addressLine1,
                                        initialValue: deliveryInfo.deliveryAddress.addressLine1,
                                        onRemediationFormFieldBuild: (node, textEditingController) {
                                          addressLine1FocusNode = node;
                                          addressLine1TextController = textEditingController;
                                        },
                                        options: addressNames.map((e) => e.fullStreetName).toList(),
                                        onTap: () => setFocusedRow(3),
                                        onTapOutside: resetFocusRow,
                                        onSubmitted: (_) {
                                          // move focus to the next field
                                          changeFocus(unitNumberFocusNode);
                                          setFocusedRow(3);
                                        },
                                        onChanged: (String? value) async {
                                          await onStreetAddressChange(value ?? "");
                                          checkFields();
                                        },
                                        fieldController: streetNameFieldController,
                                      );
                                    })),
                            const SizedBox(
                              width: _fieldHorizontalSeparation,
                            ),
                            Flexible(
                                child: RemediateAddressFormField(
                              fieldController: unitNoFieldController,
                              label: AppLocalizations.of(context)!.editAddressPage_lbl_suiteNumber,
                              initialValue: deliveryInfo.deliveryAddress.suiteNumber,
                              onRemediationFormFieldBuild: (node, _) {
                                unitNumberFocusNode = node;
                              },
                              onTap: () => setFocusedRow(3),
                              onTapOutside: resetFocusRow,
                              onSubmitted: (_) {
                                // move focus to the next field
                                changeFocus(customerNameFocusNode);
                                setFocusedRow(4);
                              },
                              onChanged: (String? value) {
                                deliveryInfo =
                                    deliveryInfo.copyWith(deliveryAddress: deliveryInfo.deliveryAddress.copyWith(suiteNumber: value));
                              },
                            )),
                          ],
                        ),
                        RemediateFormRow(
                          key: fourthRowKey,
                          opaque: (focusedRow != 4 && focusedRow != 0) || resultFound,
                          children: [
                            Flexible(
                              child: RemediateAddressFormField(
                                fieldController: customerNameFieldController,
                                label: AppLocalizations.of(context)!.editAddressPage_lbl_customerName,
                                initialValue: deliveryInfo.deliveryAddress.customerName,
                                onRemediationFormFieldBuild: (node, _) {
                                  customerNameFocusNode = node;
                                },
                                onTap: () => setFocusedRow(4),
                                onTapOutside: resetFocusRow,
                                onSubmitted: (_) {
                                  customerNameFocusNode?.unfocus();
                                  resetFocusRow();
                                },
                                onChanged: (String? value) {
                                  deliveryInfo =
                                      deliveryInfo.copyWith(deliveryAddress: deliveryInfo.deliveryAddress.copyWith(customerName: value));
                                },
                              ),
                            ),
                            const SizedBox(
                              width: _fieldHorizontalSeparation,
                            ),
                            Flexible(
                              child: RemediateFormDropdown<DeliveryTime>(
                                label: AppLocalizations.of(context)!.remediateForm_lbl_deliveryTime,
                                focusNode: deliveryTimeFocusNode,
                                onChange: (int index) {
                                  final deliveryTime = DeliveryTime.values[index];
                                  deliveryTimeController.text = deliveryTime.label(context);
                                  deliveryInfo = deliveryInfo.copyWith(
                                      deliveryDetails: deliveryInfo.deliveryDetails.copyWith(deliveryTime: deliveryTime));
                                  deliveryTimeFocusNode.unfocus();
                                  resetFocusRow();
                                },
                                onCancel: () {
                                  deliveryTimeFocusNode.unfocus();
                                  resetFocusRow();
                                },
                                controller: deliveryTimeController,
                                dropdownController: deliveryTimeDropdownController,
                                dropdownStyle:
                                    DropdownStyle(elevation: 1, shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(7))),
                                items: DeliveryTime.values
                                    .map(
                                      (item) => DropdownItem<DeliveryTime>(
                                        value: item,
                                        child: Padding(
                                          padding: const EdgeInsets.all(8.0),
                                          child: Text(item.label(context)),
                                        ),
                                      ),
                                    )
                                    .toList(),
                              ),
                            ),
                          ],
                        ),
                        RemediateFormRow(
                          key: fifthRowKey,
                          opaque: (focusedRow != 5 && focusedRow != 0) || resultFound,
                          children: [
                            Flexible(
                              child: RemediateFormDropdown<DiversionCode>(
                                label: AppLocalizations.of(context)!.remediateForm_lbl_diversionCode,
                                focusNode: diversionCodeFocusNode,
                                onChange: (int index) {
                                  final diversionCode = DiversionCode.values[index];
                                  diversionCodeController.text = diversionCode.label(context);
                                  deliveryInfo = deliveryInfo.copyWith(
                                      deliveryDetails: deliveryInfo.deliveryDetails.copyWith(diversionCode: diversionCode));
                                  resetFocusRow();
                                  diversionCodeFocusNode.unfocus();
                                },
                                onCancel: () {
                                  diversionCodeFocusNode.unfocus();
                                  resetFocusRow();
                                },
                                controller: diversionCodeController,
                                dropdownController: diversionCodeDropdownController,
                                dropdownStyle:
                                    DropdownStyle(elevation: 1, shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(7))),
                                items: DiversionCode.values
                                    .map(
                                      (item) => DropdownItem<DiversionCode>(
                                        value: item,
                                        child: Padding(
                                          padding: const EdgeInsets.all(8.0),
                                          child: Text(item.label(context)),
                                        ),
                                      ),
                                    )
                                    .toList(),
                              ),
                            ),
                            const SizedBox(
                              width: _fieldHorizontalSeparation,
                            ),
                            Flexible(
                              child: InkWell(
                                onTap: () {
                                  _onHfpuChecked(!isHFPU.value);
                                },
                                child: Padding(
                                  padding: const EdgeInsets.only(top: 12.0, right: 8.0),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      ValueListenableBuilder<bool>(
                                          valueListenable: isHFPU,
                                          builder: (context, value, _) {
                                            // need a sized box to remove the default padding
                                            // https://stackoverflow.com/a/75017655/7776801
                                            return SizedBox(
                                              width: 20,
                                              height: 20,
                                              child: Checkbox(
                                                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                                                value: value,
                                                onChanged: _onHfpuChecked,
                                                checkColor: Colors.black,
                                                activeColor: Colors.white,
                                              ),
                                            );
                                          }),
                                      const SizedBox(
                                        width: 8.0,
                                      ),
                                      Text(
                                        AppLocalizations.of(context)!.deliveryType_hfpu,
                                        style: Theme.of(context).textTheme.titleMedium,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
            bottomNavigationBar: BlocBuilder<RemediateFormCubit, RemediateFormState>(
                bloc: triageCubit,
                builder: (context, state) {
                  if (state.labelPrinted) return const SizedBox.shrink();

                  final resultFound = state.lookupResult != null;

                  return Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      FutureBuilder(
                          future: addressSuggestions,
                          builder: (context, snapshot) {
                            final suggestions = snapshot.data ?? [];
                            return !resultFound
                                ? AddressSuggestionsField(
                                    addressSuggestions: suggestions,
                                    showSuggestions: showSuggestionField,
                                    onSelected: selectSuggestion,
                                    hideSuggestions: onHideSuggestions)
                                : const SizedBox.shrink();
                          }),
                      ValueListenableBuilder<bool>(
                        valueListenable: lookingUpRoute,
                        builder: (context, isSearching, _) {
                          return Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              // Display this column only when searching or when a match was found
                              if (isSearching || resultFound)
                                Column(
                                  children: [
                                    Container(
                                      padding: const EdgeInsets.all(16.0),
                                      decoration: const BoxDecoration(
                                        color: Color(0xff212121),
                                      ),
                                      child: Row(
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        children: [
                                          Text(
                                              isSearching
                                                  ? AppLocalizations.of(context)!.remediationForm_lbl_lookingUpRoute
                                                  : AppLocalizations.of(context)!.remediate_lbl_remediateResultsFound,
                                              style: Theme.of(context).textTheme.titleMedium),
                                        ],
                                      ),
                                    ),
                                    isSearching
                                        ? const SizedBox(
                                            height: PrintLabel.labelHeight,
                                            child: Row(
                                              mainAxisAlignment: MainAxisAlignment.center,
                                              children: [CircularProgressIndicator()],
                                            ),
                                          )
                                        : Padding(
                                            padding: const EdgeInsets.all(16.0),
                                            child: PrintLabel(
                                              lookupResult: state.lookupResult!,
                                              isPastResult: false,
                                            ),
                                          ),
                                  ],
                                ),
                              Container(
                                decoration: const BoxDecoration(
                                  color: Color(0xff212121),
                                  border: Border(top: BorderSide(color: Color(0xff646464))),
                                ),
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(vertical: 25, horizontal: 17),
                                  child: resultFound
                                      ? BlocBuilder<PeripheralsCubit, PeripheralsState>(builder: (context, periState) {
                                          final printerConnected = periState.printerInfo.isConnected;
                                          return ElevatedButton(
                                            onPressed: () async => confirmResult(state.lookupResult!, printLabel: printerConnected),
                                            child: !printerConnected
                                                ? Text(AppLocalizations.of(context)!.remediate_btn_confirm)
                                                : Text(AppLocalizations.of(context)!.remediateLabelSheet_lbl_ignore),
                                          );
                                        })
                                      : Column(
                                          children: [
                                            ValueListenableBuilder<bool>(
                                                valueListenable: enableSearchButton,
                                                builder: (context, enabled, _) {
                                                  return ElevatedButton(
                                                      onPressed: (!enabled || isSearching) ? null : () => lookupRoute(),
                                                      child: Text(AppLocalizations.of(context)!.remediate_btn_lookup));
                                                }),
                                            const SizedBox(
                                              height: 16.0,
                                            ),
                                            Row(
                                              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                              children: [
                                                Flexible(
                                                  child: ElevatedButton(
                                                    onPressed: isSearching
                                                        ? null
                                                        : () async {
                                                            final terminalId = context.read<AppSettingCubit>().state.terminal!.id;

                                                            final result = LookupResult(
                                                              pin: pinNumberController?.text ?? "",
                                                              terminalNumber: terminalId,
                                                              // when route plan record is not found after the complete Remediation and SRR is selected, it should print ‘SRR/SRE – R’
                                                              lookupResultType: LookupResultType.srrR,
                                                              lookupResolvedBy: LookupResolvedBy.localRemediation,
                                                            );
                                                            printManualResolve(result);
                                                          },
                                                    style: ElevatedButton.styleFrom(
                                                        backgroundColor: const Color(0xff464646), foregroundColor: Colors.white),
                                                    child: Text(AppLocalizations.of(context)!.remediate_btn_srr),
                                                  ),
                                                ),
                                                const SizedBox(
                                                  width: 16.0,
                                                ),
                                                Flexible(
                                                  child: ElevatedButton(
                                                      onPressed: isSearching
                                                          ? null
                                                          : () {
                                                              final terminalId = context.read<AppSettingCubit>().state.terminal!.id;

                                                              final result = LookupResult(
                                                                pin: pinNumberController?.text ?? "",
                                                                terminalNumber: terminalId,
                                                                lookupResultType: LookupResultType.misdirect,
                                                                lookupResolvedBy: LookupResolvedBy.localRemediation,
                                                              );
                                                              printManualResolve(result);
                                                            },
                                                      style: ElevatedButton.styleFrom(
                                                          backgroundColor: const Color(0xff464646), foregroundColor: Colors.white),
                                                      child: Text(AppLocalizations.of(context)!.remediate_btn_misdirect)),
                                                ),
                                              ],
                                            ),
                                          ],
                                        ),
                                ),
                              ),
                            ],
                          );
                        },
                      ),
                    ],
                  );
                }),
          ),
        );
      },
    );
  }

  void _onHfpuChecked(bool? value) {
    isHFPU.value = value ?? false;
    DeliveryType deliveryType = originalDeliveryType;
    if (isHFPU.value) {
      deliveryType = DeliveryType.hfpu;
    } else {
      // if the checkbox is unchecked, and the original delivery type was HFPU,
      // we don't have a choice but to make it regular
      if (originalDeliveryType == DeliveryType.hfpu) {
        deliveryType = DeliveryType.regular;
      }
    }
    // assign the delivery type
    deliveryInfo = deliveryInfo.copyWith(deliveryDetails: deliveryInfo.deliveryDetails.copyWith(deliveryType: deliveryType));
  }
}
