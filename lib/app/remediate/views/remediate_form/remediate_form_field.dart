import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sort_pro_printer_flutter/app/remediate/models/remediation_form_field_validator.dart';

const int fuzzyMatchDistance = 2;

class RemediateAddressFormField extends StatefulWidget {
  final String label; // The label to display
  final String? initialValue; // The label to display
  final VoidCallback onTapOutside; // called when the user taps outside the field, while in focus
  final VoidCallback onTap; // called when the field gets tapped
  final ValueSetter<String?>? onSubmitted; // called after user submits the field (presses enter on the keyboard)
  final ValueSetter<String?>? onChanged; // called after text in the field changes
  final List<String> options; // list of options to suggest to the user
  final ValueSetter<String?>? onOptionSelected; // called when a suggestion was selected
  final OnRemediationFormFieldBuild
      onRemediationFormFieldBuild; // callback when the widget gets built to retrieve FocusNode and TextEditingController
  final int
      emptyErrorLength; // This represents the length of the string that will be used to be able to recreate a red square for empty fields
  final bool allCaps;
  final bool autoSelectAll;
  final RemFormFieldController fieldController;

  const RemediateAddressFormField({
    super.key,
    required this.label,
    required this.onTapOutside,
    required this.onTap,
    required this.onRemediationFormFieldBuild,
    required this.fieldController,
    this.onSubmitted,
    this.onChanged,
    this.options = const [],
    this.onOptionSelected,
    this.initialValue,
    this.emptyErrorLength = 0,
    this.allCaps = true,
    this.autoSelectAll = true,
  });

  @override
  State<RemediateAddressFormField> createState() => _RemediateAddressFormFieldState();
}

class _RemediateAddressFormFieldState extends State<RemediateAddressFormField> {
  bool initialized = false;
  TextEditingController? textEditingController;
  FocusNode? node;

  RemFormFieldController get fieldController => widget.fieldController;

  String _placeholder = "";

  // create a placeholder string that will be used to surround with red border when the field is empty
  String get placeholder {
    if (widget.emptyErrorLength == 0 || _placeholder.isNotEmpty) return _placeholder;

    final placeholderBuffer = StringBuffer();
    for (int i = 0; i < widget.emptyErrorLength; i++) {
      placeholderBuffer.write("0");
    }
    _placeholder = placeholderBuffer.toString();
    return _placeholder;
  }

  bool get textIsPlaceholder => placeholder.isNotEmpty && textEditingController?.text == placeholder;

  _clearPlaceholder() {
    if (textEditingController?.text != placeholder) return;
    textEditingController?.clear();
  }

  _resetPlaceholder() {
    if (textEditingController?.text.isNotEmpty ?? false) return;
    textEditingController?.text = placeholder;
  }

  _onFocusChanged() {
    if (node?.hasPrimaryFocus ?? false) {
      _clearPlaceholder();
    } else {
      _resetPlaceholder();
      fieldController.validate();
    }
    // force a change of state on this widget
    setState(() {});
  }

  @override
  void initState() {
    // Try to enable the field as soon as we first build the widget
    WidgetsBinding.instance.addPostFrameCallback((_) {
      fieldController.enable();
    });
    super.initState();
  }

  @override
  void dispose() {
    node?.removeListener(_onFocusChanged);
    textEditingController?.removeListener(_onFocusChanged);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Autocomplete<String>(
      optionsMaxHeight: 150,
      optionsBuilder: (TextEditingValue value) {
        var startingWithOptions = widget.options.where((element) => element.startsWith(value.text)).toList()..sort();

        if (value.text == '') {
          return const Iterable<String>.empty();
        } else if (startingWithOptions.isNotEmpty) {
          return startingWithOptions;
        }
        return widget.options.where((String option) {
          return fuzzyMatch(value.text, option);
        });
      },
      onSelected: (String selectedItem) async {
        widget.onChanged?.call(selectedItem);
        // onChange enables valid fields, and onSubmitted moves the focus to those fields
        // There could be a chance that the field hasn't been enabled by the time we try to switch focus
        // so we delay here just enough for this race condition to be rigged and always be on our favor without affecting UX
        await Future.delayed(const Duration(milliseconds: 100));
        widget.onSubmitted?.call(selectedItem);
      },
      fieldViewBuilder: (context, textEditingController, node, __) {
        // so this doesn't get re-assigned with rebuilds of the widget
        if (!initialized) {
          // assign either:
          // - the initialValue
          // - the initialValue in caps
          // - the placeholder
          textEditingController.text = (widget.initialValue != null && widget.initialValue!.isNotEmpty
              ? widget.allCaps
                  ? widget.initialValue!.toUpperCase()
                  : widget.initialValue!
              : placeholder);
          widget.onRemediationFormFieldBuild(node, textEditingController);
          this.node = node;
          this.textEditingController = textEditingController;
          node.addListener(_onFocusChanged);
          textEditingController.addListener(_onFocusChanged);
          initialized = true;
        }
        return ValueListenableBuilder<bool>(
            valueListenable: fieldController.validListenable,
            builder: (context, valid, _) {
              return ValueListenableBuilder<bool>(
                  valueListenable: fieldController.enableListenable,
                  builder: (context, enabled, _) {
                    return Opacity(
                      opacity: enabled ? 1 : .5,
                      child: TextField(
                        enabled: enabled,
                        focusNode: node,
                        onTapOutside: (_) {
                          if (node.hasPrimaryFocus) {
                            node.unfocus();
                            widget.onTapOutside();
                            _resetPlaceholder();
                          }
                        },
                        onTap: () {
                          widget.onTap();
                          if (!textIsPlaceholder && widget.autoSelectAll && !node.hasFocus) {
                            // Auto-select all the text on tap
                            textEditingController.selection =
                                TextSelection(baseOffset: 0, extentOffset: textEditingController.text.length);
                          }
                          _clearPlaceholder();
                        },
                        style: TextStyle(
                          height: 1.5,
                          color: textIsPlaceholder ? Colors.black : null,
                          background: Paint()
                            ..style = PaintingStyle.stroke
                            ..strokeWidth = 1
                            ..color = (textIsPlaceholder && enabled) || (!valid && enabled) ? Colors.red : Colors.transparent,
                        ),
                        onSubmitted: widget.onSubmitted,
                        onChanged: widget.onChanged,
                        controller: textEditingController,
                        keyboardType: TextInputType.visiblePassword,
                        inputFormatters: widget.allCaps
                            ? [
                                TextInputFormatter.withFunction((oldValue, newValue) {
                                  return newValue.copyWith(text: newValue.text.toUpperCase());
                                })
                              ]
                            : null,
                        decoration: InputDecoration(
                            label: Text(
                              widget.label,
                              style: Theme.of(context).textTheme.bodyMedium,
                            ),
                            contentPadding: const EdgeInsets.only(top: 8.0, bottom: 4.0),
                            floatingLabelBehavior: FloatingLabelBehavior.always),
                      ),
                    );
                  });
            });
      },
    );
  }
}

typedef OnRemediationFormFieldBuild = void Function(FocusNode focusNode, TextEditingController controller);

bool fuzzyMatch(String source, String target) {
  source = source.toUpperCase();
  target = target.toUpperCase();

  if (target.contains(source)) {
    return true;
  }
  return damerauLevenshteinDistance(source, target) <= fuzzyMatchDistance;
}

int damerauLevenshteinDistance(String source, String target) {
  if (source.isEmpty) return target.length;
  if (target.isEmpty) return source.length;

  var sourceLength = source.length;
  var targetLength = target.length;

  var matrix = List.generate(sourceLength + 1, (_) => List<int>.filled(targetLength + 1, 0));

  for (var i = 0; i <= sourceLength; i++) {
    matrix[i][0] = i;
  }

  for (var j = 0; j <= targetLength; j++) {
    matrix[0][j] = j;
  }

  for (var i = 1; i <= sourceLength; i++) {
    for (var j = 1; j <= targetLength; j++) {
      var cost = (source[i - 1] == target[j - 1]) ? 0 : 1;

      matrix[i][j] = [
        matrix[i - 1][j] + 1, // deletion
        matrix[i][j - 1] + 1, // insertion
        matrix[i - 1][j - 1] + cost, // substitution
      ].reduce((value, element) => value > element ? element : value);

      if (i > 1 && j > 1 && source[i - 1] == target[j - 2] && source[i - 2] == target[j - 1]) {
        matrix[i][j] = min(matrix[i][j], matrix[i - 2][j - 2] + cost); // transposition
      }
    }
  }

  return matrix[sourceLength][targetLength];
}
