import 'package:flutter/material.dart';

class RemediateFormRow extends StatelessWidget {
  final List<Widget> children;
  final bool opaque;

  const RemediateFormRow({super.key, required this.children, this.opaque = false});

  @override
  Widget build(BuildContext context) {
    return Opacity(
      opacity: opaque ? .5 : 1,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: children,
      ),
    );
  }
}
