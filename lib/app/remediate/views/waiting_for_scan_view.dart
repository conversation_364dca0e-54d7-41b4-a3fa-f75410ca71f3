import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:sort_pro_printer_flutter/app/remediate/views/camera/open_camera_button.dart';
import 'package:sort_pro_printer_flutter/styles/scale_size.dart';

class WaitingForScanView extends StatelessWidget {
  const WaitingForScanView({super.key});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Center(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                AppLocalizations.of(context)!.common_lbl_ready,
                style: Theme.of(context)
                    .textTheme
                    .headlineLarge!
                    .copyWith(color: const Color(0xFF32D74B), fontWeight: FontWeight.w600),
                textAlign: TextAlign.center,
                textScaler: TextScaler.linear(ScaleUtil.textScaleFactor(context)),
              ),
              const SizedBox(
                height: 4.0,
              ),
              const SizedBox(height: 22),
              Text(
                AppLocalizations.of(context)!.remediatePage_lbl_readyToScanSubTitle,
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.titleLarge!.copyWith(fontWeight: FontWeight.w700),
                textScaler: TextScaler.linear(ScaleUtil.textScaleFactor(context)),
              ),
              Text(
                AppLocalizations.of(context)!.remediatePage_lbl_readyToScanSubTitleOptional,
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.titleLarge!.copyWith(color: const Color(0xff898A8D)),
                textScaler: TextScaler.linear(ScaleUtil.textScaleFactor(context)),
              ),
            ],
          ),
        ),
        const Align(
          alignment: Alignment.bottomCenter,
          child: Padding(
            padding: EdgeInsets.only(bottom: 16.0),
            child: OpenCameraButton(),
          ),
        )
      ],
    );
  }
}
