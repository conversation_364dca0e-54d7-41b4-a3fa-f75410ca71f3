class XmlParser {
  static String sanitizeXMLString(String input) {
    // Replace control characters (<PERSON>Y<PERSON> and others) with a placeholder '?'.
    String sanitizedInput = input.replaceAll(RegExp('[\x00-\x1F]'), '?');
    // remove any new line characters that could potentially be present
    sanitizedInput = removeNewlines(sanitizedInput);
    return sanitizedInput;
  }

  static String removeNewlines(String input) {
    return input.replaceAll('\r', '').replaceAll('\n', '');
  }
}