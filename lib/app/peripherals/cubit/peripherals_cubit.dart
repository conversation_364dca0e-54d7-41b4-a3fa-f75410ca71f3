import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:sort_pro_printer_flutter/app/app_events/cubit/app_event_cubit.dart';
import 'package:sort_pro_printer_flutter/app/app_events/models/app_event.dart';
import 'package:sort_pro_printer_flutter/app/manage/cubit/app_setting_cubit.dart';
import 'package:sort_pro_printer_flutter/app/peripherals/peripherals_repository.dart';
import 'package:sort_pro_printer_flutter/app/pin_lookup/data/models/lookup_result.dart';
import 'package:sort_pro_printer_flutter/extensions/string_nullable.dart';
import 'package:sort_pro_printer_flutter/services/data_provider/i_data_provider_service.dart';
import 'package:sort_pro_printer_flutter/services/logs/i_log_service.dart';
import 'package:sort_pro_printer_flutter/services/printer/printer_exception.dart';
import 'package:sort_pro_printer_flutter/services/printer/printer_info.dart';
import 'package:sort_pro_printer_flutter/services/ring_scanner/i_ringer_service.dart';

part 'peripherals_state.dart';

class PeripheralsCubit extends Cubit<PeripheralsState> {
  final PeripheralsRepository _peripheralsRepository;
  final AppEventCubit _appEventCubit;
  final AppSettingCubit _appSettingsCubit;
  final IDataProviderService _dataProviderService;
  final ILogService _logService;

  StreamSubscription<RingScannerStatus>? ringScannerStatusStreamSubscription;
  StreamSubscription<PrinterInfo>? printerInfoStreamSubscription;

  PeripheralsCubit(this._peripheralsRepository, this._appEventCubit, this._appSettingsCubit, this._dataProviderService, this._logService)
      : super(const PeripheralsState());

  initialize() async {
    refreshPeripherals();

    // Change the status whenever we get a new status in
    ringScannerStatusStreamSubscription = _peripheralsRepository.ringScannerStatusStream.listen((status) {
      _logService.event("Finger Scanner Status Changed", additionalProperties: {"status": status.name});
      emit(state.copyWith(ringScannerStatus: status));
    });

    printerInfoStreamSubscription = _peripheralsRepository.printerInfoStream.listen((printerInfo) {
      if (printerInfo != state.printerInfo) {
        _logService
            .event("Printer Status Changed", additionalProperties: {"status": !printerInfo.isConnected ? "Disconnected" : "Connected"});
        emit(state.copyWith(printerInfo: printerInfo));

        if (!printerInfo.isConnected) {
          _appEventCubit.addEvent(AppEvent.error(AppEventCode.printerDisconnected));
          return;
        }

        if (printerInfo.isReadyToPrint) {
          _appEventCubit.addEvent(AppEvent.success(AppEventCode.printerReady));
          return;
        }

        if (printerInfo.isHeadOpen) {
          _appEventCubit.addEvent(AppEvent.warning(AppEventCode.printerHeadOpen));
        }

        if (printerInfo.isPaperOut) {
          _appEventCubit.addEvent(AppEvent.warning(AppEventCode.printerPaperOut));
        }
      }
    });
  }

  refreshPeripherals() async {
    final scannerStatus = await _peripheralsRepository.getRingScannerStatus();
    final macAddress = await _peripheralsRepository.getPairedPrinterMacAddress();
    final isPrinterPaired = macAddress != null;
    emit(state.copyWith(ringScannerStatus: scannerStatus, printerPaired: isPrinterPaired));
    // try and enable the printer only when the printer is paired, and not already connected or in the process of connecting
    if(isPrinterPaired && !state.printerInfo.isConnected && !state.connectingPrinter) {
      enablePrinter(macAddress: macAddress);
    }
  }

  enablePrinter({String? macAddress}) async {
    macAddress = macAddress ?? await _peripheralsRepository.getPairedPrinterMacAddress();
    if(macAddress == null) {
      // todo throw an error here
      return;
    }
    _logService.event("Connecting to printer start", additionalProperties: {"mac": macAddress});
    if (state.printerInfo.isConnected) {
      _logService.trace(LogLevel.information, "Printer already connected", additionalProperties: {"mac": macAddress});
      return;
    }
    String? savedAddress = await _peripheralsRepository.getPrinterMacAddress();
    if (savedAddress != macAddress) {
      await _peripheralsRepository.savePrinterMacAddress(macAddress);
    }

    emit(state.copyWith(connectingPrinter: true));
    try {
      _logService.trace(LogLevel.information, "Connecting to printer...", additionalProperties: {"mac": macAddress});
      await _peripheralsRepository.connectPrinter(macAddress);
      _logService.trace(LogLevel.information, "Printer connected", additionalProperties: {"mac": macAddress});
      emit(state.copyWith(connectingPrinter: false));
    } on PrinterException catch (_) {
      _logService.trace(LogLevel.information, "Printer failed to connect", additionalProperties: {"mac": macAddress});
      _appEventCubit.addEvent(AppEvent.error(AppEventCode.printerConnectionError));
      emit(state.copyWith(connectingPrinter: false, printerInfo: const PrinterInfo()));
    } finally {
      _logService.event("Connecting to printer end", additionalProperties: {"mac": macAddress});
    }
  }

  Future<bool> printLookupLabel(LookupResult? result, String languageCode) async {
    try {
      await _peripheralsRepository.printLookupLabel(result, languageCode);
      return true;
    } on PrinterException catch (e) {
      AppEventCode appEventCode = AppEventCode.printerConnectionError;
      if (e.errorCode == PrinterException.headOpenErrorCode) {
        appEventCode = AppEventCode.printerHeadOpen;
      } else if (e.errorCode == PrinterException.paperOutErrorCode) {
        appEventCode = AppEventCode.printerPaperOut;
      }
      _appEventCubit.addEvent(AppEvent.error(appEventCode));
      return false;
    }
  }

  // Manually prints a remediation label
  printRemLabel() async {
    final terminal = _appSettingsCubit.state.terminal?.id ?? "unavailable";
    final dataDescription = await _dataProviderService.getDataDescriptionForTerminal(terminal);
    // remediate labels dont care about language codes, so we send empty
    return printLookupLabel(
        LookupResult.emptyRemResult(terminalNumber: terminal, routePlanId: dataDescription?.routePlanId ?? "unavailable"), "");
  }

  disconnectPrinter() async {
    _peripheralsRepository.disconnectPrinter();
    emit(state.copyWith(printerInfo: const PrinterInfo(isConnected: false)));
  }

  removePrinterMacAddress() async {
    await _peripheralsRepository.removePrinterMacAddress();
  }

  @override
  Future<void> close() async {
    ringScannerStatusStreamSubscription?.cancel();
    printerInfoStreamSubscription?.cancel();
    _peripheralsRepository.dispose();
    super.close();
  }
}
