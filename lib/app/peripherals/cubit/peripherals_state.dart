part of 'peripherals_cubit.dart';

class PeripheralsState extends Equatable {
  final bool connectingPrinter;
  final PrinterInfo printerInfo;
  final bool printerPaired;
  final RingScannerStatus ringScannerStatus;

  const PeripheralsState({
    this.printerInfo = const PrinterInfo(),
    this.printerPaired = false,
    this.ringScannerStatus = RingScannerStatus.disconnected,
    this.connectingPrinter = false,
  });

  PeripheralsState copyWith({
    PrinterInfo? printerInfo,
    bool? printerPaired,
    RingScannerStatus? ringScannerStatus,
    bool? connectingPrinter,
  }) =>
      PeripheralsState(
        printerInfo: printerInfo ?? this.printerInfo,
        printerPaired: printerPaired ?? this.printerPaired,
        connectingPrinter: connectingPrinter ?? this.connectingPrinter,
        ringScannerStatus: ringScannerStatus ?? this.ringScannerStatus,
      );

  @override
  List<Object?> get props => [
        printerInfo,
        printerPaired,
        connectingPrinter,
        ringScannerStatus,
      ];
}
