/// This page has been removed from the app but we'll keep it for the records in case we bring it back
// import 'dart:async';
//
// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:flutter_gen/gen_l10n/app_localizations.dart';
// import 'package:sort_pro_printer_flutter/app/custom_app_bar.dart';
// import 'package:sort_pro_printer_flutter/app/help/help_document_page.dart';
// import 'package:sort_pro_printer_flutter/app/peripherals/cubit/peripherals_cubit.dart';
// import 'package:sort_pro_printer_flutter/app/peripherals/peripheral_status.dart';
// import 'package:sort_pro_printer_flutter/app/peripherals/views/connection_status_chip.dart';
// import 'package:sort_pro_printer_flutter/services/logs/i_log_service.dart';
// import 'package:sort_pro_printer_flutter/services/nfc_service.dart';
// import 'package:sort_pro_printer_flutter/services/printer/zebra_printer_info_channel.dart';
// import 'package:sort_pro_printer_flutter/styles/scale_size.dart';
// import 'package:video_player/video_player.dart';
// import 'package:chewie/chewie.dart';
//
// class ConnectPrinterPage extends StatefulWidget {
//   const ConnectPrinterPage._({Key? key}) : super(key: key);
//
//   static MaterialPageRoute route(BuildContext context, {Key? key}) {
//     context.read<ILogService>().event("Navigating to page", additionalProperties: {"page": "ConnectPrinterPage"});
//     return MaterialPageRoute(
//       fullscreenDialog: true,
//       builder: (_) => ConnectPrinterPage._(
//         key: key,
//       ),
//     );
//   }
//
//   @override
//   State<ConnectPrinterPage> createState() => _ConnectPrinterPageState();
// }
//
// class _ConnectPrinterPageState extends State<ConnectPrinterPage> {
//   static const _pairPrinterMp4Path = "assets/peripherals/pair-printer.mp4";
//   // chewie offers basic controls such as pause/play/scroll/fullscreen whereas the base video controller doesnt
//   late ChewieController chewieController;
//
//   StreamSubscription<String>? printerInfoSubscription;
//
//   final ZebraPrinterInfoChannel zebraPrinterInfoChannel = ZebraPrinterInfoChannel();
//
//   @override
//   void initState() {
//     printerInfoSubscription = zebraPrinterInfoChannel.printerMacAddress.listen((macAddress) {
//       context.read<ILogService>().event("Printer MAC Address received", additionalProperties: {"mac": macAddress});
//       context.read<PeripheralsCubit>().enablePrinter(macAddress: macAddress);
//     });
//
//     /// Enable foreground nfc so that we can get the NFC tag from the printer directly onto our Android code
//     NfcService.enableForegroundNfc();
//     super.initState();
//     chewieController = ChewieController(
//       videoPlayerController: VideoPlayerController.asset(_pairPrinterMp4Path),
//       aspectRatio: 2 / 3,
//       autoInitialize: true,
//       autoPlay: true,
//       looping: true,
//       draggableProgressBar: true,
//       errorBuilder: (context, errorMessage) {
//         return Center(
//           child: Text(
//             errorMessage,
//             style: const TextStyle(color: Colors.white),
//           ),
//         );
//       },
//     );
//   }
//
//   @override
//   void dispose() {
//     /// Disable foreground nfc again
//     NfcService.disableForegroundNfc();
//
//     printerInfoSubscription?.cancel();
//     zebraPrinterInfoChannel.dispose();
//     chewieController.dispose();
//     super.dispose();
//   }
//
//   Widget _printerConnectedView() {
//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.center,
//       children: [
//         Expanded(
//             child: Column(
//           mainAxisAlignment: MainAxisAlignment.center,
//           crossAxisAlignment: CrossAxisAlignment.center,
//           children: [
//             Image.asset("assets/peripherals/printer_connected.png"),
//             const SizedBox(height: 20),
//             Text(
//               AppLocalizations.of(context)!.connectPrint_lbl_connected,
//               style: Theme.of(context).textTheme.titleMedium,
//               textAlign: TextAlign.center,
//             ),
//           ],
//         )),
//         ElevatedButton(
//           onPressed: () => context.read<PeripheralsCubit>().disconnectPrinter(),
//           child: Text(AppLocalizations.of(context)!.connectPrint_btn_disconnect),
//         ),
//         const SizedBox(
//           height: 16.0,
//         ),
//       ],
//     );
//   }
//
//   Widget _printerDisconnectedView() {
//     return Column(
//       children: [
//         Column(
//           mainAxisAlignment: MainAxisAlignment.start,
//           crossAxisAlignment: CrossAxisAlignment.center,
//           children: [
//             Text(
//               AppLocalizations.of(context)!.connectPrint_lbl_pairTitle,
//               textAlign: TextAlign.center,
//               style: Theme.of(context).textTheme.titleMedium,
//               textScaler: TextScaler.linear(ScaleUtil.textScaleFactor(context)),
//             ),
//             const SizedBox(
//               height: 8.0,
//             ),
//             Text(
//               AppLocalizations.of(context)!.connectPrint_lbl_pairInstructions,
//               textAlign: TextAlign.center,
//               style: Theme.of(context).textTheme.bodyMedium,
//               textScaler: TextScaler.linear(ScaleUtil.textScaleFactor(context)),
//             ),
//             const SizedBox(
//               height: 16.0,
//             ),
//             Text(
//               AppLocalizations.of(context)!.connectPrint_lbl_connectTitle,
//               textAlign: TextAlign.center,
//               style: Theme.of(context).textTheme.titleMedium,
//               textScaler: TextScaler.linear(ScaleUtil.textScaleFactor(context)),
//             ),
//             const SizedBox(
//               height: 8.0,
//             ),
//             Text(
//               AppLocalizations.of(context)!.connectPrint_lbl_connectInstructions,
//               textAlign: TextAlign.center,
//               style: Theme.of(context).textTheme.bodyMedium,
//               textScaler: TextScaler.linear(ScaleUtil.textScaleFactor(context)),
//             ),
//             const SizedBox(
//               height: 16.0,
//             ),
//           ],
//         ),
//         Expanded(
//             child: Chewie(
//           controller: chewieController,
//         )),
//       ],
//     );
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return PopScope(
//       onPopInvoked: (bool didPop) {
//         context.read<ILogService>().event("Exiting Page", additionalProperties: {"page": "ConnectPrinterPage"});
//       },
//       child: Scaffold(
//         appBar: CustomAppBar(
//           title: Text(AppLocalizations.of(context)!.connectPrint_lbl_title),
//           initialHelpDocumentPage: HelpDocumentPage.printer,
//         ),
//         body: BlocListener<PeripheralsCubit, PeripheralsState>(
//           listenWhen: (oldState, newState) => oldState.printerInfo.isConnected != newState.printerInfo.isConnected,
//           listener: (context, state) {
//             // Pop the page when the printer status changes to connected or disconnected
//             Navigator.pop(context, true);
//           },
//           child: BlocBuilder<PeripheralsCubit, PeripheralsState>(
//             buildWhen: (oldState, newState) =>
//                 oldState.printerInfo != newState.printerInfo || oldState.connectingPrinter != newState.connectingPrinter,
//             builder: (context, state) {
//               if (state.connectingPrinter) {
//                 return Center(
//                   child: Column(
//                     mainAxisSize: MainAxisSize.min,
//                     crossAxisAlignment: CrossAxisAlignment.center,
//                     children: [
//                       const SizedBox(
//                         width: 50,
//                         height: 50,
//                         child: CircularProgressIndicator(
//                           color: Colors.white,
//                           strokeWidth: 6,
//                         ),
//                       ),
//                       const SizedBox(
//                         height: 32.0,
//                       ),
//                       Text(
//                         AppLocalizations.of(context)!.connectPrint_lbl_connectingToPrinter,
//                         textAlign: TextAlign.center,
//                         style: Theme.of(context).textTheme.titleMedium!.copyWith(),
//                         textScaler: TextScaler.linear(ScaleUtil.textScaleFactor(context)),
//                       ),
//                     ],
//                   ),
//                 );
//               }
//
//               PeripheralStatus peripheralStatus = PeripheralStatus.disconnected;
//               if (state.printerInfo.isConnected) {
//                 peripheralStatus = PeripheralStatus.connected;
//                 if (!state.printerInfo.isReadyToPrint) {
//                   peripheralStatus = PeripheralStatus.unavailable;
//                 }
//               }
//               final printerConnected = state.printerInfo.isConnected;
//               return Padding(
//                 padding: const EdgeInsets.all(8.0),
//                 child: Column(
//                   mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                   children: [
//                     ConnectionStatusChip(peripheralStatus: peripheralStatus),
//                     const SizedBox(
//                       height: 28.0,
//                     ),
//                     Expanded(child: printerConnected ? _printerConnectedView() : _printerDisconnectedView()),
//                   ],
//                 ),
//               );
//             },
//           ),
//         ),
//       ),
//     );
//   }
// }
