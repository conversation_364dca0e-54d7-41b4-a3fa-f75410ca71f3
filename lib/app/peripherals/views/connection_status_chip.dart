import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:sort_pro_printer_flutter/app/peripherals/peripheral_status.dart';

class ConnectionStatusChip extends StatelessWidget {
  final PeripheralStatus peripheralStatus;

  const ConnectionStatusChip({Key? key, required this.peripheralStatus}) : super(key: key);

  getStatusColor() {
    return peripheralStatus == PeripheralStatus.disconnected
        ? Colors.red
        : peripheralStatus == PeripheralStatus.connected
            ? const Color(0xff0DD545)
            : Colors.yellow;
  }

  String getStatusText(BuildContext context) {
    return peripheralStatus == PeripheralStatus.disconnected
        ? AppLocalizations.of(context)!.connectionStatus_disconnected
        : peripheralStatus == PeripheralStatus.connected
            ? AppLocalizations.of(context)!.connectionStatus_connected
            : AppLocalizations.of(context)!.connectionStatus_unavailable;
  }

  @override
  Widget build(BuildContext context) {
    return Chip(
      backgroundColor: const Color(0xFF2E2E2E),
      label: RichText(
        textAlign: TextAlign.center,
        text: TextSpan(text: AppLocalizations.of(context)!.connectionStatus_label, children: [
          TextSpan(
            text: getStatusText(context),
            style: Theme.of(context).textTheme.bodyMedium!.copyWith(color: getStatusColor()),
          )
        ]),
      ),
    );
  }
}
