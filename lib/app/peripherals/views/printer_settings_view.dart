import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sort_pro_printer_flutter/app/app_events/cubit/app_event_cubit.dart';
import 'package:sort_pro_printer_flutter/app/app_events/models/app_event.dart';
import 'package:sort_pro_printer_flutter/app/manage/views/peripheral_settings_view.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:sort_pro_printer_flutter/app/peripherals/cubit/peripherals_cubit.dart';
import 'package:sort_pro_printer_flutter/app/peripherals/peripheral_status.dart';
import 'package:sort_pro_printer_flutter/app/peripherals/peripheral_type.dart';

class PrinterSettingsView extends StatelessWidget {
  const PrinterSettingsView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<PeripheralsCubit, PeripheralsState>(
      builder: (context, state) {
        final printerInfo = state.printerInfo;
        final printerPaired = state.printerPaired;
        PeripheralStatus peripheralStatus = PeripheralStatus.disconnected;
        String title = AppLocalizations.of(context)!.printerStatus_title_unpaired;
        String subtitle = AppLocalizations.of(context)!.printerStatus_subtitle_unpaired;

        // if the printer is paired, show it at least unavailable
        if (printerPaired) {
          peripheralStatus = PeripheralStatus.unavailable;
          title = AppLocalizations.of(context)!.printerStatus_title_disabled;
          subtitle = AppLocalizations.of(context)!.printerStatus_subtitle_disabled;
        }
        // Printer is ready to go
        if (printerInfo.isConnected) {
          peripheralStatus = PeripheralStatus.connected;
          title = AppLocalizations.of(context)!.printerStatus_title_enabled;
          subtitle = AppLocalizations.of(context)!.printerStatus_subtitle_enabled;
        }
        // Printer is currently trying to connect
        if (state.connectingPrinter) {
          peripheralStatus = PeripheralStatus.connecting;
          title = "";
          subtitle = "";
        }

        return PeripheralSettingsView(
          peripheralStatus: peripheralStatus,
          title: title,
          subtitle: subtitle,
          peripheralType: PeripheralType.printer,
          onPressed: () => peripheralStatus == PeripheralStatus.unavailable
              ? context.read<PeripheralsCubit>().enablePrinter()
              : peripheralStatus == PeripheralStatus.connected
                  ? context.read<PeripheralsCubit>().disconnectPrinter()
                  : context.read<AppEventCubit>().addEvent(AppEvent.error(AppEventCode.printerNotFound)),
        );
      },
    );
  }
}
