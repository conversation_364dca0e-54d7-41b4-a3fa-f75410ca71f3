import 'package:flutter/material.dart';
import 'package:sort_pro_printer_flutter/app/peripherals/peripheral_status.dart';

class PeripheralButton extends StatelessWidget {
  final PeripheralStatus peripheralStatus;
  final IconData icon;
  final VoidCallback? onPressed;

  const PeripheralButton({Key? key, required this.icon, this.onPressed, required this.peripheralStatus}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: SizedBox(
        width: 50,
        height: 50,
        child: Stack(
          children: [
            Positioned(
              top: 2.5,
              left: 0,
              width: 45,
              height: 45,
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  color: const Color(0xff464646),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(icon),
                  ],
                ),
              ),
            ),
            Positioned(
              right: 0.5,
              top: -1,
              child: Icon(
                Icons.circle,
                size: 13,
                color: peripheralStatus == PeripheralStatus.disconnected
                    ? Colors.red
                    : peripheralStatus == PeripheralStatus.connected
                        ? const Color(0xff0DD545)
                        : Colors.yellow,
              ),
            )
          ],
        ),
      ),
    );
  }
}
