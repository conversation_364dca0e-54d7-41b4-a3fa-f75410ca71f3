/// This page has been removed from the app but we'll keep it for the records in case we bring it back
// import 'package:chewie/chewie.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:flutter_gen/gen_l10n/app_localizations.dart';
// import 'package:sort_pro_printer_flutter/app/custom_app_bar.dart';
// import 'package:sort_pro_printer_flutter/app/help/help_document_page.dart';
// import 'package:sort_pro_printer_flutter/app/peripherals/cubit/peripherals_cubit.dart';
// import 'package:sort_pro_printer_flutter/app/peripherals/peripheral_status.dart';
// import 'package:sort_pro_printer_flutter/app/peripherals/views/connection_status_chip.dart';
// import 'package:sort_pro_printer_flutter/services/logs/i_log_service.dart';
// import 'package:sort_pro_printer_flutter/services/ring_scanner/i_ringer_service.dart';
// import 'package:sort_pro_printer_flutter/styles/scale_size.dart';
// import 'package:video_player/video_player.dart';
//
// class ConnectRingerScannerPage extends StatefulWidget {
//   const ConnectRingerScannerPage._({super.key});
//
//   static MaterialPageRoute route(BuildContext context, {Key? key}) {
//     context.read<ILogService>().event("Navigating to page", additionalProperties: {"page": "ConnectRingerScannerPage"});
//     return MaterialPageRoute(
//       fullscreenDialog: true,
//       builder: (_) => ConnectRingerScannerPage._(
//         key: key,
//       ),
//     );
//   }
//
//   static const _unpairScannerMp4Path = "assets/peripherals/unpair-finger-scanner.mp4";
//   static const _pairScannerMp4Path = "assets/peripherals/pair-finger-scanner.mp4";
//
//   @override
//   State<ConnectRingerScannerPage> createState() => _ConnectRingerScannerPageState();
// }
//
// class _ConnectRingerScannerPageState extends State<ConnectRingerScannerPage> {
//   late ChewieController chewieController;
//   videoPlayer(String filePath) {
//     chewieController = ChewieController(
//       videoPlayerController: VideoPlayerController.asset(filePath),
//       aspectRatio: 2 / 3,
//       autoInitialize: true,
//       autoPlay: true,
//       looping: true,
//       draggableProgressBar: true,
//       errorBuilder: (context, errorMessage) {
//         return Center(
//           child: Text(
//             errorMessage,
//             style: const TextStyle(color: Colors.white),
//           ),
//         );
//       },
//     );
//     return chewieController;
//   }
//
//   @override
//   void dispose() {
//     chewieController.dispose();
//     super.dispose();
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return BlocConsumer<PeripheralsCubit, PeripheralsState>(
//       listenWhen: (oldState, newState) => oldState.ringScannerStatus != newState.ringScannerStatus,
//       listener: (context, state) {
//         if (state.ringScannerStatus == RingScannerStatus.connected) {
//           Navigator.pop(context);
//         }
//       },
//       buildWhen: (oldState, newState) => oldState.ringScannerStatus != newState.ringScannerStatus,
//       builder: (context, state) {
//         final ringScannerStatus = state.ringScannerStatus;
//         final peripheralStatus =
//             ringScannerStatus == RingScannerStatus.connected ? PeripheralStatus.connected : PeripheralStatus.disconnected;
//         final scannerConnected = peripheralStatus == PeripheralStatus.connected;
//         return Scaffold(
//           appBar: CustomAppBar(
//             title: Text(AppLocalizations.of(context)!.connectScanner_lbl_title),
//             initialHelpDocumentPage: HelpDocumentPage.fingerScanner,
//           ),
//           body: Padding(
//             padding: const EdgeInsets.all(8.0),
//             child: Center(
//               child: Column(
//                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                 children: [
//                   ConnectionStatusChip(peripheralStatus: peripheralStatus),
//                   const SizedBox(
//                     height: 28.0,
//                   ),
//                   Column(
//                     mainAxisAlignment: MainAxisAlignment.start,
//                     crossAxisAlignment: CrossAxisAlignment.center,
//                     children: [
//                       Text(
//                         scannerConnected
//                             ? AppLocalizations.of(context)!.connectScanner_lbl_disconnectInstructionsTitle
//                             : AppLocalizations.of(context)!.connectScanner_lbl_connectInstructionsTitle,
//                         textAlign: TextAlign.center,
//                         style: Theme.of(context).textTheme.titleMedium,
//                         textScaler: TextScaler.linear(ScaleUtil.textScaleFactor(context)),
//                       ),
//                       const SizedBox(
//                         height: 8.0,
//                       ),
//                       Text(
//                         scannerConnected
//                             ? AppLocalizations.of(context)!.connectScanner_lbl_disconnectInstructionsBody
//                             : AppLocalizations.of(context)!.connectScanner_lbl_connectInstructionsBody,
//                         textAlign: TextAlign.center,
//                         style: Theme.of(context).textTheme.bodyMedium,
//                         textScaler: TextScaler.linear(ScaleUtil.textScaleFactor(context)),
//                       ),
//                       const SizedBox(
//                         height: 16.0,
//                       ),
//                     ],
//                   ),
//                   Expanded(
//                       child: Chewie(
//                     controller: scannerConnected
//                         ? videoPlayer(ConnectRingerScannerPage._unpairScannerMp4Path)
//                         : videoPlayer(ConnectRingerScannerPage._pairScannerMp4Path),
//                   )),
//                 ],
//               ),
//             ),
//           ),
//         );
//       },
//     );
//   }
// }
