import 'package:shared_preferences/shared_preferences.dart';
import 'package:sort_pro_printer_flutter/app/pin_lookup/data/models/lookup_result.dart';
import 'package:sort_pro_printer_flutter/services/printer/i_printer_service.dart';
import 'package:sort_pro_printer_flutter/services/printer/printer_info.dart';
import 'package:sort_pro_printer_flutter/services/ring_scanner/i_ringer_service.dart';

class PeripheralsRepository {
  final IPrinterService _printerService;
  final IRingScannerService _ringScannerService;

  Stream<PrinterInfo> get printerInfoStream => _printerService.printerInfo;

  Stream<RingScannerStatus> get ringScannerStatusStream => _ringScannerService.ringScannerStatusStream;

  PeripheralsRepository(
    this._printerService,
    this._ringScannerService,
  );

  /// Printer
  Future<String?> getPairedPrinterMacAddress() async => await _printerService.getPairedPrinterMacAddress();

  Future<void> connectPrinter(String macAddress) async {
    try {
      await _printerService.connect(macAddress);
    } catch (e) {
      rethrow;
    }
  }

  Future<void> printLookupLabel(LookupResult? lookupResult, String languageCode) async {
    try {
      await _printerService.printLookupLabel(lookupResult, languageCode);
    } catch (e) {
      rethrow;
    }
  }

  Future<void> disconnectPrinter() async => await _printerService.disconnect();

  Future<bool> isPrinterConnected() async => await _printerService.isPrinterConnected();

  Future<void> savePrinterMacAddress(String macAddress) async {
    final prefs = await SharedPreferences.getInstance();
    prefs.setString('printerMac', macAddress);
  }

  Future<void> removePrinterMacAddress() async {
    final prefs = await SharedPreferences.getInstance();
    prefs.setString('printerMac', '');
  }

  Future<String?> getPrinterMacAddress() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('printerMac');
  }

  /// Ring Scanner
  Future<RingScannerStatus> getRingScannerStatus() async => await _ringScannerService.getCurrentScannerStatus();

  dispose() {
    _ringScannerService.dispose();
  }
}
