trigger:
  paths:
    include:
      - sortproprinter_flutter/
  branches:
    include:
      - main

variables:
  - group: sortpro-secrets-qa

jobs:
  - job: SortProPrinterFlutter
    pool:
      vmImage: 'macOS-latest'
      name: Azure Pipelines
    steps:
      - task: FlutterInstall@0
        displayName: 'Install Flutter'
        inputs:
          mode: 'auto'
          channel: 'stable'
          version: 'custom'
          customVersion: '3.24.5' 

      - task: JavaToolInstaller@0
        displayName: 'Install Java Tool'
        inputs:
          versionSpec: '11'
          jdkArchitectureOption: 'x64'
          jdkSourceOption: 'PreInstalled'

      - task: DownloadSecureFile@1
        name: sortproprinterKeyStore
        displayName: 'Download the keystore file'
        inputs:
          secureFile: 'sortproprinter-keystore.jks'

      - script: |
          echo -e "storePassword=$(apksigner-keystore-password)\nkeyPassword=$(apksigner-key-password)\nkeyAlias=$(apksigner-alias)\nstoreFile=$(sortproprinterKeyStore.secureFilePath)" > key.properties
        displayName: 'Add signing info to the key.properties file'
        workingDirectory: $(System.DefaultWorkingDirectory)/sortproprinter_flutter/android

      - task: FlutterCommand@0
        displayName: 'Flutter Get Dependencies'
        inputs:
          projectDirectory: 'sortproprinter_flutter'
          arguments: 'pub get'

      - task: FlutterCommand@0
        displayName: 'Flutter Generate Localization Files'
        inputs:
          projectDirectory: 'sortproprinter_flutter'
          arguments: 'gen-l10n'

      - task: FlutterBuild@0
        displayName: 'Flutter Build'
        inputs:
          target: 'apk'
          projectDirectory: 'sortproprinter_flutter'
          buildNumber: '$(Build.BuildId)'
          buildName: '1.0.$(Build.BuildNumber)'
          entryPoint: 'lib/main.dart'
          dartDefineMulti: '
          ACS_API_KEY=$(acs-api-key) 
          ACS_API_URL=$(acs-api-url) 
          ACS_PURO_LABEL_MODEL_ID=$(acs-puro-label-model-id) 
          SSLWS_URL=$(sslws-url) 
          SSLWS_DOMAIN=$(sslws-domain) 
          SSLWS_USER=$(sslws-user) 
          SSLWS_PASS=$(sslws-pass) 
          FTP_SS_HOST=$(ftp-ss-host) 
          FTP_SS_PORT=$(ftp-ss-port) 
          FTP_SS_USER=$(ftp-ss-user) 
          FTP_SS_PASS=$(ftp-ss-pass) 
          ASA_CONN_STRING=$(asa-conn-string) 
          ASA_CONTAINER_NAME=$(asa-container-name) 
          APP_CENTER_KEY=$(app-center-key)
          APP_INSIGHT_INSTRUMENTATION_KEY=$(appi-instrumentation-key)
          SESSION_EXPIRE_TIME_SECONDS=$(session-expire-time-seconds)
          ACTIVE_SESSION_TIMEOUT_SECONDS=$(active-session-timeout-seconds)
          LOG_LEVEL=$(application-log-level)
          ENV=qa
          '
          extraArgs: '--target-platform android-arm64 --obfuscate --split-debug-info=build/app/outputs/symbols --flavor nonprod'
      - task: CopyFiles@2
        displayName: 'Copy apk Files'
        inputs:
          SourceFolder: 'sortproprinter_flutter/build/app/outputs/flutter-apk'
          TargetFolder: '$(Build.ArtifactStagingDirectory)/flutter-apk'
      - task: CopyFiles@2
        displayName: 'Copy Flutter Symbols'
        inputs:
          SourceFolder: 'sortproprinter_flutter/build/app/outputs/symbols'
          TargetFolder: '$(Build.ArtifactStagingDirectory)/symbols/flutter'
      - task: CmdLine@2
        displayName: 'Add project, environment and build number to app-nonprod-release.apk'
        inputs:
          script: mv $(Build.artifactstagingdirectory)/flutter-apk/app-nonprod-release.apk $(Build.artifactstagingdirectory)/app-release-sortproprinter-$(environment)-$(Build.BuildNumber).apk
      - task: CmdLine@2
        displayName: 'Create and copy artifact files to artifact staging directory MobiControl folder'
        inputs:
          script: |
            mkdir $(Build.artifactstagingdirectory)/mobicontrol
            mv sortproprinter_flutter/mobicontrol/sortpro-qa.json $(Build.artifactstagingdirectory)/mobicontrol/sortpro-qa.json
            cp $(Build.artifactstagingdirectory)/app-release-sortproprinter-$(environment)-$(Build.BuildNumber).apk $(Build.artifactstagingdirectory)/mobicontrol/app-release-sortproprinter-$(environment)-$(Build.BuildNumber).apk
      - task: PublishBuildArtifacts@1
        displayName: 'Publish Artifact: psf-artifact'
        inputs:
          PathtoPublish: '$(Build.ArtifactStagingDirectory)'
          ArtifactName: 'sortproprinter_flutter_$(environment)_$(Build.BuildNumber)'
          publishLocation: 'Container'
