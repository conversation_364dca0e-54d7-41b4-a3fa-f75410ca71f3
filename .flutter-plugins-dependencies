{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "audioplayers_darwin", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_darwin-5.0.2/", "native_build": true, "dependencies": []}, {"name": "camera_avfoundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.19/", "native_build": true, "dependencies": []}, {"name": "connectivity_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-4.0.2/", "native_build": true, "dependencies": []}, {"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-9.1.2/", "native_build": true, "dependencies": []}, {"name": "flutter_image_compress_common", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/", "native_build": true, "dependencies": []}, {"name": "flutter_isolate", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_isolate-2.1.0/", "native_build": true, "dependencies": []}, {"name": "flutter_keyboard_visibility", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_keyboard_visibility-5.4.1/", "native_build": true, "dependencies": []}, {"name": "flutter_pdfview", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_pdfview-1.4.1+1/", "native_build": true, "dependencies": []}, {"name": "google_mlkit_barcode_scanning", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_mlkit_barcode_scanning-0.6.0/", "native_build": true, "dependencies": ["google_mlkit_commons"]}, {"name": "google_mlkit_commons", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_mlkit_commons-0.3.0/", "native_build": true, "dependencies": []}, {"name": "package_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-4.2.0/", "native_build": true, "dependencies": []}, {"name": "path_provider_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "permission_handler_apple", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.1.4/", "native_build": true, "dependencies": []}, {"name": "shared_preferences_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "sqlite3_flutter_libs", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqlite3_flutter_libs-0.5.34/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "vibration", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vibration-1.9.0/", "native_build": true, "dependencies": []}, {"name": "video_player_avfoundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/video_player_avfoundation-2.7.1/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "wakelock_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus-1.1.4/", "native_build": true, "dependencies": ["package_info_plus"]}, {"name": "webview_flutter_wkwebview", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.14.0/", "native_build": true, "dependencies": []}, {"name": "workmanager", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/workmanager-0.5.2/", "native_build": true, "dependencies": []}], "android": [{"name": "android_id", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/android_id-0.3.6/", "native_build": true, "dependencies": []}, {"name": "appcenter", "path": "/Users/<USER>/Downloads/sortproprinter_flutter/packages/appcenter/", "native_build": true, "dependencies": []}, {"name": "audioplayers_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_android-4.0.3/", "native_build": true, "dependencies": []}, {"name": "camera_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camera_android-0.10.9+11/", "native_build": true, "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "connectivity_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-4.0.2/", "native_build": true, "dependencies": []}, {"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-9.1.2/", "native_build": true, "dependencies": []}, {"name": "flutter_image_compress_common", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/", "native_build": true, "dependencies": []}, {"name": "flutter_isolate", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_isolate-2.1.0/", "native_build": true, "dependencies": []}, {"name": "flutter_keyboard_visibility", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_keyboard_visibility-5.4.1/", "native_build": true, "dependencies": []}, {"name": "flutter_pdfview", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_pdfview-1.4.1+1/", "native_build": true, "dependencies": []}, {"name": "flutter_plugin_android_lifecycle", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.22/", "native_build": true, "dependencies": []}, {"name": "google_mlkit_barcode_scanning", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_mlkit_barcode_scanning-0.6.0/", "native_build": true, "dependencies": ["google_mlkit_commons"]}, {"name": "google_mlkit_commons", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_mlkit_commons-0.3.0/", "native_build": true, "dependencies": []}, {"name": "package_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-4.2.0/", "native_build": true, "dependencies": []}, {"name": "path_provider_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.10/", "native_build": true, "dependencies": []}, {"name": "permission_handler_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-10.3.6/", "native_build": true, "dependencies": []}, {"name": "shared_preferences_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.3.2/", "native_build": true, "dependencies": []}, {"name": "sqlite3_flutter_libs", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqlite3_flutter_libs-0.5.34/", "native_build": true, "dependencies": []}, {"name": "vibration", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vibration-1.9.0/", "native_build": true, "dependencies": []}, {"name": "video_player_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/video_player_android-2.7.1/", "native_build": true, "dependencies": []}, {"name": "wakelock_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus-1.1.4/", "native_build": true, "dependencies": ["package_info_plus"]}, {"name": "webview_flutter_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_android-3.16.8/", "native_build": true, "dependencies": []}, {"name": "workmanager", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/workmanager-0.5.2/", "native_build": true, "dependencies": []}], "macos": [{"name": "audioplayers_darwin", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_darwin-5.0.2/", "native_build": true, "dependencies": []}, {"name": "connectivity_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-4.0.2/", "native_build": true, "dependencies": []}, {"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-9.1.2/", "native_build": true, "dependencies": []}, {"name": "flutter_image_compress_macos", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_image_compress_macos-1.0.3/", "native_build": true, "dependencies": []}, {"name": "flutter_keyboard_visibility_macos", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_keyboard_visibility_macos-1.0.0/", "native_build": false, "dependencies": []}, {"name": "package_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-4.2.0/", "native_build": true, "dependencies": []}, {"name": "path_provider_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "shared_preferences_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "sqlite3_flutter_libs", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqlite3_flutter_libs-0.5.34/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "video_player_avfoundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/video_player_avfoundation-2.7.1/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "wakelock_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus-1.1.4/", "native_build": true, "dependencies": ["package_info_plus"]}], "linux": [{"name": "audioplayers_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_linux-3.1.0/", "native_build": true, "dependencies": []}, {"name": "connectivity_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-4.0.2/", "native_build": false, "dependencies": []}, {"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-9.1.2/", "native_build": false, "dependencies": []}, {"name": "flutter_keyboard_visibility_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_keyboard_visibility_linux-1.0.0/", "native_build": false, "dependencies": []}, {"name": "package_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-4.2.0/", "native_build": false, "dependencies": []}, {"name": "path_provider_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/", "native_build": false, "dependencies": []}, {"name": "shared_preferences_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/", "native_build": false, "dependencies": ["path_provider_linux"]}, {"name": "sqlite3_flutter_libs", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqlite3_flutter_libs-0.5.34/", "native_build": true, "dependencies": []}, {"name": "wakelock_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus-1.1.4/", "native_build": false, "dependencies": ["package_info_plus"]}], "windows": [{"name": "audioplayers_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_windows-3.1.0/", "native_build": true, "dependencies": []}, {"name": "connectivity_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-4.0.2/", "native_build": true, "dependencies": []}, {"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-9.1.2/", "native_build": false, "dependencies": []}, {"name": "flutter_keyboard_visibility_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_keyboard_visibility_windows-1.0.0/", "native_build": false, "dependencies": []}, {"name": "package_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-4.2.0/", "native_build": false, "dependencies": []}, {"name": "path_provider_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/", "native_build": false, "dependencies": []}, {"name": "permission_handler_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.1.3/", "native_build": true, "dependencies": []}, {"name": "shared_preferences_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/", "native_build": false, "dependencies": ["path_provider_windows"]}, {"name": "sqlite3_flutter_libs", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqlite3_flutter_libs-0.5.34/", "native_build": true, "dependencies": []}, {"name": "wakelock_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus-1.1.4/", "native_build": false, "dependencies": ["package_info_plus"]}], "web": [{"name": "audioplayers_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_web-4.1.0/", "dependencies": []}, {"name": "camera_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camera_web-0.3.5/", "dependencies": []}, {"name": "connectivity_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-4.0.2/", "dependencies": []}, {"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-9.1.2/", "dependencies": []}, {"name": "flutter_image_compress_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_image_compress_web-0.1.5/", "dependencies": []}, {"name": "flutter_keyboard_visibility_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_keyboard_visibility_web-2.0.0/", "dependencies": []}, {"name": "package_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-4.2.0/", "dependencies": []}, {"name": "shared_preferences_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/", "dependencies": []}, {"name": "video_player_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/video_player_web-2.3.5/", "dependencies": []}, {"name": "wakelock_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus-1.1.4/", "dependencies": ["package_info_plus"]}]}, "dependencyGraph": [{"name": "android_id", "dependencies": []}, {"name": "appcenter", "dependencies": []}, {"name": "audioplayers", "dependencies": ["audioplayers_android", "audioplayers_darwin", "audioplayers_linux", "audioplayers_web", "audioplayers_windows", "path_provider"]}, {"name": "audioplayers_android", "dependencies": []}, {"name": "audioplayers_darwin", "dependencies": []}, {"name": "audioplayers_linux", "dependencies": []}, {"name": "audioplayers_web", "dependencies": []}, {"name": "audioplayers_windows", "dependencies": []}, {"name": "camera", "dependencies": ["camera_android", "camera_avfoundation", "camera_web", "flutter_plugin_android_lifecycle"]}, {"name": "camera_android", "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "camera_avfoundation", "dependencies": []}, {"name": "camera_web", "dependencies": []}, {"name": "connectivity_plus", "dependencies": []}, {"name": "device_info_plus", "dependencies": []}, {"name": "flutter_image_compress", "dependencies": ["flutter_image_compress_common", "flutter_image_compress_web", "flutter_image_compress_macos", "flutter_image_compress_ohos"]}, {"name": "flutter_image_compress_common", "dependencies": []}, {"name": "flutter_image_compress_macos", "dependencies": []}, {"name": "flutter_image_compress_ohos", "dependencies": []}, {"name": "flutter_image_compress_web", "dependencies": []}, {"name": "flutter_isolate", "dependencies": []}, {"name": "flutter_keyboard_visibility", "dependencies": ["flutter_keyboard_visibility_linux", "flutter_keyboard_visibility_macos", "flutter_keyboard_visibility_web", "flutter_keyboard_visibility_windows"]}, {"name": "flutter_keyboard_visibility_linux", "dependencies": []}, {"name": "flutter_keyboard_visibility_macos", "dependencies": []}, {"name": "flutter_keyboard_visibility_web", "dependencies": []}, {"name": "flutter_keyboard_visibility_windows", "dependencies": []}, {"name": "flutter_pdfview", "dependencies": []}, {"name": "flutter_plugin_android_lifecycle", "dependencies": []}, {"name": "google_mlkit_barcode_scanning", "dependencies": ["google_mlkit_commons"]}, {"name": "google_mlkit_commons", "dependencies": []}, {"name": "package_info_plus", "dependencies": []}, {"name": "path_provider", "dependencies": ["path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_windows"]}, {"name": "path_provider_android", "dependencies": []}, {"name": "path_provider_foundation", "dependencies": []}, {"name": "path_provider_linux", "dependencies": []}, {"name": "path_provider_windows", "dependencies": []}, {"name": "permission_handler", "dependencies": ["permission_handler_android", "permission_handler_apple", "permission_handler_windows"]}, {"name": "permission_handler_android", "dependencies": []}, {"name": "permission_handler_apple", "dependencies": []}, {"name": "permission_handler_windows", "dependencies": []}, {"name": "shared_preferences", "dependencies": ["shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "shared_preferences_android", "dependencies": []}, {"name": "shared_preferences_foundation", "dependencies": []}, {"name": "shared_preferences_linux", "dependencies": ["path_provider_linux"]}, {"name": "shared_preferences_web", "dependencies": []}, {"name": "shared_preferences_windows", "dependencies": ["path_provider_windows"]}, {"name": "sqlite3_flutter_libs", "dependencies": []}, {"name": "vibration", "dependencies": []}, {"name": "video_player", "dependencies": ["video_player_android", "video_player_avfoundation", "video_player_web"]}, {"name": "video_player_android", "dependencies": []}, {"name": "video_player_avfoundation", "dependencies": []}, {"name": "video_player_web", "dependencies": []}, {"name": "wakelock_plus", "dependencies": ["package_info_plus"]}, {"name": "webview_flutter", "dependencies": ["webview_flutter_android", "webview_flutter_wkwebview"]}, {"name": "webview_flutter_android", "dependencies": []}, {"name": "webview_flutter_wkwebview", "dependencies": []}, {"name": "workmanager", "dependencies": []}], "date_created": "2025-06-29 22:48:43.940944", "version": "3.22.0"}