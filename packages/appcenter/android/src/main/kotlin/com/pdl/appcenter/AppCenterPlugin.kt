package com.pdl.appcenter

import android.app.Activity
import com.microsoft.appcenter.AppCenter
import com.microsoft.appcenter.analytics.Analytics
import com.microsoft.appcenter.crashes.Crashes
import io.flutter.embedding.engine.plugins.FlutterPlugin
import io.flutter.embedding.engine.plugins.activity.ActivityAware
import io.flutter.embedding.engine.plugins.activity.ActivityPluginBinding
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.MethodChannel.MethodCallHandler
import io.flutter.plugin.common.MethodChannel.Result


/** AppCenterPlugin */
class AppCenterPlugin : FlutterPlugin, MethodCallHandler, ActivityAware {

    private lateinit var channel: MethodChannel
    private var activity: Activity? = null
    private var appKey: String? = null
    private var initialized: Boolean = false

    override fun onAttachedToEngine(flutterPluginBinding: FlutterPlugin.FlutterPluginBinding) {
        channel = MethodChannel(flutterPluginBinding.binaryMessenger, "com.pdl.appcenter")
        channel.setMethodCallHandler(this)
    }

    override fun onMethodCall(call: MethodCall, result: Result) {
        when (call.method) {
            "initialize" -> {
                appKey = call.arguments.toString()
                initializeAppCenter()
                result.success(null)
            }
            "setUser" -> {
                val userId: String = call.arguments.toString()
                if (userId.isEmpty()) {
                    result.error("APP_CENTER_EXCEPTION", "User ID is empty.", null)
                    return
                }
                AppCenter.setUserId(userId)
                result.success(true)
            }
            "setLogLevel" -> {
                val logLevelId: Int = call.arguments as Int
                AppCenter.setLogLevel(logLevelId)
                result.success(true)
            }
            "trackEvent" -> {
                val eventType = call.argument<String>("eventType")
                val eventProps = call.argument<Map<String, String>>("eventProps")
                Analytics.trackEvent(eventType, eventProps)
                result.success(true)
            }
            "trackError" -> {
                val exception = call.argument<String>("exception")
                val exceptionDetails = call.argument<Map<String, String>>("exceptionDetails")
                Crashes.trackError(Exception(exception), exceptionDetails, null)
                result.success(true)
            }
            "generateTestCrash" -> {
                Crashes.generateTestCrash()
                result.success(true)
            }
            else -> {
                result.notImplemented()
            }
        }
    }

    private fun initializeAppCenter() {
        if(!initialized && activity != null && appKey != null) {
            AppCenter.start(activity!!.application, appKey, Analytics::class.java, Crashes::class.java)
            initialized = true
        }
    }

    override fun onDetachedFromEngine(binding: FlutterPlugin.FlutterPluginBinding) {
        channel.setMethodCallHandler(null)
    }

    /** ActivityAware **/

    override fun onAttachedToActivity(binding: ActivityPluginBinding) {
        activity = binding.activity
        initializeAppCenter()
    }

    override fun onReattachedToActivityForConfigChanges(binding: ActivityPluginBinding) {
        onAttachedToActivity(binding)
    }

    override fun onDetachedFromActivityForConfigChanges() {
        onDetachedFromActivity()
    }

    override fun onDetachedFromActivity() {}
}
