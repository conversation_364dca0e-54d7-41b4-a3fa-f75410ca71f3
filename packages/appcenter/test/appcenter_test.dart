import 'package:appcenter/app_center_log_level.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:appcenter/appcenter_platform_interface.dart';
import 'package:appcenter/appcenter_method_channel.dart';
import 'package:plugin_platform_interface/plugin_platform_interface.dart';

class MockAppcenterPlatform with MockPlatformInterfaceMixin implements AppCenterPlatform {
  @override
  Future<String> initialize(String appKey) {
    // TODO: implement initialize
    throw UnimplementedError();
  }

  @override
  Future<void> setLogLevel(AppCenterLogLevel appCenterLogLevel) {
    // TODO: implement setLogLevel
    throw UnimplementedError();
  }

  @override
  Future<void> setUser(String userId) {
    // TODO: implement setUser
    throw UnimplementedError();
  }

  @override
  Future<void> trackEvent(String eventType, [Map<String, String> additionalProperties = const {}, bool splitLongProperties = true]) {
    // TODO: implement trackEvent
    throw UnimplementedError();
  }

  @override
  Future<void> generateTestCrash() {
    // TODO: implement generateTestCrash
    throw UnimplementedError();
  }

  @override
  Future<Function> trackError(Object exception, StackTrace stackTrace, [Map<String, String> additionalProperties = const {}]) {
    // TODO: implement generateTestCrash
    throw UnimplementedError();
  }
}

void main() {
  final AppCenterPlatform initialPlatform = AppCenterPlatform.instance;

  test('$MethodChannelAppCenter is the default instance', () {
    expect(initialPlatform, isInstanceOf<MethodChannelAppCenter>());
  });

  test('getPlatformVersion', () async {
    MockAppcenterPlatform fakePlatform = MockAppcenterPlatform();
    AppCenterPlatform.instance = fakePlatform;

    // expect(await appcenterPlugin.getPlatformVersion(), '42');
  });
}
