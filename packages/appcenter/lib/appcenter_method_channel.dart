import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

import 'app_center.dart';
import 'app_center_log_level.dart';
import 'appcenter_platform_interface.dart';

/// An implementation of [AppCenterPlatform] that uses method channels.
class MethodChannelAppCenter extends AppCenterPlatform {
  /// The method channel used to interact with the native platform.
  @visibleForTesting
  final methodChannel = const MethodChannel('com.pdl.appcenter');

  @override
  Future<void> initialize(String appKey) async {
    await methodChannel.invokeMethod<String>('initialize', appKey);
  }

  @override
  Future<void> setUser(String userId) async {
    await methodChannel.invokeMethod<void>('setUser', userId);
  }

  @override
  Future<void> setLogLevel(AppCenterLogLevel appCenterLogLevel) async {
    await methodChannel.invokeMethod<void>('setLogLevel', appCenterLogLevel.id);
  }

  @override
  Future<void> trackEvent(String eventType, [Map<String, String> additionalProperties = const {}, bool splitLongProperties = true]) async {
    await methodChannel.invokeMethod<void>('trackEvent', {
      "eventType": eventType,
      "eventProps": splitLongProperties ? _splitLongProperties(additionalProperties) : additionalProperties,
    });
  }

  Map<String, String> _splitLongProperties(Map<String, String> additionalProperties) {
    final newProps = Map<String, String>.from(additionalProperties);
    for (MapEntry<String, String> entry in additionalProperties.entries) {
      // if the property value is too large, let's split it into ordered entries.
      if (entry.value.length > AppCenter.characterLimit) {
        final splitStrings = _splitString(entry.value, AppCenter.characterLimit);
        newProps.remove(entry.key);
        for (String split in splitStrings) {
          newProps.putIfAbsent("${entry.key}_${splitStrings.indexOf(split)}", () => split);
        }
      }
    }

    return newProps;
  }

  List<String> _splitString(String input, int maxLength) {
    List<String> result = [];
    int start = 0;
    int end = maxLength;

    while (start < input.length) {
      if (end >= input.length) {
        end = input.length;
      }
      result.add(input.substring(start, end));
      start = end;
      end += maxLength;
    }

    return result;
  }

  @override
  Future<void> trackError(Object exception, StackTrace stackTrace, [Map<String, String> additionalProperties = const {}]) async {
    additionalProperties.addAll({"stacktrace": "$stackTrace"});
    await methodChannel.invokeMethod<void>('trackError', {
      "exception": "$exception",
      "exceptionDetails": additionalProperties,
    });
  }

  @override
  Future<void> generateTestCrash() async {
    await methodChannel.invokeMethod<void>('generateTestCrash');
  }
}
