import 'package:appcenter/app_center_log_level.dart';
import 'package:plugin_platform_interface/plugin_platform_interface.dart';

import 'appcenter_method_channel.dart';

abstract class AppCenterPlatform extends PlatformInterface {
  /// Constructs a AppcenterPlatform.
  AppCenterPlatform() : super(token: _token);

  static final Object _token = Object();

  static AppCenterPlatform _instance = MethodChannelAppCenter();

  /// The default instance of [AppCenterPlatform] to use.
  ///
  /// Defaults to [MethodChannelAppCenter].
  static AppCenterPlatform get instance => _instance;

  /// Platform-specific implementations should set this with their own
  /// platform-specific class that extends [AppCenterPlatform] when
  /// they register themselves.
  static set instance(AppCenterPlatform instance) {
    PlatformInterface.verifyToken(instance, _token);
    _instance = instance;
  }

  Future<void> initialize(String appKey) {
    throw UnimplementedError('initialize(appKey) has not been implemented.');
  }

  Future<void> setUser(String userId) {
    throw UnimplementedError('setUser(userId) has not been implemented.');
  }

  Future<void> setLogLevel(AppCenterLogLevel appCenterLogLevel) {
    throw UnimplementedError('setLogLevel(appCenterLogLevel) has not been implemented.');
  }

  Future<void> trackEvent(String eventType, [Map<String, String> additionalProperties = const {}, bool splitLongProperties = true]) {
    throw UnimplementedError('trackEvent(eventType, additionalProperties, splitLongProperties) has not been implemented.');
  }

  Future<void> trackError(Object exception, StackTrace stackTrace, [Map<String, String> additionalProperties = const {}]) {
    throw UnimplementedError('trackError(exception, stackTrace, additionalProperties) has not been implemented.');
  }

  Future<void> generateTestCrash() {
    throw UnimplementedError('generateTestCrash() has not been implemented.');
  }
}
