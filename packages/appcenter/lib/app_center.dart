import 'app_center_log_level.dart';
import 'appcenter_platform_interface.dart';

class AppCenter {

  // The SDK truncates each property key and value to the first 125 characters.
  static const characterLimit = 125;

  AppCenter._();

  static Future<void> initialize({required String appCenterKey}) async => await AppCenterPlatform.instance.initialize(appCenterKey);

  static Future<void> setUser({required String userId}) async => await AppCenterPlatform.instance.setUser(userId);

  static Future<void> setLogLevel({required AppCenterLogLevel logLevel}) async => await AppCenterPlatform.instance.setLogLevel(logLevel);

  static Future<void> trackEvent({required String eventType, Map<String, String> additionalProperties = const {}, bool splitLongProperties = true}) async =>
      await AppCenterPlatform.instance.trackEvent(eventType, additionalProperties, splitLongProperties);

  static Future<void> trackError({required Object exception, required StackTrace stackTrace, Map<String, String> additionalProperties = const {}}) async =>
      await AppCenterPlatform.instance.trackError(exception, stackTrace, additionalProperties);

  static Future<void> generateTestCrash() async => await AppCenterPlatform.instance.generateTestCrash();
}
