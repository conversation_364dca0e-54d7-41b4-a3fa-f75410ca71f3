{"tasks": [{"taskType": "CREATE_PACKAGE", "packageName": "SortPro-QA-Automated", "uploadToServer": true, "files": [{"filePattern": "mobicontrol/app-release-sortproprinter-qa*.apk", "destinationPath": "Internal Storage", "fileFlag": 132}]}, {"taskType": "SET_PROFILE", "profileName": "Sort Pro QA-Automated", "attachedPackages": ["SortPro-QA-Automated"], "deviceGroupAssignments": [{"DeviceGroupPath": "\\\\Purolator\\Testing\\TC58", "Excluded": false}, {"DeviceGroupPath": "\\\\Purolator\\Smart Sort", "Excluded": false}]}]}