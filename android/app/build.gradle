plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}


def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

android {
    namespace "com.pdl.sortproprinter"
    compileSdkVersion flutter.compileSdkVersion
    ndkVersion flutter.ndkVersion

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        applicationId "com.pdl.sortproprinter"
        // This is the min version needed for querying with the content resolver
        minSdkVersion 26
        //noinspection ExpiredTargetSdkVersion
        targetSdkVersion 30 // Zebra devices are on android 11. Explicitly targeting this saves a lot of warnings from the IDE
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
    }

    packagingOptions {
        // https://github.com/flutter/flutter/issues/138535
        // There's an issue with Flutter 3.16.0 and flutter_pdfviewer when running in DEBUG (from android studio)
        pickFirst 'lib/x86/libc++_shared.so'
        pickFirst 'lib/x86_64/libc++_shared.so'
        pickFirst 'lib/armeabi-v7a/libc++_shared.so'
        pickFirst 'lib/arm64-v8a/libc++_shared.so'

        exclude 'META-INF/LICENSE.txt'
        exclude 'META-INF/NOTICE.txt'
        exclude 'META-INF/NOTICE'
        exclude 'META-INF/LICENSE'
        exclude 'META-INF/DEPENDENCIES'
    }

    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword keystoreProperties['storePassword']
        }
    }

    buildFeatures {
        flavorDimensions = ["app"]
    }

    productFlavors {
        nonprod {
            dimension "app"
            applicationIdSuffix ".nonprod"
        }
        prod {
            dimension "app"
        }
    }

    buildTypes {
        release {
            // com.github.ltrudu:DeviceIdentifiersWrapper must have some reflection logic in place that fails when minified
            // This doesn't affect much the size of the app (there is currently a 5mb increase)
            shrinkResources false
            minifyEnabled false
            signingConfig signingConfigs.release
        }
    }
}

flutter {
    source '../..'
}

dependencies {
    implementation 'com.github.ltrudu:DeviceIdentifiersWrapper:0.3'

    implementation files('libs/commons-lang3-3.4.jar')
    implementation files('libs/commons-net-3.1.jar')
    implementation files('libs/commons-validator-1.4.0.jar')
    implementation files('libs/core-1.53.0.0.jar')
    implementation files('libs/httpcore-4.3.1.jar')
    implementation files('libs/httpmime-4.3.2.jar')
    implementation files('libs/jackson-annotations-2.2.3.jar')
    implementation files('libs/jackson-core-2.2.3.jar')
    implementation files('libs/jackson-databind-2.2.3.jar')
    implementation files('libs/opencsv-2.2.jar')
    implementation files('libs/pkix-1.53.0.0.jar')
    implementation files('libs/prov-1.53.0.0.jar')
    implementation files('libs/snmp6_1z.jar')
    implementation files('libs/ZSDK_ANDROID_API.jar')
}
