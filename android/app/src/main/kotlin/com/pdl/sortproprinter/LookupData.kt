package com.pdl.sortproprinter

import org.json.JSONObject

enum class LookupResultType {
    routing,
    crossDock,
    srr,
    srrA,
    srrR,
    srrP,
    misdirect,
    remediation,
}

data class LookupData(
    val lookupResultType: LookupResultType,
    val pin: String,
    val terminalNumber: String,
    val date: String,
    val sequenceNumber: String?,
    val routeNumber: String?,
    val shelfNumber: String?,
    val pudroNumber: String?,
    val conveyorSide: String?,
    val routePlanId: String?
) {

    companion object {
        fun fromJsonObject(jsonObject: JSONObject): LookupData {
            return LookupData(
                lookupResultType = LookupResultType.valueOf(jsonObject.getString("lookupResultType")),
                pin = jsonObject.getString("pin"),
                terminalNumber = jsonObject.getString("terminalNumber"),
                date = jsonObject.getString("date"),
                sequenceNumber = jsonObject.getString("sequenceNumber"),
                routeNumber = jsonObject.getString("routeNumber"),
                shelfNumber = jsonObject.getString("shelfNumber"),
                pudroNumber = jsonObject.getString("pudroNumber"),
                conveyorSide = jsonObject.getString("conveyorSide"),
                routePlanId = jsonObject.getString("routePlanId"),
            )
        }
    }
}
