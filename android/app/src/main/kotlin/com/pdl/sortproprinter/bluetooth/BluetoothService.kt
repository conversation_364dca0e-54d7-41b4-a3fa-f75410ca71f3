package com.pdl.sortproprinter.bluetooth

import android.annotation.SuppressLint
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothManager
import android.content.Context

class BluetoothService(private val context: Context)  {

    private var mBTAdapter: BluetoothAdapter? = null

    // We are building for API 30 so we don't need to ask for BT permissions
    // If we end up targeting greater devices, then we need to ask for BLUETOOTH_CONNECT permissions
    @SuppressLint("MissingPermission")
    fun fetchBoundedDevices(): Set<BluetoothDevice> {
        mBTAdapter = (context.getSystemService(Context.BLUETOOTH_SERVICE) as BluetoothManager).adapter
        return mBTAdapter?.bondedDevices ?: emptySet()
    }

}