package com.pdl.sortproprinter.printer

import android.annotation.SuppressLint
import android.bluetooth.BluetoothClass
import android.bluetooth.BluetoothDevice
import com.pdl.sortproprinter.LookupData
import com.pdl.sortproprinter.LookupResultType
import com.zebra.sdk.comm.BluetoothConnection
import com.zebra.sdk.comm.ConnectionA
import com.zebra.sdk.comm.ConnectionException
import com.zebra.sdk.printer.*
import java.util.*

class ZebraPrinterService(
    override val onPrinterStatusChanged: OnPrinterStatusChanged,
    override val onPrinterStatusChangedError: OnPrinterStatusChangedError
) : IPrinterService {

    private var timer: Timer? = Timer()

    private var zebraPrinterConnection: ConnectionA? = null
    private var zebraPrinter: ZebraPrinter? = null
    private var zebraLinkOsPrinter: ZebraPrinterLinkOs? = null
    private var printerStatus: PrinterStatus = PrinterStatus()

    private fun startListeningToPrinterStatus() {
        val timerTask = object : TimerTask() {
            override fun run() {
                try {
                    val printerStatus = getPrinterStatus()
                    setPrinterStatus(printerStatus)
                } catch (e: PrinterException) {
                    onPrinterStatusChangedError(e)
                }
            }
        }
        timer = Timer()
        timer?.scheduleAtFixedRate(timerTask, 0, 1000L)
    }

    override fun connect(macAddress: String) {
        zebraPrinterConnection = BluetoothConnection(macAddress)
        try {
            zebraPrinterConnection?.open()
        } catch (e: ConnectionException) {
            disconnect()
            throw PrinterException("CONNECT_EXCEPTION", e.message, e)
        }
        if (!isPrinterConnected()) return

        try {
            zebraPrinter = ZebraPrinterFactory.getInstance(zebraPrinterConnection!!)
            zebraLinkOsPrinter = ZebraPrinterFactory.createLinkOsPrinter(zebraPrinter!!)
            // Calibrate the printer
            zebraPrinter!!.calibrate()
            startListeningToPrinterStatus()
        } catch (e: ConnectionException) {
            disconnect()
            throw PrinterException("CONNECT_EXCEPTION", e.message, e)
        } catch (e: ZebraPrinterLanguageUnknownException) {
            disconnect()
            throw PrinterException("CONNECT_EXCEPTION", e.message, e)
        }
    }

    override fun isPrinterConnected(): Boolean {
        return zebraPrinterConnection != null && zebraPrinterConnection!!.isConnected
    }

    @SuppressLint("MissingPermission")
    override fun isBTPrinter(device: BluetoothDevice): Boolean {
        return (device.bluetoothClass.majorDeviceClass == BluetoothClass.Device.Major.UNCATEGORIZED || device.bluetoothClass.majorDeviceClass == BluetoothClass.Device.Major.IMAGING) && !device.name.startsWith("RS6100 ")
    }

    override fun disconnect() {
        timer?.cancel()
        timer = null
        if (zebraPrinterConnection != null) {
            zebraPrinterConnection?.close()
        }

        zebraPrinterConnection = null
        zebraPrinter = null
        setPrinterStatus(PrinterStatus(isConnected = false))
    }

    private fun getLabelData(lookupData: LookupData): ByteArray {
        SGD.SET("device.languages", "zpl", zebraPrinterConnection!!)

        val configLabel: ByteArray = when (lookupData.lookupResultType) {
            LookupResultType.routing -> getRoutingLabelData(lookupData)
            LookupResultType.crossDock -> getCrossDock(lookupData)
            LookupResultType.srr -> getRemediateLabelData(lookupData)
            LookupResultType.srrA -> getRemediateLabelData(lookupData)
            LookupResultType.srrR -> getRemediateLabelData(lookupData)
            LookupResultType.srrP -> getRemediateLabelData(lookupData)
            LookupResultType.misdirect -> getRemediateLabelData(lookupData)
            LookupResultType.remediation -> getRemediateLabelData(lookupData)
        }
        return configLabel
    }

    private fun getRoutingLabelData(lookupData: LookupData): ByteArray {

        return buildString {
            append("^XA^CFD") // Opening Tag
            append("^POI")
            append("^LH0,0")
            append("^FO0,0^GB576,180,5,,0^FS") //box
            append("^FO10,10^CFQ,21,10^FD ${lookupData.date}^FS") // Top Left - date
            append("^FO320,10^CFQ,21,10^FDPIN ${lookupData.pin}^FS") // Top Right - PIN
            append("^FO0,35^GB100,110,80^FS") //box
            append("^FO100,35^GB100,110,2^FS") //box
            append("^FO200,35^GB170,110,5^FS") //box with dark border
            append("^FO205,40^GB160,100,5^FS") //box with dark border
            append("^FO370,35^GB120,110,2^FS") //box
            append("^FO490,35^GB86,110,2^FS") //box
            append("^FO30,50^CFV,60,40^FR^FD${lookupData.pudroNumber ?: ""}^FS") //First box value - PUDRO NUMBER
            append("^FO130,50^CFV,60,40^FD${lookupData.conveyorSide ?: ""}^FS") //Second box value - CONVEYOR SIDE
            append("^FO235,50^AV,60,40^FD${lookupData.routeNumber ?: ""}^FS") //Third box value - ROUTE NUMBER
            append("^FO395,50^AV,60,40^FD${lookupData.shelfNumber ?: ""}^FS") //Fourth box value - SHELF NUMBER
            append("^FO505,100^CFT,36,20^FD${lookupData.sequenceNumber ?: ""}^FS") //Fifth box value - SEQUENCE NUMBER
            append("^FO10,148^CFQ,21,10^FD ${lookupData.terminalNumber}^FS") // Bottom Left - TERMINAL ID
            append("^FO400,148^CFQ,21,10^FD " + "${lookupData.routePlanId}" + "^FS")// Bottom Right - ROUTE PLAN VERSION
            append("^XZ") // Closing Tag
        }.toByteArray()
    }

    private fun getCrossDock(lookupData: LookupData): ByteArray {
        return getRoutingLabelData(lookupData)
    }

    private fun getRemediateLabelData(lookupData: LookupData): ByteArray {

        var remediationTextCommand = "^FO50,70^CFT,36,20^FDREMEDIATION/REMANIEMENT^FS"
        when (lookupData.lookupResultType) {
            LookupResultType.misdirect -> {
                remediationTextCommand = "^FO10,60^CFU,36,20^FDMISDIRECT/MALACHEMINE^FS"
            }
            LookupResultType.srr -> {
                remediationTextCommand =
                    "^FO150,60^CFU,36,20^FDSRR/SRE^FS"
            }
            LookupResultType.srrA -> {
                remediationTextCommand =
                    "^FO150,60^CFU,36,20^FDSRR/SRE - A^FS"
            }
            LookupResultType.srrR -> {
                remediationTextCommand =
                    "^FO150,60^CFU,36,20^FDSRR/SRE - R^FS"
            }
            LookupResultType.srrP -> {
                remediationTextCommand =
                    "^FO150,60^CFU,36,20^FDSRR/SRE - P^FS"
            }
            else -> {} // keep empty (mandatory to be present), we already default to Remediation Text
        }

        return buildString {
            append("^XA^CFD")
            append("^POI")
            append("^LH0,0")
            append("^FO0,0^GB576,180,5,,0^FS")//box
            append("^FO10,10^CFQ,21,10^FD${lookupData.date}^FS")
            append("^FO320,10^CFQ,21,10^FD${if (lookupData.pin.isEmpty()) "" else "PIN"} ${lookupData.pin}^FS")
            append("^FO0,35^GB576,0,2,^FS")//Line
            append(remediationTextCommand)
            append("^FO0,140^GB576,0,2,^FS")//Line
            append("^FO10,148^CFQ,21,10^FD ${lookupData.terminalNumber}^FS")
            append("^FO400,148^CFQ,21,10^FD " + "${lookupData.routePlanId}" + "^FS")
            append("^XZ")
        }.toByteArray()
    }

    override fun printLookupLabel(lookupData: LookupData) {
        if (zebraPrinter == null) return

        try {
            if (printerStatus.isReadyToPrint) {
                val configLabel: ByteArray = getLabelData(lookupData)
                zebraPrinterConnection!!.write(configLabel)
            } else if(printerStatus.isHeadOpen) {
                throw PrinterException("HEAD_OPEN", "Printer head is open", null)
            } else if(printerStatus.isPaperOut) {
                throw PrinterException("PAPER_OUT", "Printer is out of paper", null)
            }
        } catch (e: ConnectionException) {
            throw PrinterException("CONNECT_EXCEPTION", e.message, e)
        }
    }

    private fun getPrinterStatus(): PrinterStatus {
        try {
            val zebraPrinterStatus: com.zebra.sdk.printer.PrinterStatus =
                if (zebraLinkOsPrinter != null) zebraLinkOsPrinter!!.currentStatus else if (zebraPrinter != null) zebraPrinter!!.currentStatus else throw PrinterException(
                    "CONNECT_EXCEPTION",
                    "Printer not connected.",
                    null
                )
            return mapZebraPrinterStatusToPrinterStatus(zebraPrinterStatus)
        } catch (e: ConnectionException) {
            disconnect()
            throw PrinterException(
                "CONNECT_EXCEPTION",
                "Something went wrong getting the printer status.",
                null
            )
        }
    }

    private fun mapZebraPrinterStatusToPrinterStatus(zebraPrinterStatus: com.zebra.sdk.printer.PrinterStatus): PrinterStatus {
        return PrinterStatus(
            isHeadCold = zebraPrinterStatus.isHeadCold,
            isHeadOpen = zebraPrinterStatus.isHeadOpen,
            isPaperOut = zebraPrinterStatus.isPaperOut,
            isHeadTooHot = zebraPrinterStatus.isHeadTooHot,
            isPaused = zebraPrinterStatus.isPaused,
            isReadyToPrint = zebraPrinterStatus.isReadyToPrint,
            isRibbonOut = zebraPrinterStatus.isRibbonOut,
            isReceiveBufferFull = zebraPrinterStatus.isReceiveBufferFull,
            isPartialFormatInProgress = zebraPrinterStatus.isPartialFormatInProgress,
            isConnected = isPrinterConnected()
        )
    }

    private fun setPrinterStatus(printerStatus: PrinterStatus) {
        this.printerStatus = printerStatus
        onPrinterStatusChanged(printerStatus)
    }

}