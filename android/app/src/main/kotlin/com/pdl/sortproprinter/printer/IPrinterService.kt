package com.pdl.sortproprinter.printer

import android.bluetooth.BluetoothDevice
import com.pdl.sortproprinter.LookupData

typealias OnPrinterStatusChanged = (PrinterStatus) -> Unit
typealias OnPrinterStatusChangedError = (PrinterException) -> Unit

interface IPrinterService {

    val onPrinterStatusChanged: OnPrinterStatusChanged
    val onPrinterStatusChangedError: OnPrinterStatusChangedError

    fun connect(macAddress: String)
    fun isPrinterConnected() : Boolean
    fun isBTPrinter(device: BluetoothDevice) : Boolean
    fun disconnect()
    fun printLookupLabel(lookupData: LookupData)
}