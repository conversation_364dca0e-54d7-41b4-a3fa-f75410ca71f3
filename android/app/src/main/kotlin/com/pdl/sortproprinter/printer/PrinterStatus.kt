package com.pdl.sortproprinter.printer

import org.json.JSONObject

data class PrinterStatus(
    var isPartialFormatInProgress: Boolean = false,
    var isConnected: Boolean = false,
    var isReadyToPrint: Boolean = false,
    var isHeadCold: <PERSON>olean = false,
    var isHeadOpen: Boolean = false,
    var isHeadTooHot: Boolean = false,
    var isPaperOut: Boolean = false,
    var isRibbonOut: Boolean = false,
    var isReceiveBufferFull: Boolean = false,
    var isPaused: Boolean = false,
) {
    fun toJson(): JSONObject {
        return JSONObject(
            mapOf(
                "isPartialFormatInProgress" to this.isPartialFormatInProgress,
                "isConnected" to this.isConnected,
                "isReadyToPrint" to this.isReadyToPrint,
                "isHeadCold" to this.isHeadCold,
                "isHeadOpen" to this.isHeadOpen,
                "isHeadTooHot" to this.isHeadTooHot,
                "isPaperOut" to this.isPaperOut,
                "isRibbonOut" to this.isRibbonOut,
                "isReceiveBufferFull" to this.isReceiveBufferFull,
                "isPaused" to this.isPaused,
            )
        )
    }
}