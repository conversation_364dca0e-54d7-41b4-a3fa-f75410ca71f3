package com.pdl.sortproprinter

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle

class DwManager {

    var dwNotificationBr: BroadcastReceiver? = null

    companion object {
        // DW Profile Info
        private const val PROFILE_NAME = "SortProPrinter"
        private const val PROFILE_INTENT_ACTION = "com.pdl.sortproprinter.SCAN"
        private const val PROFILE_INTENT_BROADCAST = "2"

        private const val NOTIFICATION_ACTION = "com.symbol.datawedge.api.NOTIFICATION_ACTION"
        private const val NOTIFICATION_EXTRA = "com.symbol.datawedge.api.NOTIFICATION"
        private const val NOTIFICATION_TYPE_SCANNER_STATUS = "SCANNER_STATUS"

        const val DATAWEDGE_PACKAGE_NAME = "com.symbol.datawedge"
        const val DATAWEDGE_SEND_ACTION = "com.symbol.datawedge.api.ACTION"
        const val DATAWEDGE_RETURN_ACTION = "com.symbol.datawedge.api.RESULT_ACTION"
        const val DATAWEDGE_RETURN_CATEGORY = "android.intent.category.DEFAULT"
        const val DATAWEDGE_EXTRA_SEND_RESULT = "SEND_RESULT"
        const val DATAWEDGE_EXTRA_COMMAND_IDENTIFIER = "COMMAND_IDENTIFIER"
        const val DATAWEDGE_SCAN_EXTRA_DECODED_MODE = "com.symbol.datawedge.decoded_mode"
        const val DATAWEDGE_SCAN_EXTRA_BARCODES = "com.symbol.datawedge.barcodes"
        const val DATAWEDGE_SCAN_EXTRA_DATA_STRING = "com.symbol.datawedge.data_string"
        const val DATAWEDGE_SCAN_EXTRA_LABEL_TYPE = "com.symbol.datawedge.label_type"
        const val DATAWEDGE_SEND_CREATE_PROFILE = "com.symbol.datawedge.api.CREATE_PROFILE"
        const val DATAWEDGE_SEND_DELETE_PROFILE = "com.symbol.datawedge.api.DELETE_PROFILE"
        const val DATAWEDGE_SEND_SET_CONFIG = "com.symbol.datawedge.api.SET_CONFIG"
    }

    fun setOnScannerStatusChanged(context: Context, onScannerStatusChanged: (DwScannerStatus) -> Unit) {
        // first, send an intent to start receiving status notifications
        val b = Bundle()
        b.putString("com.symbol.datawedge.api.APPLICATION_NAME", context.packageName)
        b.putString("com.symbol.datawedge.api.NOTIFICATION_TYPE", NOTIFICATION_TYPE_SCANNER_STATUS)
        val i = Intent()
        i.action = "com.symbol.datawedge.api.ACTION"
        i.putExtra("com.symbol.datawedge.api.REGISTER_FOR_NOTIFICATION", b)
        context.sendBroadcast(i)

        // Create the broadcast receiver to receive the notifications
        if (dwNotificationBr != null) context.unregisterReceiver(dwNotificationBr!!)
        dwNotificationBr = createDwNotificationBroadcastReceiver(onScannerStatusChanged)
        val dwNotificationBrIntentFilter = IntentFilter()
        dwNotificationBrIntentFilter.addAction(NOTIFICATION_ACTION)
        context.registerReceiver(dwNotificationBr!!, dwNotificationBrIntentFilter)
    }

    /** Set up the Profile **/
    fun configureProfile(context: Context) {
        var dwIntent = Intent()
        val profileConfig = Bundle()
        profileConfig.putString("PROFILE_NAME", PROFILE_NAME)
        profileConfig.putString("PROFILE_ENABLED", "true") //  These are all strings
        profileConfig.putString("CONFIG_MODE", "CREATE_IF_NOT_EXIST")

        val barcodeConfig = getBarcodePluginConfig()
        profileConfig.putBundle("PLUGIN_CONFIG", barcodeConfig)

        val appConfig = Bundle()
        appConfig.putString("PACKAGE_NAME", context.packageName) //  Associate the profile with this app
        appConfig.putStringArray("ACTIVITY_LIST", arrayOf("*"))
        profileConfig.putParcelableArray("APP_LIST", arrayOf(appConfig))

        dwIntent = Intent()
        dwIntent.action = DATAWEDGE_SEND_ACTION
        dwIntent.putExtra(DATAWEDGE_SEND_SET_CONFIG, profileConfig)
        context.sendBroadcast(dwIntent)

        //  You can only configure one plugin at a time in some versions of DW, now do the intent output
        profileConfig.remove("PLUGIN_CONFIG")
        val intentConfig = Bundle()
        intentConfig.putString("PLUGIN_NAME", "INTENT")
        intentConfig.putString("RESET_CONFIG", "true")
        val intentProps = Bundle()
        intentProps.putString("intent_output_enabled", "true")
        intentProps.putString("intent_action", PROFILE_INTENT_ACTION)
        intentProps.putString("intent_delivery", PROFILE_INTENT_BROADCAST)  //  "2"
        intentConfig.putBundle("PARAM_LIST", intentProps)
        profileConfig.putBundle("PLUGIN_CONFIG", intentConfig)

        dwIntent = Intent()
        dwIntent.action = DATAWEDGE_SEND_ACTION
        dwIntent.putExtra(DATAWEDGE_SEND_SET_CONFIG, profileConfig)
        context.sendBroadcast(dwIntent)

        //  You can only configure one plugin at a time in some versions of DW, now do the keystroke output
        // We need to remove keystroke output because this sends the data to the software keyboard of the device, so if
        // you are focused on a textfield, scanning a barcode would input its raw data within it.
        // some info here https://stackoverflow.com/a/59904766/7776801
        profileConfig.remove("PLUGIN_CONFIG")
        val bConfig = Bundle()
        bConfig.putString("PLUGIN_NAME", "KEYSTROKE")
        val bParams = Bundle()
        bParams.putString("keystroke_output_enabled", "false")
        bConfig.putBundle("PARAM_LIST", bParams)
        profileConfig.putBundle("PLUGIN_CONFIG", bConfig)

        dwIntent = Intent()
        dwIntent.action = DATAWEDGE_SEND_ACTION
        dwIntent.putExtra(DATAWEDGE_SEND_SET_CONFIG, profileConfig)
        context.sendBroadcast(dwIntent)
    }

    /** Create Profile **/
    fun createProfile(context: Context) {
        deleteProfile(context)
        var dwIntent = Intent()
        dwIntent.action = DATAWEDGE_SEND_ACTION
        dwIntent.putExtra(DATAWEDGE_SEND_CREATE_PROFILE, PROFILE_NAME)

        configureProfile(context)
    }

    fun playFingerScannerFeedback(context: Context, pattern: IntArray) {
        val i = createFeedbackIntent(pattern)
        context.sendBroadcast(i)
    }

    fun dispose(context: Context) {
        if (dwNotificationBr != null) context.unregisterReceiver(dwNotificationBr!!)
    }

    // Common stuff that the SetConfig intent always needs
    private fun createSetConfigIntent(bMain: Bundle): Intent {
        bMain.putString("PROFILE_NAME", PROFILE_NAME) //Specify the profile name
        bMain.putString("PROFILE_ENABLED", "true") //Enable the profile
        bMain.putString("CONFIG_MODE", "UPDATE")
        val iSetConfig = Intent()
        iSetConfig.action = DATAWEDGE_SEND_ACTION
        iSetConfig.setPackage(DATAWEDGE_PACKAGE_NAME)
        iSetConfig.putExtra(DATAWEDGE_SEND_SET_CONFIG, bMain)
        iSetConfig.putExtra(DATAWEDGE_EXTRA_SEND_RESULT, "COMPLETE_RESULT")
        return iSetConfig
    }

    private fun createDwNotificationBroadcastReceiver(onScannerStatusChanged: (DwScannerStatus) -> Unit): BroadcastReceiver {
        return object : BroadcastReceiver() {

            override fun onReceive(context: Context?, intent: Intent) {
                intent.extras?.let {
                    val notificationData = it.getBundle(NOTIFICATION_EXTRA)
                    val notificationType = notificationData?.getString("NOTIFICATION_TYPE")
                    if (notificationType == NOTIFICATION_TYPE_SCANNER_STATUS) {
                        // We only care about our profile
                        val profileName = notificationData.getString("PROFILE_NAME")

                        if (profileName != PROFILE_NAME) return

                        // WAITING, SCANNING, CONNECTED, DISCONNECTED, IDLE, DISABLED,
                        val scannerStatus = notificationData.getString("STATUS")
                        // INTERNAL_IMAGER, BLUETOOTH_ZEBRA
                        val scannerIdentifier = notificationData.getString("SCANNER_IDENTIFIER")
                        onScannerStatusChanged(DwScannerStatus(status = scannerStatus ?: "", identifier = scannerIdentifier ?: ""))
                    }
                }

            }
        }
    }

    /** Delete Profile **/
    private fun deleteProfile(context: Context) {
        val dwIntent = Intent()
        dwIntent.action = DATAWEDGE_SEND_ACTION
        dwIntent.putExtra(DATAWEDGE_SEND_DELETE_PROFILE, arrayOf(PROFILE_NAME))
        context.sendBroadcast(dwIntent)
    }

    private fun getBarcodePluginConfig(): Bundle {
        val barcodeConfig = Bundle()
        barcodeConfig.putString("PLUGIN_NAME", "BARCODE")
        barcodeConfig.putString("RESET_CONFIG", "true")
        val barcodeProps = Bundle()
        barcodeProps.putString("scanner_input_enabled", "true")
        barcodeProps.putString("configure_all_scanners", "true") // doesn't seem to work but we'll still keep it
        barcodeProps.putString("scanner_selection", "auto")
        barcodeProps.putString("charset_name", "ISO-8859-1")
        barcodeProps.putString("connection_idle_time", "0") // Don't let the finger scanner go idle
        barcodeProps.putString("auto_switch_to_default_on_event", "3") //0 - Disabled 1 - On connect 2 - On disconnect 3 - On connect/disconnect
        barcodeConfig.putBundle("PARAM_LIST", barcodeProps)
        return barcodeConfig
    }

    private fun createFeedbackIntent(pattern: IntArray) : Intent {
        val i = Intent()
        val bundleNotify = Bundle()
        val bundleNotificationConfig = Bundle()
        i.action = "com.symbol.datawedge.api.ACTION"
        bundleNotificationConfig.putString("DEVICE_IDENTIFIER", "BLUETOOTH_GENERIC")
        bundleNotificationConfig.putIntArray("NOTIFICATION_SETTINGS", pattern)
        bundleNotify.putBundle("NOTIFICATION_CONFIG", bundleNotificationConfig)
        i.putExtra("com.symbol.datawedge.api.notification.NOTIFY", bundleNotify)
        return i
    }

}