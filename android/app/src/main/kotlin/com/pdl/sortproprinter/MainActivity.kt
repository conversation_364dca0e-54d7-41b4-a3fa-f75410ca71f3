package com.pdl.sortproprinter

import android.app.PendingIntent
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.nfc.NdefMessage
import android.nfc.NfcAdapter
import android.nfc.Tag
import android.nfc.tech.Ndef
import android.os.Environment
import com.pdl.sortproprinter.bluetooth.BluetoothService
import com.pdl.sortproprinter.printer.IPrinterService
import com.pdl.sortproprinter.printer.PrinterException
import com.pdl.sortproprinter.printer.ZebraPrinterInfo
import com.pdl.sortproprinter.printer.ZebraPrinterService
import com.zebra.deviceidentifierswrapper.IDIResultCallbacks
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.EventChannel
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugins.GeneratedPluginRegistrant
import org.json.JSONObject
import java.io.BufferedReader
import java.io.File
import java.io.FileReader
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Timer
import kotlin.concurrent.schedule
import kotlinx.coroutines.*

class MainActivity : FlutterActivity() {

    companion object {
        // Method Channels
        private const val COMMAND_CHANNEL = "com.pdl.sortproprinter/command"
        private const val PRINTER_CHANNEL = "com.pdl.sortproprinter/printer"
        private const val NFC_CHANNEL = "com.pdl.sortproprinter/nfc"

        // Event Channels
        private const val PRINTER_INFO_CHANNEL = "com.pdl.sortproprinter/printer/info"
        private const val PRINTER_STATUS_CHANNEL = "com.pdl.sortproprinter/printer/status"
        private const val RING_SCANNER_STATUS_CHANNEL = "com.pdl.sortproprinter/ring-scanner/status"
        private const val SCANNER_TRIGGERED_CHANNEL = "com.pdl.sortproprinter/scanner/triggered"
        private const val SCAN_CHANNEL = "com.pdl.sortproprinter/scan"

        // DW Profile Info
        private const val PROFILE_INTENT_ACTION = "com.pdl.sortproprinter.SCAN"

        // Access Shared storage constants
        private const val TERMINALS_FILE_NAME = "terminals.csv"
    }
    private val mainScope = CoroutineScope(Dispatchers.IO)

    private val dwManager: DwManager = DwManager()
    private var currentRingScannerStatus: String = "DISCONNECTED"
    private var isScanning = false

    private lateinit var printerService: IPrinterService
    private val bluetoothService = BluetoothService(this)

    private var nAdapter: NfcAdapter? = null
    private var foregroundNfcDispatchEnabled: Boolean = false
    private var dataWedgeBroadcastReceiver: BroadcastReceiver? = null

    // Event Channel to get the printer's Mac Address
    private var printerInfoEventChannelSink: EventChannel.EventSink? = null

    // Event Channel to get the printer's status
    private var printerStatusEventChannelSink: EventChannel.EventSink? = null

    // Event Channel to get the finger scanner's connection status
    private var ringScannerStatusEventChannelSink: EventChannel.EventSink? = null

    // Event Channel to get the finger scanner's connection status
    private var scannerTriggeredEventChannelSink: EventChannel.EventSink? = null

    // Handle receiving the NFC Tag intent from the Zebra Printer
    private fun handleNfcIntent(intent: Intent, detectedTag: Tag) {
        var ndefMessages: Array<NdefMessage?>? = null
        val ndef = Ndef.get(detectedTag)
        try {
            val messages = intent.getParcelableArrayExtra(NfcAdapter.EXTRA_NDEF_MESSAGES)
            if (messages != null) {
                ndefMessages = arrayOfNulls(messages.size)
                for (i in messages.indices) { //   Records NDEF message
                    ndefMessages[i] = messages[i] as NdefMessage
                }
            }
            val record = ndefMessages!![0]!!.records[0] // Retrieves NDEF record
            val payload = record.payload //  Retrieves byte payload from record
            val nfcData = String(payload) //  Converts payload into String format

            val zebraPrinterInfo = ZebraPrinterInfo.fromNfcTagData(nfcData)
            printerInfoEventChannelSink?.success(zebraPrinterInfo.macAddress)
            ndef.close() //  Closes connection
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override fun onNewIntent(intent: Intent) {
        setIntent(intent)
        if (getIntent().action == NfcAdapter.ACTION_TAG_DISCOVERED) {
            val detectedTag = intent.getParcelableExtra<Tag>(NfcAdapter.EXTRA_TAG)
            detectedTag?.let { handleNfcIntent(getIntent(), it) } //   Process NFC data
        }
    }

    private fun setupForegroundDispatch() {
        val pendingIntent: PendingIntent = PendingIntent.getActivity(
            this, 0,
            Intent(this, javaClass).addFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP), PendingIntent.FLAG_UPDATE_CURRENT
        )
        val tagDetected = IntentFilter(NfcAdapter.ACTION_TAG_DISCOVERED)
        val filter2 = IntentFilter(NfcAdapter.ACTION_NDEF_DISCOVERED)
        val filters = arrayOf(tagDetected, filter2)
        nAdapter?.enableForegroundDispatch(this, pendingIntent, filters, null)
    }

    //  Disables foreground dispatch
    private fun stopForegroundDispatch() {
        nAdapter?.disableForegroundDispatch(this)
    }

    override fun onResume() {
        super.onResume()
        if (foregroundNfcDispatchEnabled) setupForegroundDispatch()
    }

    /**
     * onPause disables Foreground Dispatch when the application is paused, allowing NFC use with other apps
     */
    override fun onPause() {
        super.onPause()
        stopForegroundDispatch()
    }

    override fun onDestroy() {
        super.onDestroy()
        unregisterReceiver(dataWedgeBroadcastReceiver!!)
        dataWedgeBroadcastReceiver = null

        dwManager.dispose(this)
        mainScope.cancel()  // Cancel the scope when the activity is destroyed
    }

    private fun notifyScannerStatusChanged(scannerStatus: String) {
        currentRingScannerStatus = scannerStatus
        ringScannerStatusEventChannelSink?.success(scannerStatus)
    }

    private fun notifyScannerTriggered() {
        scannerTriggeredEventChannelSink?.success(true)
    }

    private fun readFileFromSharedStorage() : String {
        // Ensure that external storage is available
        if (isExternalStorageReadable()) {
            val file = File(
                Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS),
                TERMINALS_FILE_NAME
            )
            try {
                val br = BufferedReader(FileReader(file))
                val text = StringBuilder()
                var line: String?
                while (br.readLine().also { line = it } != null) {
                    text.append(line)
                    text.append('\n')
                }
                br.close()
                // Process the text read from the file
                return text.toString()
            } catch (e: IOException) {
                throw e
            }
        } else {
            throw Exception("External storage not readable")
        }
    }

    private fun isExternalStorageReadable(): Boolean {
        val state = Environment.getExternalStorageState()
        return Environment.MEDIA_MOUNTED == state ||
                Environment.MEDIA_MOUNTED_READ_ONLY == state
    }


    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        GeneratedPluginRegistrant.registerWith(flutterEngine)
        nAdapter = NfcAdapter.getDefaultAdapter(this)

        printerService = ZebraPrinterService({
            runOnUiThread {
                printerStatusEventChannelSink?.success(it.toJson().toString())
            }
        }) {
            runOnUiThread {
                printerStatusEventChannelSink?.error("CONNECT_EXCEPTION", it.message, null)
            }
        }

        dwManager.createProfile(this)


        // Start listening to the bounded BT devices
        // Set up notifications for Scanner Status
        var notifyScannerStatusChangedTimer: Timer? = null
        dwManager.setOnScannerStatusChanged(context) {

            // notify when any scanner connected is triggered
            if (it.status == "SCANNING") {
                // because we continuously receive events, we might get the scanning status multiple times in a row
                // this prevents notifying more than once per trigger
                if (!isScanning) {
                    notifyScannerTriggered()
                    isScanning = true
                }
                return@setOnScannerStatusChanged
            } else {
                isScanning = false
            }

            // We currently only care about listening the status of the Bluetooth connected scanner
            if (it.identifier == "BLUETOOTH_ZEBRA") {
                var delay: Long = 0
                // we only care about whether is connected, waiting, disconnected or disabled
                if (it.status == "CONNECTED" || it.status == "WAITING" || it.status == "DISABLED" || it.status == "DISCONNECTED") {

                    // re-configure the profile whenever we connect the Finger scanner
                    if (it.status == "CONNECTED") {
                        dwManager.configureProfile(context)
                    }

                    // DW puts the Bluetooth Scanner in DISABLED mode when switching modes such as from single barcode to multi-barcode
                    // This Delay adds a little bit of room so that we don't display disconnected unnecessarily when that happens
                    if (it.status == "DISABLED" || it.status == "DISCONNECTED") {
                        delay = 2000
                    }

                    if (notifyScannerStatusChangedTimer != null) {
                        notifyScannerStatusChangedTimer!!.cancel()
                    }
                    notifyScannerStatusChangedTimer = Timer("notifyStatusChange", false)
                    notifyScannerStatusChangedTimer!!.schedule(delay) {
                        runOnUiThread {
                            notifyScannerStatusChanged(it.status)
                        }
                    }
                }
            }
        }

        // Event Channel is meant to communicate things back to the Flutter Code via a Stream (Queue)
        EventChannel(flutterEngine.dartExecutor, SCAN_CHANNEL).setStreamHandler(
            object : EventChannel.StreamHandler {
                override fun onListen(arguments: Any?, events: EventChannel.EventSink?) {
                    dataWedgeBroadcastReceiver = createDataWedgeBroadcastReceiver(events)
                    val intentFilter = IntentFilter()
                    intentFilter.addAction(PROFILE_INTENT_ACTION)
                    intentFilter.addAction(DwManager.DATAWEDGE_RETURN_ACTION)
                    intentFilter.addCategory(DwManager.DATAWEDGE_RETURN_CATEGORY)
                    registerReceiver(dataWedgeBroadcastReceiver, intentFilter)
                }

                override fun onCancel(arguments: Any?) {
                    unregisterReceiver(dataWedgeBroadcastReceiver)
                    dataWedgeBroadcastReceiver = null
                }
            }
        )
        EventChannel(flutterEngine.dartExecutor, PRINTER_INFO_CHANNEL).setStreamHandler(
            object : EventChannel.StreamHandler {
                override fun onListen(arguments: Any?, events: EventChannel.EventSink?) {
                    printerInfoEventChannelSink = events
                }

                override fun onCancel(arguments: Any?) {
                    printerInfoEventChannelSink = null
                }
            }
        )
        EventChannel(flutterEngine.dartExecutor, PRINTER_STATUS_CHANNEL).setStreamHandler(
            object : EventChannel.StreamHandler {
                override fun onListen(arguments: Any?, events: EventChannel.EventSink?) {
                    printerStatusEventChannelSink = events
                }

                override fun onCancel(arguments: Any?) {
                    printerStatusEventChannelSink = null
                }
            }
        )
        EventChannel(flutterEngine.dartExecutor, RING_SCANNER_STATUS_CHANNEL).setStreamHandler(
            object : EventChannel.StreamHandler {
                override fun onListen(arguments: Any?, events: EventChannel.EventSink?) {
                    ringScannerStatusEventChannelSink = events
                }

                override fun onCancel(arguments: Any?) {
                    ringScannerStatusEventChannelSink = null
                }
            }
        )
        EventChannel(flutterEngine.dartExecutor, SCANNER_TRIGGERED_CHANNEL).setStreamHandler(
            object : EventChannel.StreamHandler {
                override fun onListen(arguments: Any?, events: EventChannel.EventSink?) {
                    scannerTriggeredEventChannelSink = events
                }

                override fun onCancel(arguments: Any?) {
                    scannerTriggeredEventChannelSink = null
                }
            }
        )


        // Method channel is meant to use to receive commands from the Flutter Code
        MethodChannel(
            flutterEngine.dartExecutor,
            COMMAND_CHANNEL
        ).setMethodCallHandler { call, result ->
            when (call.method) {
                "getCurrentRingScannerStatus" -> {
                    result.success(currentRingScannerStatus)
                }

                "playFeedback" -> {
                    playFingerScannerFeedback(call.arguments.toString().split(" ").map { str -> str.toInt() }.toIntArray())
                    result.success(null)
                }

                "getSerialNumber" -> {
                    ZebraUtil.getSerialNumber(context, object : IDIResultCallbacks {
                        override fun onSuccess(message: String) {
                            runOnUiThread {
                                result.success(message)
                            }
                        }

                        override fun onError(message: String) {
                            runOnUiThread {
                                result.error("SERIAL_NUMBER_EXCEPTION", message, null)
                            }
                        }

                        override fun onDebugStatus(message: String) {}
                    })
                }
                "getTerminalsCsvContent" -> {
                    try {
                        val content = readFileFromSharedStorage()
                        result.success(content)
                    } catch (e: Exception) {
                        result.error("TERMINAL_FILE_EXCEPTION", e.message, null)
                    }
                }
            }
        }
        MethodChannel(
            flutterEngine.dartExecutor,
            PRINTER_CHANNEL
        ).setMethodCallHandler { call, result ->
            when (call.method) {
                "connectPrinter" -> {
                    mainScope.launch(Dispatchers.IO) {
                        try {
                            printerService.connect(macAddress = call.arguments.toString())
                            result.success(true)
                        } catch (e: PrinterException) {
                            result.error("CONNECT_EXCEPTION", e.message, null)
                        }
                    }
                }
                "getPairedPrinterMacAddress" -> {
                    val pairedDevices = bluetoothService.fetchBoundedDevices()
                    val printer = pairedDevices.find { printerService.isBTPrinter(it) }
                    result.success(printer?.address)
                }
                "isPrinterConnected" -> {
                    val printerConnected = printerService.isPrinterConnected()
                    result.success(printerConnected)
                }

                "disconnectPrinter" -> {
                    mainScope.launch(Dispatchers.IO) {
                        printerService.disconnect()
                        result.success(true)
                    }
                }

                "printLabel" -> {
                    try {
                        printerService.printLookupLabel(
                            LookupData.fromJsonObject(
                                JSONObject(
                                    call.arguments.toString()
                                )
                            )
                        )
                        result.success(true)
                    } catch (e: PrinterException) {
                        result.error(e.code, e.message, null)
                    }
                }

                else -> {
                    result.notImplemented()
                }
            }
        }
        MethodChannel(
            flutterEngine.dartExecutor,
            NFC_CHANNEL
        ).setMethodCallHandler { call, result ->
            when (call.method) {
                "enableForegroundNfc" -> {
                    foregroundNfcDispatchEnabled = true
                    setupForegroundDispatch()
                    result.success(true)
                }

                "disableForegroundNfc" -> {
                    foregroundNfcDispatchEnabled = false
                    stopForegroundDispatch()
                    result.success(true)
                }

                else -> {
                    result.notImplemented()
                }
            }
        }
    }

    private fun playFingerScannerFeedback(pattern: IntArray) {
        dwManager.playFingerScannerFeedback(context, pattern)
    }

    // This initializes a broadcast receiver for events coming from DataWedge (Such as Scans)
    private fun createDataWedgeBroadcastReceiver(events: EventChannel.EventSink?): BroadcastReceiver {
        return object : BroadcastReceiver() {

            override fun onReceive(context: Context, intent: Intent) {
                val date = Calendar.getInstance().time
                val df = SimpleDateFormat("dd/MM/yyyy HH:mm:ss")
                val dateTimeString = df.format(date)

                //  A barcode has been scanned
                if (intent.action.equals(PROFILE_INTENT_ACTION)) {
                    val scanData =
                        intent.getStringExtra(DwManager.DATAWEDGE_SCAN_EXTRA_DATA_STRING)
                            ?: "N/A"
                    val symbology =
                        intent.getStringExtra(DwManager.DATAWEDGE_SCAN_EXTRA_LABEL_TYPE)
                            ?: "N/A"
                    val currentScan = Scan(scanData, symbology, dateTimeString)
                    events?.success(currentScan.toJson().toString())
                }

            }
        }
    }
}
