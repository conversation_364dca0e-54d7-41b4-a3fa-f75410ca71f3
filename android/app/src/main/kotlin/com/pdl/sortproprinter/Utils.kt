package com.pdl.sortproprinter

import android.os.Bundle
import android.util.Log

class Utils {
    companion object {
        fun printBundle(
            bundle: Bundle,
            nestLevel: Int = 0
        ) {
            val newLevel = nestLevel+1
            for (key in bundle.keySet()) {
                val spaces = buildString {
                    repeat(newLevel) {
                        append("    ")
                    }
                }
                Log.e("PRINT_BUNDLE_$nestLevel", "$spaces$key: ${if (bundle[key] != null) bundle[key] else "NULL"}")

                if(key.equals("RESULT_CODE")) {
                    Log.e("PRINT_BUNDLE", "YEEET")
                }

                if (bundle[key] is Bundle) {
                    printBundle(bundle[key] as Bundle, newLevel)
                }
                if (bundle[key] is Iterable<*>) {
                    (bundle[key] as Iterable<*>).forEach {
                        if (it is Bundle) {
                            printBundle(it, newLevel)
                        }
                    }
                }
            }
        }

    }
}