package com.pdl.sortproprinter.printer

data class ZebraPrinterInfo(
    val macAddress: String,
    val serialNumber: String,
    val wifiAddress: String,
) {
    companion object {
        fun fromNfcTagData(nfcData: String) : ZebraPrinterInfo {
            var index = 0
            index = nfcData.indexOf("&mB=") // Index is set to the Bluetooth MAC Address identifier
            val nfcMacAddress = nfcData.substring(
                index + 4,
                index + 16
            ) //  Creates a substring containing only the MAC Address information
            index = nfcData.indexOf("&s") // Index is set to the Serial Number identifier
            val nfcSerialName = nfcData.substring(
                index + 3,
                index + 17
            ) // Creates a substring containg only the Serial Number
            index = nfcData.indexOf("&mW=")
            val nfcWifiAddress = nfcData.substring(
                index + 3,
                index + 16
            ) // Creates a substring containing only the WIFI MAC Address information
            return ZebraPrinterInfo(macAddress = nfcMacAddress, serialNumber = nfcSerialName, wifiAddress = nfcWifiAddress)
        }
    }
}
