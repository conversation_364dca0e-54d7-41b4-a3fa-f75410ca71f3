package com.pdl.sortproprinter

import org.json.JSONObject;

// Represents the Scan Data from a Zebra Scanner's Scan action
data class Scan(private val data: String, private val symbology: String, private val dateTime: String)
{
    fun toJson(): JSONObject{
        return JSONObject(mapOf(
            "scanData" to this.data,
            "symbology" to this.symbology,
            "dateTime" to this.dateTime
        ))
    }
}

