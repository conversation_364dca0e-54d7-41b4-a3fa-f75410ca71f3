flowchart TD
    %% Main Flow
    Start([Start]) --> A[Launch Application]
    A --> B{Check Authentication Status}
    
    %% Authentication Flow
    B -->|Not Authenticated| C[Display Authentication Screen]
    C --> C1[Scan Employee ID/Badge]
    C1 --> C2{Validate Employee Info}
    C2 -->|Failed| C3[Display Error Message]
    C3 --> C1
    C2 -->|Success| C4[Save Employee Information]
    C4 --> D
    
    B -->|Authenticated| D[Display Main Interface]
    
    %% Environment Selection
    D --> E[Check Current Environment]
    
    %% Development Environment
    E -->|Development| DEV[Development Environment Config]
    DEV --> DEV1[Connect to Dev Server]
    DEV1 --> DEV2[Enable Debug Logging]
    DEV2 --> F
    
    %% QA Environment
    E -->|QA| QA[QA Environment Config]
    QA --> QA1[Connect to QA Server]
    QA1 --> QA2[Enable Verbose Logging]
    QA2 --> F
    
    %% Production Environment
    E -->|Production| PROD[Production Environment Config]
    PROD --> PROD1[Connect to Production Server]
    PROD1 --> PROD2[Enable Error Logging Only]
    PROD2 --> F
    
    %% Scanning Flow
    F[Wait for Package Scan] --> G[Scan Package Barcode]
    G --> G1{Check for Duplicate Scan}
    G1 -->|Yes| G2[Skip Processing]
    G2 --> F
    G1 -->|No| G3[Record to Recent Scans]
    G3 --> H[Parse Barcode Data]
    
    %% Query Flow - Local Database
    H --> I[Start Query Process]
    I --> I1[Extract PIN and Postal Code]
    I1 --> J[Query PIN Database]
    
    J -->|Found| K[Generate Routing Label]
    J -->|Not Found| L[Query PrePrint Database]
    
    L -->|Found| K
    L -->|Not Found| M[Query Postal Code]
    
    M -->|Found| K
    M -->|Not Found| N{Is HFPU?}
    
    N -->|Yes| O[Query HFPU Database]
    N -->|No| P
    
    O -->|Found| K
    O -->|Not Found| P
    
    %% Query Flow - External Services
    P[SSLWS Network Query] --> P1{Network Connection Status}
    P1 -->|Connected| P2[Send SSLWS Request]
    P1 -->|Not Connected| P5[Log Connection Error]
    
    P2 --> P3{Process Response}
    P3 -->|Success| P4{Result Status Code}
    P3 -->|Failure/Timeout| P5
    
    P4 -->|Resolved| K
    P4 -->|CD| CD[Generate Cross-Dock Label]
    P4 -->|SRR| SRR[Generate SRR Label]
    P4 -->|MDR| MDR[Generate Misdirect Label]
    P4 -->|Error/REM| P5
    
    P5 --> Q[HVR Query]
    
    Q --> Q1{Check Service Priority}
    Q1 --> Q2[Query RPM Database]
    Q2 --> Q3{Query Results}
    Q3 -->|Found| Q4[Query ParkingPlan Database]
    Q3 -->|Not Found| R
    
    Q4 -->|Found| K
    Q4 -->|Not Found| R
    
    %% Address Parsing Query
    R[Address Parsing Query] --> R1[Parse Address Components]
    R1 --> R2[Query RPM Database]
    R2 --> R3{Query Results}
    R3 -->|Found| K
    R3 -->|Not Found| S[Generate Remediation Label]
    
    %% Label Generation and Printing
    K --> T[Prepare for Printing]
    CD --> T
    SRR --> T
    MDR --> T
    S --> T
    
    T --> T1{Printer Connection Status}
    T1 -->|Not Connected| T2[Display Connect Printer Prompt]
    T2 --> T3[Connect to Printer]
    T3 --> T4
    
    T1 -->|Connected| T4[Send Print Command]
    T4 --> T5{Print Result}
    T5 -->|Success| T6[Display Print Success]
    T5 -->|Failure| T7[Display Print Error]
    T7 --> T8[Retry Printing]
    T8 --> T4
    
    T6 --> U[Record Scan Result]
    
    %% Result Recording and Synchronization
    U --> U1[Save to Local Database]
    U1 --> U2{Network Connection Status}
    U2 -->|Connected| U3[Sync to Server]
    U2 -->|Not Connected| U4[Add to Sync Queue]
    
    U3 --> V[Return to Wait for Scan]
    U4 --> V
    
    V --> F
    
    %% Session Management
    subgraph "Session Management"
    SM1[Monitor User Activity]
    SM2{Check Timeout}
    SM3[Display Timeout Warning]
    SM4[Auto Logout]
    
    SM1 --> SM2
    SM2 -->|About to Timeout| SM3
    SM3 -->|No Activity| SM4
    SM3 -->|Activity Detected| SM1
    SM2 -->|Activity Normal| SM1
    SM4 --> C
    end
    
    %% Error Handling
    subgraph "Error Handling"
    E1[Catch Exception]
    E2[Log Error]
    E3{Error Type}
    E4[Display User-Friendly Error]
    E5[Handle Silently]
    E6[Retry Operation]
    
    E1 --> E2
    E2 --> E3
    E3 -->|User-Visible| E4
    E3 -->|Background| E5
    E3 -->|Recoverable| E6
    E6 --> F
    E4 --> F
    E5 --> F
    end
    
    %% Data Synchronization
    subgraph "Data Synchronization"
    DS1[Check for Data Updates]
    DS2{Updates Needed?}
    DS3[Download Latest Data]
    DS4[Update Local Database]
    
    DS1 --> DS2
    DS2 -->|Yes| DS3
    DS2 -->|No| DS1
    DS3 --> DS4
    DS4 --> DS1
    end
    
    %% Environment Styling
    classDef devEnv fill:#f9f,stroke:#333,stroke-width:2px
    classDef qaEnv fill:#bbf,stroke:#333,stroke-width:2px
    classDef prodEnv fill:#bfb,stroke:#333,stroke-width:2px
    
    class DEV,DEV1,DEV2 devEnv
    class QA,QA1,QA2 qaEnv
    class PROD,PROD1,PROD2 prodEnv
    
    %% Process Type Styling
    classDef auth fill:#ffcc99,stroke:#333
    classDef scan fill:#ccffcc,stroke:#333
    classDef query fill:#ccccff,stroke:#333
    classDef print fill:#ffffcc,stroke:#333
    classDef error fill:#ffcccc,stroke:#333
    
    class C,C1,C2,C3,C4 auth
    class G,G1,G2,G3,H scan
    class I,I1,J,K,L,M,N,O,P,P1,P2,P3,P4,P5,Q,Q1,Q2,Q3,Q4,R,R1,R2,R3,S query
    class T,T1,T2,T3,T4,T5,T6,T7,T8 print
    class E1,E2,E3,E4,E5,E6 error