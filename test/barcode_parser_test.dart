import 'package:flutter_test/flutter_test.dart';
import 'package:sort_pro_printer_flutter/app/scanner/data/models/barcode.dart';
import 'package:sort_pro_printer_flutter/app/scanner/data/models/barcode_data.dart';
import 'package:sort_pro_printer_flutter/utils/barcode_parser.dart';

void main() {
  group('Barcode Parser tests', () {
    /// Emp Badge
    test('Should validate the extracted data from the Employee Badge', () {
      // Arrange
      const rawData = "60123456";
      const barcodeSymbology = BarcodeSymbology.code39;
      const barcode = Barcode(rawData, barcodeSymbology);
      const expectedResult = BarcodeData(employeeId: rawData);

      // Act
      final result = BarcodeParser.decodeBarcode(barcode);

      // Assert
      expect(result, expectedResult);
    });

    /// NGB
    test('Should validate the extracted data from the NGB barcode', () {
      // Arrange
      const rawData = "1201410809433415775629000000102120";
      const barcodeSymbology = BarcodeSymbology.code128;
      const barcode = Barcode(rawData, barcodeSymbology);
      const expectedResult = BarcodeData(
        postalCode: "L0N1H0",
        pinNumber: "334157756290",
        deliveryTime: "00",
        shipmentType: "0",
        deliveryType: "1",
        diversionCode: "0",
        isDiversionCodeFromBarcode: true,
        isDeliveryTimeFromBarcode: true,
      );

      // Act
      final result = BarcodeParser.decodeBarcode(barcode);

      // Assert
      expect(result, expectedResult);
    });

    /// Legacy Puro Barcode
    test('Should validate the extracted data from the Legacy Puro barcode', () {
      // Arrange
      const rawData = "V12345678901";
      const barcodeSymbology = BarcodeSymbology.code39;
      const barcode = Barcode(rawData, barcodeSymbology);
      final expectedResult = BarcodeData(pinNumber: rawData.substring(1));

      // Act
      final result = BarcodeParser.decodeBarcode(barcode);

      // Assert
      expect(result, expectedResult);
    });

    /// Manual Way Bill
    test('Should validate the extracted data from the Manual Way Bill barcode', () {
      // Arrange
      const rawData = "A12345678901";
      const barcodeSymbology = BarcodeSymbology.code128;
      const barcode = Barcode(rawData, barcodeSymbology);
      const expectedResult = BarcodeData(pinNumber: "12345678901");

      // Act
      final result = BarcodeParser.decodeBarcode(barcode);

      // Assert
      expect(result, expectedResult);
    });

    /// UPS 1Z
    test('Should validate the extracted data from the UPS 1Z barcode', () {
      // Arrange
      const rawData = "1Z123X56A112345671";
      const barcodeSymbology = BarcodeSymbology.unknown;
      const barcode = Barcode(rawData, barcodeSymbology);
      const expectedResult = BarcodeData(pinNumber: "1Z123X56A112345671");

      // Act
      final result = BarcodeParser.decodeBarcode(barcode);

      // Assert
      expect(result, expectedResult);
    });

    /// 2D Puro
    test('Should validate the extracted data from the Puro 2D barcode', () {
      // Arrange
      const rawData =
          "V01~A01|D01~T5J3S4|R01~ACME WIDGET SUPPLY|R02~SUITE 200|R03~5995|R04~AVEBURY DRIVE|R05~|R06~MISSISSAUGA|R07~L5R3T8|S01~VVV000000001|S02~VVV000000002|S03~1|S04~00|S05~0|S06~0|S07~0|S08~20140611|S09~2|S10~5|S11~18|S12~99|S13~55|S14~YYZ|S15~00|B01~202|B02~PP";
      const barcodeSymbology = BarcodeSymbology.pdf417;
      const barcode = Barcode(rawData, barcodeSymbology);
      const expectedResult = BarcodeData(
        pinNumber: "VVV000000002",
        streetNumber: "5995",
      );

      // Act
      final result = BarcodeParser.decodeBarcode(barcode);

      // Assert
      expect(result, expectedResult);
    });
  });
}
