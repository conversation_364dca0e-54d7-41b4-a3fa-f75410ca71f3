import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:sort_pro_printer_flutter/services/logs/i_log_service.dart';
import 'package:sort_pro_printer_flutter/utils/pin_validator.dart';

class MockLogService extends Mock implements ILogService {
  @override
  void trace(LogLevel? logLevel, String? message,
      {Map<String, Object>? additionalProperties = const {}});
}

void main() {
  group('PIN Validator tests', () {
    final MockLogService logService = MockLogService();
    when(logService.trace(any, any));
    final PinValidator pinValidator = PinValidator(logService: logService);

    test('Should validate TRUE the passed PINS', () {
      // Pin 1
      var pin = "RTE002655436";
      var result = pinValidator.validatePin(pin);
      expect(result, true);

      // Pin 2
      pin = "334003183707";
      result = pinValidator.validatePin(pin);
      expect(result, true);

      // Pin 3
      pin = "334097633195";
      result = pinValidator.validatePin(pin);
      expect(result, true);

      // Pin 4
      pin = "334097633914";
      result = pinValidator.validatePin(pin);
      expect(result, true);

      // Pin 5
      pin = "MBG000712677";
      result = pinValidator.validatePin(pin);
      expect(result, true);

      // Pin 6
      pin = "608397243684";
      result = pinValidator.validatePin(pin);
      expect(result, true);

      // Pin 7
      pin = "BHJ004048769";
      result = pinValidator.validatePin(pin);
      expect(result, true);

      // Pin 8
      pin = "AWB718413513";
      result = pinValidator.validatePin(pin);
      expect(result, true);

      // Pin 9
      pin = "WMO000029713";
      result = pinValidator.validatePin(pin);
      expect(result, true);

      // Pin 10
      pin = "334109664907";
      result = pinValidator.validatePin(pin);
      expect(result, true);
    });
  });
}
