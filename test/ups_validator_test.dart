import 'package:flutter_test/flutter_test.dart';
import 'package:sort_pro_printer_flutter/utils/ups_validator.dart';

void main() {
  group('UPS Validator tests', () {
    test(
        'Should validate TRUE the passed raw data value from a UPS 1Z barcode',
        () {
      // Arrange
      const rawData = "1Z123X56A112345671";
      final result = UpsValidator.ups1zValidation(rawData);

      // Assert
      expect(result, true);
    });
    test(
        'Should validate FALSE the passed raw data value from a UPS 1Z barcode',
        () {
      // Arrange
      const rawData = "1Z123Z56A112345671";
      final result = UpsValidator.ups1zValidation(rawData);

      // Assert
      expect(result, false);
    });
  });
}
