import 'package:flutter_test/flutter_test.dart';
import 'package:sort_pro_printer_flutter/app/scanner/data/models/barcode.dart';

void main() {
  group('Barcode Type tests', () {

    /// Emp Badge
    test(
        'should return BarcodeType.employeeBadge for valid Employee Badge barcode',
            () {
          // Arrange
          const rawData = "60123456";
          const barcodeSymbology = BarcodeSymbology.code39;

          // Act
          const barcode = Barcode(rawData, barcodeSymbology);
          final result = barcode.barcodeType;

          // Assert
          expect(result, BarcodeType.employeeBadge);
        });

    /// Legacy Puro
    test('should return BarcodeType.legacyPurolator for valid Legacy Puro barcode', () {
      // Arrange
      const rawData = "VVV0000001";
      const barcodeSymbology = BarcodeSymbology.code39;

      // Act
      const barcode = Barcode(rawData, barcodeSymbology);
      final result = barcode.barcodeType;

      // Assert
      expect(result, BarcodeType.legacyPurolator);
    });

    /// PURO 2D
    test('should return BarcodeType.puro2d for valid puro2d barcode', () {
      // Arrange
      const rawData =
          "V01~A01|D01~T5J3S4|R01~ACME WIDGET SUPPLY|R02~SUITE 200|R03~5995|R04~AVEBURY DRIVE|R05~|R06~MISSISSAUGA|R07~L5R3T8|S01~VVV000000001|S02~VVV000000002|S03~1|S04~00|S05~0|S06~0|S07~0|S08~20140611|S09~2|S10~5|S11~18|S12~99|S13~55|S14~YYZ|S15~00|B01~202|B02~PP";
      const barcodeSymbology = BarcodeSymbology.pdf417;

      // Act
      const barcode = Barcode(rawData, barcodeSymbology);
      final result = barcode.barcodeType;

      // Assert
      expect(result, BarcodeType.puro2d);
    });

    /// NGB
    test('should return BarcodeType.ngb for valid NGB barcode data', () {
      // Arrange
      const rawData = "1820351082222220000000011280002010";
      const barcodeSymbology =
          BarcodeSymbology.code128; // symbology doesn't matter for this barcode

      // Act
      const barcode = Barcode(rawData, barcodeSymbology);
      final result = barcode.barcodeType;

      // Assert
      expect(result, BarcodeType.ngb);
    });

    /// Manual Way Bill
    test(
        'should return BarcodeType.manualWayBill for valid Manual Way Bill barcode',
        () {
      // Arrange
      const rawData = "A12345678901";
      const barcodeSymbology = BarcodeSymbology.code128;

      // Act
      const barcode = Barcode(rawData, barcodeSymbology);
      final result = barcode.barcodeType;

      // Assert
      expect(result, BarcodeType.manualWayBill);
    });

    /// UPS 1Z
    test('should return BarcodeType.ups1z for valid UPS 1Z barcode', () {
      // Arrange
      const rawData = "1Z123X56A112345671";
      const barcodeSymbology = BarcodeSymbology.unknown;

      // Act
      const barcode = Barcode(rawData, barcodeSymbology);
      final result = barcode.barcodeType;

      // Assert
      expect(result, BarcodeType.ups1z);
    });

    /// UPS Postal Code
    // todo This test fails due to weird logic in the method. Need to confirm with the SS team about this barcode.
    // test(
    //     'should return BarcodeType.upsPostalCode for valid UPS PostalCode barcode',
    //     () {
    //   // Arrange
    //   const rawData = "421300768845";
    //   const barcodeSymbology = BarcodeSymbology.code128;
    //
    //   // Act
    //   final barcode = Barcode(rawData, barcodeSymbology);
    //   final result = barcode.barcodeType;
    //
    //   // Assert
    //   expect(result, BarcodeType.upsPostalCode);
    // });

    /// UPS Manual Waybill
    test(
        'should return BarcodeType.upsManualWaybill for valid UPS Manual Waybill barcode',
            () {
          // Arrange
          const rawData = "M1234567891";
          const barcodeSymbology = BarcodeSymbology.code128;

          // Act
          const barcode = Barcode(rawData, barcodeSymbology);
          final result = barcode.barcodeType;

          // Assert
          expect(result, BarcodeType.upsManualWaybill);
        });

    /// UPS Maxicode
    test('should return BarcodeType.upsMaxicode for valid UPS Maxicode barcode', () {
      // Arrange
      final rawData = "12345${String.fromCharCode(29)}67890";
      const barcodeSymbology = BarcodeSymbology.code128;

      // Act
      final barcode = Barcode(rawData, barcodeSymbology);
      final result = barcode.barcodeType;

      // Assert
      expect(result, BarcodeType.upsMaxicode);
    });

    /// Invalid barcode
    test('should return BarcodeType.unknown for unknown barcode', () {
      // Arrange
      const rawData = "invalid barcode";
      const barcodeSymbology = BarcodeSymbology.unknown;

      // Act
      const barcode = Barcode(rawData, barcodeSymbology);
      final result = barcode.barcodeType;

      // Assert
      expect(result, BarcodeType.unknown);
    });
  });
}
