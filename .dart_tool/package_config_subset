_fe_analyzer_shared
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/_fe_analyzer_shared-67.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/_fe_analyzer_shared-67.0.0/lib/
analyzer
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/analyzer-6.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/analyzer-6.4.1/lib/
android_id
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/android_id-0.3.6/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/android_id-0.3.6/lib/
archive
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/
args
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/args-2.7.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/args-2.7.0/lib/
async
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/async-2.11.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/async-2.11.0/lib/
audioplayers
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers-5.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers-5.2.1/lib/
audioplayers_android
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_android-4.0.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_android-4.0.3/lib/
audioplayers_darwin
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_darwin-5.0.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_darwin-5.0.2/lib/
audioplayers_linux
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_linux-3.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_linux-3.1.0/lib/
audioplayers_platform_interface
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_platform_interface-6.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_platform_interface-6.1.0/lib/
audioplayers_web
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_web-4.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_web-4.1.0/lib/
audioplayers_windows
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_windows-3.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_windows-3.1.0/lib/
azblob
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/azblob-2.6.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/azblob-2.6.0/lib/
azure_application_insights
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/azure_application_insights-4.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/azure_application_insights-4.0.1/lib/
bloc
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/bloc-8.1.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/bloc-8.1.4/lib/
boolean_selector
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.1/lib/
build
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build-2.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build-2.4.1/lib/
build_config
2.14
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_config-1.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_config-1.1.1/lib/
build_daemon
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_daemon-4.0.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_daemon-4.0.2/lib/
build_resolvers
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_resolvers-2.4.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_resolvers-2.4.2/lib/
build_runner
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_runner-2.4.11/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_runner-2.4.11/lib/
build_runner_core
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_runner_core-7.3.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_runner_core-7.3.1/lib/
built_collection
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/built_collection-5.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/built_collection-5.1.1/lib/
built_value
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/built_value-8.10.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/built_value-8.10.1/lib/
camera
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/camera-0.10.6/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/camera-0.10.6/lib/
camera_android
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/camera_android-0.10.9+11/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/camera_android-0.10.9+11/lib/
camera_avfoundation
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.19/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.19/lib/
camera_platform_interface
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/camera_platform_interface-2.10.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/camera_platform_interface-2.10.0/lib/
camera_web
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/camera_web-0.3.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/camera_web-0.3.5/lib/
chalkdart
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/chalkdart-3.0.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/chalkdart-3.0.5/lib/
change_app_package_name
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/change_app_package_name-1.5.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/change_app_package_name-1.5.0/lib/
characters
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.3.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.3.0/lib/
checked_yaml
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/checked_yaml-2.0.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/checked_yaml-2.0.3/lib/
chewie
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/chewie-1.7.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/chewie-1.7.0/lib/
cli_config
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cli_config-0.2.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cli_config-0.2.0/lib/
cli_util
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cli_util-0.4.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cli_util-0.4.2/lib/
clock
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.1/lib/
code_builder
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/code_builder-4.10.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/code_builder-4.10.0/lib/
collection
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/
connectivity_plus
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-4.0.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-4.0.2/lib/
connectivity_plus_platform_interface
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus_platform_interface-1.2.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus_platform_interface-1.2.4/lib/
convert
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/convert-3.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/convert-3.1.2/lib/
coverage
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/coverage-1.14.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/coverage-1.14.1/lib/
cross_file
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/lib/
crypto
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/
csslib
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/lib/
csv
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/csv-6.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/csv-6.0.0/lib/
cupertino_icons
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/lib/
dart_style
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/dart_style-2.3.6/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/dart_style-2.3.6/lib/
dbus
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/
device_info_plus
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-9.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-9.1.2/lib/
device_info_plus_platform_interface
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus_platform_interface-7.0.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus_platform_interface-7.0.2/lib/
dio
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/
dio_web_adapter
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/dio_web_adapter-2.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/dio_web_adapter-2.1.1/lib/
equatable
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/equatable-2.0.7/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/equatable-2.0.7/lib/
fake_async
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.1/lib/
ffi
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.3/lib/
file
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/
fixnum
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/lib/
flutter_bloc
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_bloc-8.1.6/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_bloc-8.1.6/lib/
flutter_image_compress
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_image_compress-2.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_image_compress-2.4.0/lib/
flutter_image_compress_common
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/lib/
flutter_image_compress_macos
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_image_compress_macos-1.0.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_image_compress_macos-1.0.3/lib/
flutter_image_compress_ohos
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_image_compress_ohos-0.0.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_image_compress_ohos-0.0.3/lib/
flutter_image_compress_platform_interface
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_image_compress_platform_interface-1.0.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_image_compress_platform_interface-1.0.5/lib/
flutter_image_compress_web
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_image_compress_web-0.1.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_image_compress_web-0.1.5/lib/
flutter_isolate
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_isolate-2.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_isolate-2.1.0/lib/
flutter_keyboard_visibility
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_keyboard_visibility-5.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_keyboard_visibility-5.4.1/lib/
flutter_keyboard_visibility_linux
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_keyboard_visibility_linux-1.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_keyboard_visibility_linux-1.0.0/lib/
flutter_keyboard_visibility_macos
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_keyboard_visibility_macos-1.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_keyboard_visibility_macos-1.0.0/lib/
flutter_keyboard_visibility_platform_interface
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_keyboard_visibility_platform_interface-2.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_keyboard_visibility_platform_interface-2.0.0/lib/
flutter_keyboard_visibility_web
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_keyboard_visibility_web-2.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_keyboard_visibility_web-2.0.0/lib/
flutter_keyboard_visibility_windows
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_keyboard_visibility_windows-1.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_keyboard_visibility_windows-1.0.0/lib/
flutter_launcher_icons
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_launcher_icons-0.13.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_launcher_icons-0.13.1/lib/
flutter_lints
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_lints-2.0.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_lints-2.0.3/lib/
flutter_pdfview
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_pdfview-1.4.1+1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_pdfview-1.4.1+1/lib/
flutter_plugin_android_lifecycle
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.22/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.22/lib/
flutter_svg
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_svg-2.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_svg-2.1.0/lib/
frontend_server_client
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/frontend_server_client-4.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/frontend_server_client-4.0.0/lib/
ftpconnect
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/ftpconnect-2.0.7/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/ftpconnect-2.0.7/lib/
glob
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/glob-2.1.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/glob-2.1.3/lib/
google_fonts
2.14
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-4.0.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-4.0.5/lib/
google_mlkit_barcode_scanning
2.16
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_mlkit_barcode_scanning-0.6.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_mlkit_barcode_scanning-0.6.0/lib/
google_mlkit_commons
2.16
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_mlkit_commons-0.3.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_mlkit_commons-0.3.0/lib/
graphs
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/graphs-2.3.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/graphs-2.3.2/lib/
hive
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/
html
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/lib/
http
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/
http_multi_server
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/http_multi_server-3.2.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/http_multi_server-3.2.2/lib/
http_parser
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.0.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.0.2/lib/
hydrated_bloc
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/hydrated_bloc-9.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/hydrated_bloc-9.1.2/lib/
image
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/
intl
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/
io
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/io-1.0.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/io-1.0.5/lib/
js
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/js-0.6.7/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/js-0.6.7/lib/
json_annotation
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/
json_serializable
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/json_serializable-6.8.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/json_serializable-6.8.0/lib/
leak_tracker
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.4/lib/
leak_tracker_flutter_testing
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.3/lib/
leak_tracker_testing
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/lib/
lints
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/lints-2.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/lints-2.1.1/lib/
logging
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0/lib/
matcher
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.16+1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.16+1/lib/
material_color_utilities
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.8.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.8.0/lib/
material_symbols_icons
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/material_symbols_icons-4.2815.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/material_symbols_icons-4.2815.1/lib/
meta
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.12.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.12.0/lib/
mime
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/mime-1.0.6/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/mime-1.0.6/lib/
mockito
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/mockito-5.4.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/mockito-5.4.4/lib/
nested
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/nested-1.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/nested-1.0.0/lib/
nm
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/nm-0.5.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/nm-0.5.0/lib/
node_preamble
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/node_preamble-2.0.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/node_preamble-2.0.2/lib/
package_config
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/package_config-2.2.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/package_config-2.2.0/lib/
package_info_plus
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-4.2.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-4.2.0/lib/
package_info_plus_platform_interface
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus_platform_interface-2.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus_platform_interface-2.0.1/lib/
path
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.0/lib/
path_parsing
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_parsing-1.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_parsing-1.1.0/lib/
path_provider
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/lib/
path_provider_android
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.10/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.10/lib/
path_provider_foundation
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/lib/
path_provider_linux
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/
path_provider_platform_interface
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/
path_provider_windows
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/
permission_handler
2.15
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler-10.4.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler-10.4.5/lib/
permission_handler_android
2.15
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-10.3.6/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-10.3.6/lib/
permission_handler_apple
2.15
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.1.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.1.4/lib/
permission_handler_platform_interface
2.14
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-3.12.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-3.12.0/lib/
permission_handler_windows
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.1.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.1.3/lib/
petitparser
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/
platform
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/
plugin_platform_interface
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/lib/
pool
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/pool-1.5.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/pool-1.5.1/lib/
posix
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/posix-6.0.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/posix-6.0.3/lib/
provider
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/lib/
pub_semver
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/pub_semver-2.2.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/pub_semver-2.2.0/lib/
pubspec_parse
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/pubspec_parse-1.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/pubspec_parse-1.4.0/lib/
shared_preferences
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.3.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.3.3/lib/
shared_preferences_android
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.3.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.3.2/lib/
shared_preferences_foundation
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib/
shared_preferences_linux
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/lib/
shared_preferences_platform_interface
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/
shared_preferences_web
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/lib/
shared_preferences_windows
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/lib/
shelf
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shelf-1.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shelf-1.4.1/lib/
shelf_packages_handler
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shelf_packages_handler-3.0.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shelf_packages_handler-3.0.2/lib/
shelf_static
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shelf_static-1.1.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shelf_static-1.1.3/lib/
shelf_web_socket
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shelf_web_socket-1.0.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shelf_web_socket-1.0.4/lib/
source_gen
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_gen-1.5.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_gen-1.5.0/lib/
source_helper
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_helper-1.3.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_helper-1.3.5/lib/
source_map_stack_trace
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_map_stack_trace-2.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_map_stack_trace-2.1.2/lib/
source_maps
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_maps-0.10.13/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_maps-0.10.13/lib/
source_span
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.0/lib/
sqlite3
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqlite3-2.4.7/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqlite3-2.4.7/lib/
sqlite3_flutter_libs
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqlite3_flutter_libs-0.5.34/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqlite3_flutter_libs-0.5.34/lib/
stack_trace
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.11.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.11.1/lib/
stream_channel
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.2/lib/
stream_transform
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/lib/
string_scanner
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.2.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.2.0/lib/
synchronized
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.1.0+1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.1.0+1/lib/
term_glyph
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.1/lib/
test
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/test-1.25.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/test-1.25.2/lib/
test_api
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.0/lib/
test_core
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/test_core-0.6.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/test_core-0.6.0/lib/
timeago
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/
timing
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/timing-1.0.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/timing-1.0.2/lib/
typed_data
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.3.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.3.2/lib/
uuid
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/uuid-3.0.7/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/uuid-3.0.7/lib/
vector_graphics
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics-1.1.18/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics-1.1.18/lib/
vector_graphics_codec
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_codec-1.1.13/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_codec-1.1.13/lib/
vector_graphics_compiler
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib/
vector_math
2.14
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/
vibration
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vibration-1.9.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vibration-1.9.0/lib/
vibration_platform_interface
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vibration_platform_interface-0.0.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vibration_platform_interface-0.0.3/lib/
video_player
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/video_player-2.9.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/video_player-2.9.5/lib/
video_player_android
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/video_player_android-2.7.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/video_player_android-2.7.1/lib/
video_player_avfoundation
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/video_player_avfoundation-2.7.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/video_player_avfoundation-2.7.1/lib/
video_player_platform_interface
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/video_player_platform_interface-6.3.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/video_player_platform_interface-6.3.0/lib/
video_player_web
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/video_player_web-2.3.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/video_player_web-2.3.5/lib/
vm_service
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vm_service-14.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vm_service-14.2.1/lib/
wakelock_plus
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus-1.1.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus-1.1.4/lib/
wakelock_plus_platform_interface
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus_platform_interface-1.2.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus_platform_interface-1.2.3/lib/
watcher
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/watcher-1.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/watcher-1.1.2/lib/
web
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/
web_socket_channel
2.15
file:///Users/<USER>/.pub-cache/hosted/pub.dev/web_socket_channel-2.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/web_socket_channel-2.4.0/lib/
webkit_inspection_protocol
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/webkit_inspection_protocol-1.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/webkit_inspection_protocol-1.2.1/lib/
webview_flutter
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter-4.8.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter-4.8.0/lib/
webview_flutter_android
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_android-3.16.8/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_android-3.16.8/lib/
webview_flutter_platform_interface
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_platform_interface-2.13.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_platform_interface-2.13.0/lib/
webview_flutter_wkwebview
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.14.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.14.0/lib/
win32
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.5.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.5.4/lib/
win32_registry
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/win32_registry-1.1.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/win32_registry-1.1.5/lib/
workmanager
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/workmanager-0.5.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/workmanager-0.5.2/lib/
xdg_directories
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/lib/
xml
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/
yaml
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/yaml-3.1.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/yaml-3.1.3/lib/
sort_pro_printer_flutter
3.0
file:///Users/<USER>/Downloads/sortproprinter_flutter/
file:///Users/<USER>/Downloads/sortproprinter_flutter/lib/
appcenter
3.1
file:///Users/<USER>/Downloads/sortproprinter_flutter/packages/appcenter/
file:///Users/<USER>/Downloads/sortproprinter_flutter/packages/appcenter/lib/
sky_engine
3.2
file:///Users/<USER>/flutter/bin/cache/pkg/sky_engine/
file:///Users/<USER>/flutter/bin/cache/pkg/sky_engine/lib/
flutter
3.3
file:///Users/<USER>/flutter/packages/flutter/
file:///Users/<USER>/flutter/packages/flutter/lib/
flutter_localizations
3.2
file:///Users/<USER>/flutter/packages/flutter_localizations/
file:///Users/<USER>/flutter/packages/flutter_localizations/lib/
flutter_test
3.3
file:///Users/<USER>/flutter/packages/flutter_test/
file:///Users/<USER>/flutter/packages/flutter_test/lib/
flutter_web_plugins
3.2
file:///Users/<USER>/flutter/packages/flutter_web_plugins/
file:///Users/<USER>/flutter/packages/flutter_web_plugins/lib/
2
