# Introduction 
Sort Pro is a flutter project made to be used on Zebra TC58 devices in smart sort terminals.
The idea is that a Purolator employee scans a parcel and then:
  - App will determine where the parcel belongs (what route, shelf, side of belt..etc)
  - connect to Zebra printer
  - print yellow sticker

# Getting Started
Assuming you have flutter installed, you will need to add project configs, these currently look like:
--dart-define=ACS_API_KEY=some_key_here
--dart-define=ACS_API_URL=some_url_here
--dart-define=ACS_PURO_LABEL_MODEL_ID=some_modelId_here
--dart-define=SSLWS_URL=some_url_here
--dart-define=SSLWS_DOMAIN=some_domain_here
--dart-define=SSLWS_USER=some_username_here
--dart-define=SSLWS_PASS=some_pass_here
--dart-define=FTP_SS_HOST=some_host_here
--dart-define=FTP_SS_PORT=some_port_here
--dart-define=FTP_SS_USER=some_user_here
--dart-define=FTP_SS_PASS=some_pass_here
--dart-define=ASA_CONN_STRING='some_connection_string_here'
--dart-define=ASA_CONTAINER_NAME=some_container_name_here
--dart-define=APP_CENTER_KEY=some_key_here
--dart-define=FEEDBACK_FORM_LINK=some_link_here


# Build and Test
`flutter pub get` to get all the packages
`flutter gen-l10n` to generate localization files
`cd packages/appcenter && flutter pub get` to install extra packages
Click the run button in android studio or `flutter run` with the above run arguments^
To regenerate files containing `.g.dart`:
`flutter pub run build_runner build --delete-conflicting-outputs`

# Contribute
Feel free to message in the Sort Pro channel for help
Usually the process is to make a PR (pull request) from a git branch into dev
After that, create a PR from dev to main to get main up-to-date
Each PR requires at least 2 approvers


# Assets
In the assets folder you will find lots of pictures, videos..etc
As for videos, they were previously gifs then we realized they take up a lot of space.
In order to reduce the size:
 - grab the mp4 files (from sharepoint)
 - go to quicktime player > open file > choose mp4 file
 - click on File > Export > 480p (we dont need the videos to be ultra hd)
 - at this point you will have a .mov file, grab it and go to `cloudconvert.com`
 - convert mov to mp4, feel free to play around with settings, but for now we just set fps to 30
 - click export and download the file :)